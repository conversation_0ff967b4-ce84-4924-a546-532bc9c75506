/**
 * Utility functions for formatting values with locale support
 */

// Exchange rates (fixed values for demonstration)
// In a production app, these would be fetched from an API
export const EXCHANGE_RATES = {
  USD_TO_RMB: 7.2, // 1 USD = 7.2 RMB (example rate)
};

/**
 * Format a price with currency symbol based on locale
 *
 * @param {number} price - The price value
 * @param {string} locale - The locale code (e.g., 'en', 'zh')
 * @param {boolean} useRmbForChinese - Whether to convert and display in RMB for Chinese locale
 * @returns {string} Formatted price with currency symbol
 */
export const formatPrice = (price, locale = 'en', useRmbForChinese = true) => {
  if (price === null || price === undefined || isNaN(price)) {
    return '';
  }

  // For Chinese locale, convert to RMB if requested
  if (locale === 'zh' && useRmbForChinese) {
    const rmbPrice = price * EXCHANGE_RATES.USD_TO_RMB;
    return `¥${rmbPrice.toFixed(2)}`;
  }

  // Default to USD
  return `$${price.toFixed(2)}`;
};

/**
 * Format a price with currency symbol and unit
 *
 * @param {number} price - The price value
 * @param {string} unit - The unit (e.g., 'kg')
 * @param {string} locale - The locale code (e.g., 'en', 'zh')
 * @param {boolean} useRmbForChinese - Whether to convert and display in RMB for Chinese locale
 * @returns {string} Formatted price with currency symbol and unit
 */
export const formatPriceWithUnit = (price, unit = 'kg', locale = 'en', useRmbForChinese = true) => {
  const formattedPrice = formatPrice(price, locale, useRmbForChinese);

  if (!formattedPrice) {
    return '';
  }

  // For Chinese locale, adjust the unit display format
  if (locale === 'zh') {
    return `${formattedPrice}/${unit}`;
  }

  // Default format for other locales
  return `${formattedPrice}/${unit}`;
};

/**
 * Format a number with consistent decimal places
 *
 * @param {number} value - The number to format
 * @param {number} decimalPlaces - Number of decimal places (default: 2)
 * @returns {string} Formatted number with specified decimal places
 */
export const formatNumber = (value, decimalPlaces = 2) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '';
  }

  return Number(value).toFixed(decimalPlaces);
};

/**
 * Format a number with consistent decimal places and add a unit
 *
 * @param {number} value - The number to format
 * @param {string} unit - The unit to append (e.g., 'kg', '%')
 * @param {number} decimalPlaces - Number of decimal places (default: 2)
 * @returns {string} Formatted number with specified decimal places and unit
 */
export const formatNumberWithUnit = (value, unit = '', decimalPlaces = 2) => {
  const formattedValue = formatNumber(value, decimalPlaces);

  if (!formattedValue) {
    return '';
  }

  return unit ? `${formattedValue} ${unit}` : formattedValue;
};
