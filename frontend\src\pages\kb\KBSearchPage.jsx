import React, { useState, useEffect } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { useQuery, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'

import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Input from '../../components/ui/Input'
import KBArticleContent from '../../components/kb/KBArticleContent'
import { searchArticles } from '../../services/kbService'

/* -------------------------------------------------------------------------- */
/*                               Helper Views                                */
/* -------------------------------------------------------------------------- */

/**
 * Click‑to‑expand search result card.
 * Title link navigates to article; clicking elsewhere toggles expansion.
 * Collapsed content height ≈3 lines; expanded height capped & scrollable.
 */
const ArticlePreview = ({ article, showScore }) => {
  const { t } = useTranslation()
  const [expand, setExpand] = useState(false)
  const score = article.score ? Math.round(article.score * 100) : null

  const badgeColor = (s) => {
    if (!s) return 'bg-gray-100 text-gray-800'
    if (s >= 90) return 'bg-green-100 text-green-800'
    if (s >= 70) return 'bg-blue-100 text-blue-800'
    if (s >= 50) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  const contentStyle = expand
    ? { maxHeight: '14rem', overflowY: 'auto' }
    : { maxHeight: '4.5rem', overflow: 'hidden' }

  const handleCardClick = (e) => {
    if (e.target.closest('a')) return // preserve link clicks
    setExpand((prev) => !prev)
  }

  return (
    <Card
      className="border hover:border-blue-400 transition-colors cursor-pointer"
      onClick={handleCardClick}
    >
      {/* Title & score */}
      <div className="flex justify-between items-start gap-2">
        <Link
          to={`/kb/articles/${article.slug}`}
          onClick={(e) => e.stopPropagation()}
          className="text-lg md:text-xl font-semibold text-blue-600 hover:text-blue-800"
        >
          {article.title}
        </Link>
        {showScore && score && (
          <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${badgeColor(score)}`}>
            {score}% {t('kb.relevance')}
          </span>
        )}
      </div>

      {/* Summary */}
      {article.summary && (
        <KBArticleContent
          content={article.summary}
          style={contentStyle}
          className="mt-2 mb-3"
          showFade={!expand}
          isPreview={true}
        />
      )}

      {/* Meta */}
      <div className="flex flex-wrap items-center gap-2 text-xs text-gray-500 mt-2">
        {article.category_name && (
          <span className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded-full">
            {article.category_name}
          </span>
        )}
        {article.tags?.slice(0, 3).map((tag, i) => (
          <span key={i} className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
            {tag}
          </span>
        ))}
        <span className="ml-auto">
          {new Date(article.created_at).toLocaleDateString()}
        </span>
      </div>
    </Card>
  )
}

/* -------------------------------------------------------------------------- */
/*                               Main Page                                    */
/* -------------------------------------------------------------------------- */

const KBSearchPage = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()
  const params = new URLSearchParams(location.search)
  const queryClient = useQueryClient()

  const initialQuery = params.get('q') || ''
  const [query, setQuery] = useState(initialQuery)
  const [current, setCurrent] = useState(initialQuery)
  const [searchTimestamp, setSearchTimestamp] = useState(Date.now())
  const [mode, setMode] = useState(params.get('mode') === 'keyword' ? 'keyword' : 'semantic')

  /* --------------------------- Data fetching --------------------------- */
  const {
    data: results = [],
    isLoading,
    error,
    refetch
  } = useQuery(['kb-search', current, mode, searchTimestamp], () => searchArticles(current, 50, mode), {
    enabled: !!current,
    refetchOnWindowFocus: false
  })

  /* ----------------------------- Handlers ----------------------------- */
  const submitSearch = (e) => {
    e.preventDefault()
    if (query.trim()) {
      // If the query is the same as the current one, update the timestamp to force a refetch
      if (query.trim() === current) {
        setSearchTimestamp(Date.now())
      } else {
        setCurrent(query.trim())
      }
    }
  }

  useEffect(() => {
    if (current) {
      navigate({ pathname: location.pathname, search: `?q=${encodeURIComponent(current)}&mode=${mode}` }, { replace: true })
    } else {
      navigate({ pathname: location.pathname, search: '' }, { replace: true })
    }
  }, [current, mode])

  const toggleMode = () => setMode((prev) => (prev === 'semantic' ? 'keyword' : 'semantic'))
  const clear = () => {
    setQuery('')
    setCurrent('')
  }

  /* ------------------------------ Render ------------------------------ */
  return (
    <div className="space-y-10 max-w-5xl mx-auto">
      {/* Search bar */}
      <Card className="p-6 sticky top-0 bg-white/90 backdrop-blur z-10">
        <form onSubmit={submitSearch} className="flex flex-col md:flex-row gap-3 md:gap-0">
          <Input
            placeholder={t('kb.searchPlaceholder')}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="flex-grow rounded-r-none"
            noMargin
          />
          <Button type="submit" className="rounded-l-none h-10 md:w-40 shrink-0" disabled={isLoading || !query.trim()}>
            {isLoading ? t('common.searching') : t('common.search')}
          </Button>
        </form>
        <div className="flex items-center justify-between mt-3 text-sm">
          <button onClick={toggleMode} className={`px-3 py-1 rounded-full ${mode === 'semantic' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
            {mode === 'semantic' ? t('kb.semanticSearch') : t('kb.keywordSearch')}
          </button>
          {current && <button onClick={clear} className="text-gray-500 hover:text-gray-700">{t('common.reset')}</button>}
        </div>
      </Card>

      {/* Results */}
      {current ? (
        <section className="space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-semibold text-gray-900">{t('kb.searchResults')}: "{current}"</h1>
            {!isLoading && !error && <span className="text-sm text-gray-500">{results.length} {t('kb.resultsFound')}</span>}
          </div>

          {isLoading ? (
            <div className="flex justify-center py-16">
              <div className="animate-spin h-10 w-10 border-2 border-green-500 border-t-transparent rounded-full" />
            </div>
          ) : error ? (
            <Card className="bg-red-50 text-center">
              <p className="text-red-700 py-6">{t('kb.searchError')}</p>
              <Button variant="outline" size="sm" onClick={refetch}>{t('common.tryAgain')}</Button>
            </Card>
          ) : results.length === 0 ? (
            <Card className="bg-gray-50 text-center py-8">
              <p className="text-gray-600 mb-2">{t('kb.noSearchResults')}</p>
              <Link to="/kb/ask"><Button variant="outline" size="sm">{t('kb.askQuestion')}</Button></Link>
            </Card>
          ) : (
            <div className="grid gap-4">
              {mode === 'semantic' && (
                <div className="bg-blue-50 text-blue-800 p-3 rounded-lg text-sm">
                  <p className="font-medium">{t('kb.semanticSearchInfo')}</p>
                  <p>{t('kb.semanticSearchDetail')}</p>
                </div>
              )}
              {results.map((a, i) => <ArticlePreview key={a.id || i} article={a} showScore />)}
            </div>
          )}
        </section>
      ) : (
        <Card className="text-center py-16">
          <h2 className="text-xl font-bold mb-2">{t('kb.startSearchTitle')}</h2>
          <p className="text-gray-600 max-w-md mx-auto">{t('kb.startSearchHint')}</p>
        </Card>
      )}

      {/* Footer buttons */}
      <div className="flex justify-between mt-10">
        <Button variant="outline" onClick={() => navigate(-1)}>{t('common.back')}</Button>
        <Link to="/kb/ask"><Button>{t('kb.askQuestion')}</Button></Link>
      </div>
    </div>
  )
}

export default KBSearchPage
