{"name": "dairy-ration-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.1", "@tailwindcss/forms": "^0.5.10", "axios": "^1.6.7", "date-fns": "^4.1.0", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "lucide-react": "^0.488.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-query": "^3.39.3", "react-router-dom": "^6.22.1", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^11.0.5", "recharts": "^2.12.0", "remark-gfm": "^4.0.1", "zustand": "^4.5.0"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^5.1.0"}}