# my_project/backend/app/services/db_schema_service.py

from flask import current_app
# Import all relevant models from your application structure
from ..models import User, Feed, Nutrient, FeedNutrient, Herd, AnimalGroup, Ration, RationFeed, Constraint

# List of models that are likely targets for direct Excel import
# Adjust this list based on which data types you expect users to import via Excel
IMPORTABLE_MODELS = [Feed, Herd, FeedNutrient, Ration, RationFeed] # Example: Assume these are primary import targets

def get_formatted_schema_for_llm(target_models=IMPORTABLE_MODELS):
    """
    Introspects SQLAlchemy models and returns a formatted string description
    of their relevant columns for the LLM prompt.

    Args:
        target_models (list): A list of SQLAlchemy model classes to describe.
                              Defaults to IMPORTABLE_MODELS.

    Returns:
        str: A formatted string describing the relevant schema.
    """
    schema_description = "Target Database Schema Information:\n"
    schema_description += "---------------------------------\n"
    table_names = []
    try:
        for model in target_models:
            if not hasattr(model, '__tablename__') or not hasattr(model, '__table__'):
                current_app.logger.warning(f"Model {model.__name__} lacks necessary SQLAlchemy attributes.")
                continue

            table_name = model.__tablename__
            table_names.append(f"'{table_name}'") # Collect names for the prompt instructions
            columns_info = []

            # Iterate through the columns defined in the SQLAlchemy model
            for column in model.__table__.columns:
                col_name = column.name
                # Exclude non-relevant columns
                col_type = str(column.type)
                columns_info.append(f"{col_name} ({col_type})")

            if columns_info:
                 schema_description += f"Table: '{table_name}' Relevant Columns for Mapping: [{', '.join(columns_info)}]\n"
            else:
                 schema_description += f"Table: '{table_name}' (No columns identified for direct mapping)\n"

        schema_description += "---------------------------------\n"
        schema_description += f"Note: The targetTable in the plan must be one of {', '.join(table_names)}.\n"

    except Exception as e:
        current_app.logger.error(f"Error introspecting schema: {e}", exc_info=True)
        # Provide a fallback message in the prompt context
        schema_description += "Error: Could not retrieve detailed database schema information.\n"

    return schema_description

def get_nutrients_data():
    """
    Query the nutrients table and return a structured representation of the data.
    
    Returns:
        dict: A dictionary of nutrient data {nutrient_id: {name_en, name_zh, unit}, ...}
    """
    from flask import current_app
    from ..models import Nutrient
    
    try:
        # Create a structured representation of nutrients data
        result = {}
        
        # Get all nutrients
        nutrients = Nutrient.query.all()
        for nutrient in nutrients:
            result[nutrient.id] = {
                'name_en': nutrient.name_en,
                'name_zh': nutrient.name_zh,
                'unit': nutrient.unit
            }
            
        current_app.logger.info(f"Fetched {len(result)} nutrients from database")
        return result
    
    except Exception as e:
        current_app.logger.error(f"Error fetching nutrients data: {str(e)}")
        return {'error': str(e)}
def get_importable_models():
    return IMPORTABLE_MODELS