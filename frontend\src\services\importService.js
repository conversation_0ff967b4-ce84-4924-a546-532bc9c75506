import api from './api'; // Uses the configured axios instance

/**
 * Analyzes the Excel payload and stages the data directly into target tables.
 * @param {object} excelPayload - The raw payload from the Excel add-in taskpane.ts.
 * @returns {Promise<object>} - Promise resolving to { success, sessionId, recordType, message, reviewUrl }
 */
export const analyzeAndStageData = async (excelPayload) => {
  if (!excelPayload) {
    throw new Error('Excel payload is required.');
  }
  try {
    // Call the new backend endpoint
    const response = await api.post('/analyze_and_stage', excelPayload);
    // Expecting { success: true/false, sessionId, recordType, message, reviewUrl }
    if (response.data && response.data.success) {
      return response.data;
    } else {
      // Throw error with backend message if available 
      throw new Error(response.data?.message || 'Failed to analyze and stage data.');
    }
  } catch (error) {
    console.error("Error analyzing and staging data:", error);
    // Rethrow error potentially augmented by backend response
    throw error.response?.data || error;
  }
};

/**
 * Fetches pending import data for review from the target table.
 * @param {string} sessionId - The ID of the import session.
 * @param {string} recordType - The type of record being imported (e.g., 'Feed', 'Herd').
 * @param {number} [page=1] - The page number to fetch.
 * @param {number} [limit=100] - The number of records per page.
 * @returns {Promise<object>} - Promise resolving to the API response containing paginated data.
 */
export const getPendingImportData = async (sessionId, recordType, page = 1, limit = 100) => {
  if (!sessionId) throw new Error('Session ID is required.');
  if (!recordType) throw new Error('Record Type is required.');

  try {
    // Call the new backend endpoint, passing recordType as a query parameter
    const response = await api.get(`/pending_import_data/${sessionId}`, {
      params: { recordType, page, limit }
    });
    // Expecting { success: true, data: [...], page, totalPages, etc. }
    if (response.data && response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data?.message || 'Failed to fetch pending import data.');
    }
  } catch (error) {
    console.error("Error fetching pending import data:", error);
    throw error.response?.data || error;
  }
};

/**
 * Gets all record types that have data for a given import session.
 * @param {string} sessionId - The ID of the import session.
 * @returns {Promise<object>} - Promise resolving to { success, sessionId, recordTypes }
 */
export const getImportSessionTypes = async (sessionId) => {
  if (!sessionId) throw new Error('Session ID is required.');

  try {
    const response = await api.get(`/import_session_types/${sessionId}`);
    if (response.data && response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data?.message || 'Failed to fetch session types.');
    }
  } catch (error) {
    console.error("Error fetching session types:", error);
    throw error.response?.data || error;
  }
};

/**
 * Confirms a pending import session.
 * @param {string} sessionId - The ID of the import session.
 * @param {string} recordType - The type of record being confirmed.
 * @param {string} mode - The confirmation mode ('replace' or 'upsert').
 * @returns {Promise<object>} - Promise resolving to { success, message }
 */
export const confirmImport = async (sessionId, recordType, mode) => {
  if (!sessionId) throw new Error('Session ID is required.');
  if (!recordType) throw new Error('Record Type is required.');
  if (!mode) throw new Error('Confirmation mode is required.');

  try {
    // Call the new backend endpoint
    const response = await api.post(`/confirm_import/${sessionId}`, { recordType, mode });
     if (response.data && response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data?.message || 'Failed to confirm import.');
    }
  } catch (error) {
    console.error("Error confirming import:", error);
    throw error.response?.data || error;
  }
};

/**
 * Discards a pending import session.
 * @param {string} sessionId - The ID of the import session.
 * @param {string} recordType - The type of record being discarded.
 * @returns {Promise<object>} - Promise resolving to { success, message }
 */
export const discardImport = async (sessionId, recordType) => {
   if (!sessionId) throw new Error('Session ID is required.');
   if (!recordType) throw new Error('Record Type is required.');

  try {
    // Call the new backend endpoint, passing recordType as a query parameter
    const response = await api.delete(`/discard_import/${sessionId}`, {
        params: { recordType }
    });
     if (response.data && response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data?.message || 'Failed to discard import.');
    }
  } catch (error) {
     console.error("Error discarding import:", error);
    throw error.response?.data || error;
  }
};