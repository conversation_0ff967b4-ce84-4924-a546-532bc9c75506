from datetime import datetime
from ..extensions import db

class Herd(db.Model):
    __tablename__ = 'herds'
    __table_args__ = {'schema': 'ration_app'}

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>Key('ration_app.users.id'))
    animal_group_id = db.Column(db.Integer, db.ForeignKey('ration_app.animal_groups.id'))
    name = db.Column(db.String(255), nullable=True)
    description = db.Column(db.Text)

    # New extended fields
    ear_num = db.Column(db.String(255))
    gender = db.Column(db.String(50))
    birth_weight = db.Column(db.Float)
    breed = db.Column(db.String(255))
    color = db.Column(db.String(255))
    father_ear_num = db.Column(db.String(255))
    mother_ear_num = db.Column(db.String(255))
    entry_date = db.Column(db.DateTime)
    entry_type = db.Column(db.String(255))
    group_name = db.Column(db.String(255))
    lactation_number = db.Column(db.Integer)
    month_age = db.Column(db.Float)
    grow_status = db.Column(db.String(255))
    fertility_status = db.Column(db.String(255))
    high = db.Column(db.String(50))
    bust = db.Column(db.String(50))
    measure_date = db.Column(db.DateTime)
    birth_date = db.Column(db.DateTime)
    is_on_farm = db.Column(db.Integer)
    exit_date = db.Column(db.DateTime)
    exit_type = db.Column(db.String(255))
    exit_reason = db.Column(db.String(255))
    calving_date = db.Column(db.DateTime)
    cure_date = db.Column(db.DateTime)
    abortion_date = db.Column(db.DateTime)
    insem_date = db.Column(db.DateTime)
    curedate = db.Column(db.String(255))
    pd1_date = db.Column(db.String(255))
    pd2_date = db.Column(db.String(255))
    disease_date = db.Column(db.String(255))
    health_status = db.Column(db.String(255))
    disease_name = db.Column(db.String(255))
    create_time = db.Column(db.String(255))
    remark = db.Column(db.String(255))
    dry_date = db.Column(db.DateTime)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    import_status = db.Column(db.String(50), nullable=True, default='confirmed', index=True)
    import_session_id = db.Column(db.String(36), index=True)


    # Relationships
    animal_group = db.relationship('AnimalGroup', backref='herds', lazy=True, foreign_keys=[animal_group_id])

    def to_dict(self, include_groups=False):
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'animal_group_id': self.animal_group_id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,

            # Extended fields
            'ear_num': self.ear_num,
            'gender': self.gender,
            'birth_weight': self.birth_weight,
            'breed': self.breed,
            'color': self.color,
            'father_ear_num': self.father_ear_num,
            'mother_ear_num': self.mother_ear_num,
            'entry_date': self.entry_date.isoformat() if self.entry_date else None,
            'entry_type': self.entry_type,
            'group_name': self.group_name,
            'lactation_number': self.lactation_number,
            'month_age': self.month_age,
            'grow_status': self.grow_status,
            'fertility_status': self.fertility_status,
            'high': self.high,
            'bust': self.bust,
            'measure_date': self.measure_date.isoformat() if self.measure_date else None,
            'birth_date': self.birth_date.isoformat() if self.birth_date else None,
            'is_on_farm': self.is_on_farm,
            'exit_date': self.exit_date.isoformat() if self.exit_date else None,
            'exit_type': self.exit_type,
            'exit_reason': self.exit_reason,
            'calving_date': self.calving_date.isoformat() if self.calving_date else None,
            'cure_date': self.cure_date.isoformat() if self.cure_date else None,
            'abortion_date': self.abortion_date.isoformat() if self.abortion_date else None,
            'insem_date': self.insem_date.isoformat() if self.insem_date else None,
            'curedate': self.curedate,
            'pd1_date': self.pd1_date,
            'pd2_date': self.pd2_date,
            'disease_date': self.disease_date,
            'health_status': self.health_status,
            'disease_name': self.disease_name,
            'create_time': self.create_time,
            'remark': self.remark,
            'dry_date': self.dry_date.isoformat() if self.dry_date else None,
            'import_status': self.import_status,
            'import_session_id': self.import_session_id
        }

        if include_groups and self.animal_group:
            data['animal_group'] = self.animal_group.to_dict()

        return data

class AnimalGroup(db.Model):
    __tablename__ = 'animal_groups'
    __table_args__ = {'schema': 'ration_app'}

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)  # This will store the group_name
    user_id = db.Column(db.Integer, db.ForeignKey('ration_app.users.id'))
    description = db.Column(db.Text)
    weight_kg = db.Column(db.Float)
    bcs = db.Column(db.Float)
    milk_production_kg = db.Column(db.Float)
    days_in_milk = db.Column(db.Integer)
    lactation_number = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    rations = db.relationship('Ration', backref='animal_group', lazy=True)

    def to_dict(self, include_herds=False):
        data = {
            'id': self.id,
            'name': self.name,
            'user_id': self.user_id,
            'description': self.description,
            'weight_kg': self.weight_kg,
            'bcs' : self.bcs,
            'milk_production_kg': self.milk_production_kg,
            'days_in_milk': self.days_in_milk,
            'lactation_number': self.lactation_number,
            'created_at': self.created_at.isoformat(),
            'herd_count': len(self.herds) if self.herds else 0
        }

        if include_herds:
            data['herds'] = [herd.to_dict() for herd in self.herds]

        return data