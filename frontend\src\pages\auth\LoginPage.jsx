import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import useAuthStore from '../../store/authStore'
import Button from '../../components/ui/Button'
import Input from '../../components/ui/Input'

const LoginPage = () => {
  const { t } = useTranslation()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [formErrors, setFormErrors] = useState({})
  const { login, error, clearError } = useAuthStore()
  const navigate = useNavigate()

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    // Clear field error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }

    // Clear global error
    if (error) {
      clearError()
    }
  }

  const validate = () => {
    const errors = {}

    if (!formData.email) {
      errors.email = t('validation.emailRequired')
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = t('validation.emailInvalid')
    }

    if (!formData.password) {
      errors.password = t('validation.passwordRequired')
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validate()) {
      return
    }

    try {
      console.log('Submitting login form...');
      const result = await login(formData)
      console.log('Login successful, token received');

      // Add a small delay to ensure token is stored before navigation
      setTimeout(() => {
        console.log('Navigating to dashboard, token in storage:', !!localStorage.getItem('token'));
        navigate('/');
      }, 300);
    } catch (err) {
      // Error is handled by the auth store
      console.error('Login failed:', err)
    }
  }

  return (
    <div>
      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <Input
          id="email"
          name="email"
          type="email"
          label={t('auth.email')}
          value={formData.email}
          onChange={handleChange}
          error={formErrors.email}
          required
          autoComplete="email"
        />

        <Input
          id="password"
          name="password"
          type="password"
          label={t('auth.password')}
          value={formData.password}
          onChange={handleChange}
          error={formErrors.password}
          required
          autoComplete="current-password"
        />

        <div>
          <Button type="submit" fullWidth>
            {t('auth.signIn')}
          </Button>
        </div>
      </form>

      <div className="mt-6">
        <div className="text-center text-sm">
          <span className="text-gray-600">{t('auth.noAccount')}</span>
          <Link to="/register" className="ml-1 font-medium text-green-600 hover:text-green-500">
            {t('auth.signUp')}
          </Link>
        </div>
      </div>
    </div>
  )
}

export default LoginPage