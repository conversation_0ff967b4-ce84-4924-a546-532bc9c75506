import { create } from 'zustand'
import { login as loginService, logout as logoutService, getCurrentUser, register as registerService } from '../services/authService'

const useAuthStore = create((set, get) => ({
  user: null,
  isAuthenticated: false,
  loading: true,
  error: null,
  
  // Check if user is authenticated
  checkAuth: async () => {
    set({ loading: true, error: null })
    
    try {
      // Try to get user from localStorage first
      const storedUser = localStorage.getItem('user')
      
      if (storedUser) {
        const user = JSON.parse(storedUser)
        set({ user, isAuthenticated: true, loading: false })
        return true
      }
      
      // If no stored user but token exists, fetch user
      const token = localStorage.getItem('token')
      
      if (token) {
        const user = await getCurrentUser()
        
        if (user) {
          localStorage.setItem('user', JSON.stringify(user))
          set({ user, isAuthenticated: true, loading: false })
          return true
        } else {
          // Clear invalid token
          localStorage.removeItem('token')
          localStorage.removeItem('refresh_token')
          localStorage.removeItem('user')
          set({ user: null, isAuthenticated: false, loading: false })
          return false
        }
      } else {
        set({ user: null, isAuthenticated: false, loading: false })
        return false
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      set({ user: null, isAuthenticated: false, loading: false, error: error.message })
      return false
    }
  },
  
  // Register a new user
  register: async (userData) => {
    set({ loading: true, error: null })
    
    try {
      const result = await registerService(userData)
      set({ loading: false })
      return result
    } catch (error) {
      console.error('Registration failed:', error)
      set({ 
        loading: false, 
        error: error.response?.data?.message || 'Registration failed. Please try again.' 
      })
      throw error
    }
  },
  
  // Login user
  login: async (credentials) => {
    set({ loading: true, error: null })
    
    try {
      const result = await loginService(credentials)
      // Make sure we properly set isAuthenticated to true
      set({ 
        user: result.user, 
        isAuthenticated: true, 
        loading: false 
      })
      
      // Manually verify the token was stored
      const storedToken = localStorage.getItem('token')
      console.log('After login - Token stored:', !!storedToken)
      
      return result
    } catch (error) {
      console.error('Login failed:', error)
      set({ 
        user: null, 
        isAuthenticated: false, 
        loading: false, 
        error: error.response?.data?.message || 'Login failed. Please check your credentials.' 
      })
      throw error
    }
  },
  
  // Logout user
  logout: () => {
    logoutService()
    set({ user: null, isAuthenticated: false, error: null })
  },
  
  // Clear error
  clearError: () => set({ error: null })
}))

export default useAuthStore