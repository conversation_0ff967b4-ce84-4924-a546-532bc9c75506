# cli.py
import click
from flask import Blueprint, current_app
from flask.cli import with_appcontext
import logging
import asyncio  # Import asyncio
import sys      # Import sys
from sqlalchemy import text
from .extensions import db

logger = logging.getLogger(__name__)
bp = Blueprint('procrastinate_worker', __name__, cli_group=None)

@bp.cli.command('run-worker')
@click.option('--queues', '-q', help="Comma-separated list of queues to process (default: all)")
@click.option('--wait/--no-wait', default=True, help="Wait for jobs if none are available (default: wait)")
@click.option('--listen-notify/--no-listen-notify', default=True, help="Use LISTEN/NOTIFY (default: listen)")
@with_appcontext # Ensures Flask app context is available
def run_worker(queues, wait, listen_notify):
    """Runs the Procrastinate worker."""

    # --- Add asyncio policy fix for Windows ---
    # This needs to run before the asyncio loop is started by run_worker
    if sys.platform == 'win32':
        logger.info("Platform is Windows, applying WindowsSelectorEventLoopPolicy for asyncio.")
        try:
            # Dynamically import to avoid ImportError on non-Windows
            from asyncio import WindowsSelectorEventLoopPolicy
            # Set the policy
            asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())
            logger.info("Successfully applied WindowsSelectorEventLoopPolicy.")
        except ImportError:
            # Should not happen on modern Python on Windows, but good practice
            logger.warning("WindowsSelectorEventLoopPolicy not found. Psycopg async operations might fail.")
        except Exception as policy_exc:
            # Catch any unexpected errors during policy setting
            logger.error(f"Error applying WindowsSelectorEventLoopPolicy: {policy_exc}", exc_info=True)
            # Depending on severity, you might want to exit here
            # sys.exit(1)
    # --- End fix ---

    logger.info("Attempting to start Procrastinate worker...")
    try:
        # The Flask app context provides the necessary config
        # get_procrastinate_app retrieves the initialized app
        procrastinate_app = current_app.procrastinate

        queue_list = queues.split(',') if queues else None
        logger.info(f"Worker starting with queues: {queue_list or 'all'}, wait: {wait}, listen_notify: {listen_notify}")

        # Run the worker (this will start the asyncio loop internally)
        procrastinate_app.run_worker(
            queues=queue_list,
            wait=wait,
            listen_notify=listen_notify
        )
        logger.info("Procrastinate worker finished.") # Likely only seen if wait=False or on clean exit

    except Exception as e:
        logger.exception(f"Error running Procrastinate worker: {e}", exc_info=True)
        # Exit with error status
        # import sys # Already imported above
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Procrastinate worker stopped by user.")

@bp.cli.command('apply-schema')
@with_appcontext
def apply_schema():
    """Applies the Procrastinate database schema."""
    logger.info("Attempting to apply Procrastinate schema...")
    try:
        procrastinate_app = current_app.procrastinate
        if procrastinate_app is None:
             raise RuntimeError("Procrastinate instance not found on current_app.")

        # Use the synchronous context manager 'open()' for schema operations
        with procrastinate_app.open() as sync_connector:
             logger.info("Applying schema migrations...")
             procrastinate_app.schema_manager.apply_schema()
             logger.info("Procrastinate schema applied successfully (or already exists).")

    except Exception as e:
        logger.exception(f"Error applying Procrastinate schema: {e}", exc_info=True)
        sys.exit(1) # Exit with error status
    logger.info("Schema application command finished.")


@bp.cli.command('create-kb-index')
@click.option('--metric', default='l2', help='Distance metric (l2, ip, cosine).')
@click.option('--m', default=16, help='HNSW M parameter.')
@click.option('--ef', default=64, help='HNSW ef_construction parameter.')
@with_appcontext
def create_kb_index(metric, m, ef):
    """Creates the HNSW index on the kb_articles_chunks embedding_vector column."""
    logger.info(f"Attempting to create HNSW index on kb_articles.embedding_vector using {metric} distance...")

    if metric not in ['l2', 'ip', 'cosine']:
        logger.error(f"Invalid metric: {metric}. Must be 'l2', 'ip', or 'cosine'.")
        sys.exit(1)

    op_class = f"vector_{metric}_ops"
    index_name = f'ix_kb_articles_embedding_{metric}_hnsw'

    logger.info(f"Index Name: {index_name}, Op Class: {op_class}, M: {m}, EF: {ef}")

    # Define the index using SQLAlchemy Index construct
    # Note: Direct HNSW parameters might require specific SQLAlchemy/dialect versions
    # Using postgresql_with for parameters is common
    try:
        # Ensure the schema context is correct if using one
        # Option 1: Define index directly (might need specific dialect support for HNSW params directly)
        # index = Index(
        #     index_name,
        #     KBArticle.embedding_vector,
        #     postgresql_using='hnsw',
        #     postgresql_ops={'embedding_vector': op_class},
        #     postgresql_with={'m': m, 'ef_construction': ef},
        #     _table_args={'schema': 'ration_app'} # Apply schema if needed
        # )

        # Option 2: Execute raw SQL (often more reliable for custom index types/params)
        # Ensure schema is correctly handled in the SQL string
        index_sql = text(f"""
            CREATE INDEX IF NOT EXISTS {index_name}
            ON ration_app.kb_article_chunks -- Use explicit schema
            USING hnsw (embedding_vector {op_class})
            WITH (m = {int(m)}, ef_construction = {int(ef)});
        """)

        logger.info(f"Executing SQL: {index_sql}")
        with db.engine.connect() as connection:
             # DDL statements often require execution outside a transaction or specific handling
             # Using execute() directly on connection might be better
             trans = connection.begin() # Start transaction for DDL context if needed by driver/db
             try:
                 connection.execute(index_sql)
                 trans.commit() # Commit the transaction
                 logger.info(f"Successfully created or ensured HNSW index '{index_name}' exists.")
             except Exception as e_inner:
                 trans.rollback() # Rollback on error
                 logger.error(f"Error during index creation execution: {e_inner}", exc_info=True)
                 sys.exit(1)

        # If using Option 1 (SQLAlchemy Index object):
        # try:
        #     index.create(bind=db.engine)
        #     logger.info(f"Successfully created HNSW index '{index_name}'.")
        # except Exception as e_create:
        #     # Catch errors like index already exists, etc.
        #     logger.warning(f"Could not create index '{index_name}' (it might already exist): {e_create}")
        #     # You might want to check the specific error type here

    except Exception as e:
        logger.exception(f"Error creating HNSW index: {e}", exc_info=True)
        sys.exit(1)

    logger.info("KB index creation command finished.")


@bp.cli.command('init-nrc-coefficients')
@click.option('--force', is_flag=True, help='Force re-initialization even if data exists')
@with_appcontext
def init_nrc_coefficients(force):
    """Initialize NRC 8th edition coefficients in the database."""
    from .models import NrcModelVersion, NrcCoefficientGroup, NrcCoefficient
    from datetime import datetime
    import json
    import os

    logger.info("Initializing NRC 8th edition coefficients...")

    # Check if data already exists
    if not force and NrcModelVersion.query.count() > 0:
        logger.info("NRC coefficients already exist. Use --force to reinitialize.")
        return

    try:
        # Create NRC 8th edition model version
        model = NrcModelVersion(
            name="NRC 8th Edition (NASEM 2021)",
            description="Nutrient Requirements of Dairy Cattle, 8th Revised Edition (NASEM, 2021)",
            publication_date=datetime(2021, 1, 1),  # Approximate date
            is_active=True
        )
        db.session.add(model)
        db.session.flush()  # Get the model ID

        # Define coefficient groups and their coefficients
        groups = [
            {
                "name": "Dry Matter Intake",
                "description": "Coefficients for dry matter intake equations",
                "coefficients": [
                    {
                        "name": "Multiparous Base Coefficient",
                        "code": "dmi_multi_base",
                        "value": 0.14,
                        "unit": "kg/kg^0.75",
                        "description": "Base coefficient for multiparous cow DMI equation",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Milk Production Coefficient",
                        "code": "dmi_milk_coef",
                        "value": 0.15,
                        "unit": "kg/kg",
                        "description": "Coefficient for milk production in DMI equation",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Primiparous Adjustment Factor",
                        "code": "dmi_prim_factor",
                        "value": 0.88,
                        "unit": "",
                        "description": "Adjustment factor for primiparous cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Early Lactation Base",
                        "code": "dmi_early_base",
                        "value": 1.7,
                        "unit": "",
                        "description": "Base value for early lactation adjustment",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Early Lactation Exponent",
                        "code": "dmi_early_exp",
                        "value": -0.07,
                        "unit": "1/day",
                        "description": "Exponent for early lactation adjustment",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Dry Cow Coefficient",
                        "code": "dmi_dry_coef",
                        "value": 0.022,
                        "unit": "kg/kg",
                        "description": "Coefficient for dry cow DMI equation",
                        "animal_type": "dry"
                    },
                    {
                        "name": "Heifer Weight Coefficient",
                        "code": "dmi_heifer_weight_coef",
                        "value": 0.0185,
                        "unit": "kg/kg",
                        "description": "Weight coefficient for heifer DMI equation",
                        "animal_type": "heifer"
                    },
                    {
                        "name": "Heifer Metabolic Weight Coefficient",
                        "code": "dmi_heifer_mw_coef",
                        "value": 0.2,
                        "unit": "kg/kg^0.75",
                        "description": "Metabolic weight coefficient for heifer DMI equation",
                        "animal_type": "heifer"
                    }
                ]
            },
            {
                "name": "Energy Requirements",
                "description": "Coefficients for energy requirement equations",
                "coefficients": [
                    {
                        "name": "Maintenance Energy Coefficient",
                        "code": "nel_maint_coef",
                        "value": 0.086,
                        "unit": "Mcal/kg^0.75",
                        "description": "Coefficient for maintenance energy requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Milk Fat Energy Coefficient",
                        "code": "nel_milk_fat_coef",
                        "value": 0.0929,
                        "unit": "Mcal/kg",
                        "description": "Energy coefficient for milk fat",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Milk Protein Energy Coefficient",
                        "code": "nel_milk_protein_coef",
                        "value": 0.0547,
                        "unit": "Mcal/kg",
                        "description": "Energy coefficient for milk protein",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Milk Lactose Energy Coefficient",
                        "code": "nel_milk_lactose_coef",
                        "value": 0.0395,
                        "unit": "Mcal/kg",
                        "description": "Energy coefficient for milk lactose",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Default Lactose Percentage",
                        "code": "default_lactose_percent",
                        "value": 4.85,
                        "unit": "%",
                        "description": "Default lactose percentage in milk",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Pregnancy Energy Coefficient",
                        "code": "nel_preg_coef",
                        "value": 0.00318,
                        "unit": "Mcal/kg",
                        "description": "Energy coefficient for pregnancy",
                        "animal_type": "pregnant"
                    },
                    {
                        "name": "Pregnancy Energy Exponent",
                        "code": "nel_preg_exp",
                        "value": 0.0352,
                        "unit": "1/day",
                        "description": "Exponent for pregnancy energy equation",
                        "animal_type": "pregnant"
                    },
                    {
                        "name": "Growth Energy Factor",
                        "code": "nel_growth_factor",
                        "value": 0.1,
                        "unit": "",
                        "description": "Factor of maintenance energy for growth in first lactation",
                        "animal_type": "lactating"
                    }
                ]
            },
            {
                "name": "Protein Requirements",
                "description": "Coefficients for protein requirement equations",
                "coefficients": [
                    {
                        "name": "Maintenance MP Coefficient 1",
                        "code": "mp_maint_coef1",
                        "value": 4.1,
                        "unit": "g/kg^0.5",
                        "description": "First coefficient for maintenance MP requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Maintenance MP Coefficient 2",
                        "code": "mp_maint_coef2",
                        "value": 0.3,
                        "unit": "g/kg",
                        "description": "Second coefficient for maintenance MP requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Maintenance MP Constant",
                        "code": "mp_maint_const",
                        "value": 30,
                        "unit": "g/day",
                        "description": "Constant term for maintenance MP requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Milk Protein Efficiency",
                        "code": "mp_milk_efficiency",
                        "value": 0.67,
                        "unit": "",
                        "description": "Efficiency of MP use for milk protein synthesis",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Pregnancy MP Coefficient",
                        "code": "mp_preg_coef",
                        "value": 0.69,
                        "unit": "g/kg",
                        "description": "Coefficient for pregnancy MP requirement",
                        "animal_type": "pregnant"
                    },
                    {
                        "name": "Pregnancy MP Exponent",
                        "code": "mp_preg_exp",
                        "value": 0.0345,
                        "unit": "1/day",
                        "description": "Exponent for pregnancy MP equation",
                        "animal_type": "pregnant"
                    },
                    {
                        "name": "Growth MP Factor",
                        "code": "mp_growth_factor",
                        "value": 0.12,
                        "unit": "",
                        "description": "Factor of maintenance MP for growth in first lactation",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "MP to CP Conversion",
                        "code": "mp_to_cp_conversion",
                        "value": 0.67,
                        "unit": "",
                        "description": "Conversion efficiency from CP to MP",
                        "animal_type": "all"
                    }
                ]
            },
            {
                "name": "Mineral Requirements",
                "description": "Coefficients for mineral requirement equations",
                "coefficients": [
                    {
                        "name": "Calcium Maintenance Coefficient",
                        "code": "ca_maint_coef",
                        "value": 0.0078,
                        "unit": "g/kg",
                        "description": "Coefficient for maintenance calcium requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Calcium Milk Coefficient",
                        "code": "ca_milk_coef",
                        "value": 1.2,
                        "unit": "g/kg",
                        "description": "Coefficient for milk calcium requirement",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Calcium Pregnancy Coefficient",
                        "code": "ca_preg_coef",
                        "value": 0.02,
                        "unit": "g/kg",
                        "description": "Coefficient for pregnancy calcium requirement",
                        "animal_type": "pregnant"
                    },
                    {
                        "name": "Phosphorus Maintenance Coefficient",
                        "code": "p_maint_coef",
                        "value": 0.01,
                        "unit": "g/kg",
                        "description": "Coefficient for maintenance phosphorus requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Phosphorus Milk Coefficient",
                        "code": "p_milk_coef",
                        "value": 0.9,
                        "unit": "g/kg",
                        "description": "Coefficient for milk phosphorus requirement",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Phosphorus Pregnancy Coefficient",
                        "code": "p_preg_coef",
                        "value": 0.01,
                        "unit": "g/kg",
                        "description": "Coefficient for pregnancy phosphorus requirement",
                        "animal_type": "pregnant"
                    },
                    {
                        "name": "Magnesium Maintenance Coefficient",
                        "code": "mg_maint_coef",
                        "value": 0.003,
                        "unit": "g/kg",
                        "description": "Coefficient for maintenance magnesium requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Magnesium Milk Coefficient",
                        "code": "mg_milk_coef",
                        "value": 0.15,
                        "unit": "g/kg",
                        "description": "Coefficient for milk magnesium requirement",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Iron Requirement",
                        "code": "fe_req",
                        "value": 50,
                        "unit": "mg/kg DM",
                        "description": "Iron requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Manganese Requirement",
                        "code": "mn_req",
                        "value": 40,
                        "unit": "mg/kg DM",
                        "description": "Manganese requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Zinc Requirement",
                        "code": "zn_req",
                        "value": 60,
                        "unit": "mg/kg DM",
                        "description": "Zinc requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Copper Requirement",
                        "code": "cu_req",
                        "value": 15,
                        "unit": "mg/kg DM",
                        "description": "Copper requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Cobalt Requirement",
                        "code": "co_req",
                        "value": 0.11,
                        "unit": "mg/kg DM",
                        "description": "Cobalt requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Iodine Requirement",
                        "code": "i_req",
                        "value": 0.5,
                        "unit": "mg/kg DM",
                        "description": "Iodine requirement",
                        "animal_type": "all"
                    },
                    {
                        "name": "Selenium Requirement",
                        "code": "se_req",
                        "value": 0.3,
                        "unit": "mg/kg DM",
                        "description": "Selenium requirement",
                        "animal_type": "all"
                    }
                ]
            },
            {
                "name": "Fiber Requirements",
                "description": "Coefficients for fiber requirement equations",
                "coefficients": [
                    {
                        "name": "NDF Min High Production",
                        "code": "ndf_min_high",
                        "value": 28,
                        "unit": "%",
                        "description": "Minimum NDF for high producing cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "NDF Max High Production",
                        "code": "ndf_max_high",
                        "value": 32,
                        "unit": "%",
                        "description": "Maximum NDF for high producing cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "NDF Min Medium Production",
                        "code": "ndf_min_medium",
                        "value": 30,
                        "unit": "%",
                        "description": "Minimum NDF for medium producing cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "NDF Max Medium Production",
                        "code": "ndf_max_medium",
                        "value": 35,
                        "unit": "%",
                        "description": "Maximum NDF for medium producing cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "NDF Min Low Production",
                        "code": "ndf_min_low",
                        "value": 33,
                        "unit": "%",
                        "description": "Minimum NDF for low producing cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "NDF Max Low Production",
                        "code": "ndf_max_low",
                        "value": 40,
                        "unit": "%",
                        "description": "Maximum NDF for low producing cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "NDF Min Dry Cow",
                        "code": "ndf_min_dry",
                        "value": 35,
                        "unit": "%",
                        "description": "Minimum NDF for dry cows",
                        "animal_type": "dry"
                    },
                    {
                        "name": "NDF Max Dry Cow",
                        "code": "ndf_max_dry",
                        "value": 45,
                        "unit": "%",
                        "description": "Maximum NDF for dry cows",
                        "animal_type": "dry"
                    },
                    {
                        "name": "Forage NDF Min High Production",
                        "code": "fndf_min_high",
                        "value": 18,
                        "unit": "%",
                        "description": "Minimum forage NDF for high producing cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Forage NDF Min Medium Production",
                        "code": "fndf_min_medium",
                        "value": 19,
                        "unit": "%",
                        "description": "Minimum forage NDF for medium producing cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Forage NDF Min Low Production",
                        "code": "fndf_min_low",
                        "value": 21,
                        "unit": "%",
                        "description": "Minimum forage NDF for low producing cows",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Forage NDF Min Dry Cow",
                        "code": "fndf_min_dry",
                        "value": 28,
                        "unit": "%",
                        "description": "Minimum forage NDF for dry cows",
                        "animal_type": "dry"
                    },
                    {
                        "name": "Early Lactation NDF Adjustment",
                        "code": "ndf_early_adj",
                        "value": 2,
                        "unit": "%",
                        "description": "NDF minimum adjustment for early lactation",
                        "animal_type": "lactating"
                    },
                    {
                        "name": "Early Lactation NDF Max Adjustment",
                        "code": "ndf_max_early_adj",
                        "value": 3,
                        "unit": "%",
                        "description": "NDF maximum adjustment for early lactation",
                        "animal_type": "lactating"
                    }
                ]
            }
        ]

        # Create groups and coefficients
        for group_data in groups:
            group = NrcCoefficientGroup(
                model_version_id=model.id,
                name=group_data["name"],
                description=group_data["description"]
            )
            db.session.add(group)
            db.session.flush()  # Get the group ID

            # Add coefficients for this group
            for coef_data in group_data["coefficients"]:
                coef = NrcCoefficient(
                    group_id=group.id,
                    name=coef_data["name"],
                    code=coef_data["code"],
                    value=coef_data["value"],
                    unit=coef_data["unit"],
                    description=coef_data["description"],
                    animal_type=coef_data["animal_type"]
                )
                db.session.add(coef)

        # Commit all changes
        db.session.commit()
        logger.info(f"Successfully initialized NRC 8th edition coefficients with {len(groups)} groups.")

    except Exception as e:
        db.session.rollback()
        logger.exception(f"Error initializing NRC coefficients: {e}")
        sys.exit(1)