import { useState } from 'react'
import { useParams, useNavigate, Link } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import { getFeedById, deleteFeed } from '../../services/feedService'
import { formatPrice } from '../../utils/formatters'

const FeedDetailsPage = () => {
  const { t, i18n } = useTranslation()
  const { id } = useParams()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const { data: feed, isLoading, error } = useQuery(['feed', id], () => getFeedById(id))

  const deleteMutation = useMutation(() => deleteFeed(id), {
    onSuccess: () => {
      queryClient.invalidateQueries('feeds')
      navigate('/feeds')
    }
  })

  const handleDelete = () => {
    deleteMutation.mutate()
  }

  if (isLoading) {
    return <p className="text-center py-4">{t('common.loading')}</p>
  }

  if (error) {
    return (
      <Card>
        <p className="text-red-500">{t('feeds.errorLoading')}: {error.message}</p>
        <div className="mt-4">
          <Link to="/feeds">
            <Button variant="outline">{t('feeds.backToFeeds')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  if (!feed) {
    return (
      <Card>
        <p className="text-gray-500">{t('feeds.feedNotFound')}</p>
        <div className="mt-4">
          <Link to="/feeds">
            <Button variant="outline">{t('feeds.backToFeeds')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{feed.name}</h1>
        <div className="flex space-x-2">
          <Link to={`/feeds/${id}/edit`}>
            <Button variant="outline">{t('common.edit')}</Button>
          </Link>
          <Button variant="danger" onClick={() => setShowDeleteConfirm(true)}>{t('common.delete')}</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card title={t('feeds.feedInformation')}>
          <div className="divide-y divide-gray-200">
            <div className="py-3 flex justify-between">
              <span className="text-gray-500">{t('feeds.dryMatter')}</span>
              <span className="font-medium">{(feed.dry_matter_percentage*100).toFixed(2)}%</span>
            </div>
            <div className="py-3 flex justify-between">
              <span className="text-gray-500">{t('feeds.costPerKg')}</span>
              <span className="font-medium">{formatPrice(feed.cost_per_kg, i18n.language)}</span>
            </div>
            <div className="py-3 flex justify-between">
              <span className="text-gray-500">{t('feeds.visibility')}</span>
              <span className="font-medium">{feed.is_public ? t('common.public') : t('common.private')}</span>
            </div>
            {feed.description && (
              <div className="py-3">
                <span className="text-gray-500 block mb-1">{t('common.description')}</span>
                <p>{feed.description}</p>
              </div>
            )}
          </div>
        </Card>

        <Card title={t('feeds.nutrientComposition')}>
          {feed.nutrients && feed.nutrients.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {feed.nutrients.map(nutrient => (
                <div key={nutrient.id} className="py-3 flex justify-between">
                  <span className="text-gray-500">{nutrient.nutrient_name}</span>
                  <span className="font-medium">
                  {nutrient.nutrient_unit === '%'
                    ? (parseFloat(nutrient.value) * 100).toFixed(2) // If unit is '%', multiply, format, and display
                    : nutrient.value                              // Otherwise, just display the value
                  }
                  {nutrient.nutrient_unit} {/* Display the unit after the value */}
                </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 py-4 text-center">{t('feeds.noNutrientData')}</p>
          )}
        </Card>
      </div>

      <div className="mt-6">
        <Link to="/feeds">
          <Button variant="outline">{t('feeds.backToFeeds')}</Button>
        </Link>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">{t('feeds.confirmDeletion')}</h3>
            <p className="text-gray-500 mb-4">
              {t('feeds.deleteConfirmationMessage', { name: feed.name })}
            </p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
                {t('common.cancel')}
              </Button>
              <Button variant="danger" onClick={handleDelete}>
                {t('common.delete')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FeedDetailsPage