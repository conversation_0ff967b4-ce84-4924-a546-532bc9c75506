import React from 'react'
import PropTypes from 'prop-types'
import { useTranslation } from 'react-i18next'
import Card from '../ui/Card'
import { formatNrcRequirements } from '../../services/nrcRequirementsService'
import { formatNumber, formatNumberWithUnit } from '../../utils/formatters'

const NrcRequirementsCard = ({ nrcData, className = '' }) => {
  const { t } = useTranslation()

  // Format the NRC data for display
  const formattedData = formatNrcRequirements(nrcData)

  // Check if we have data to display
  if (!formattedData || Object.keys(formattedData).length === 0) {
    return (
      <Card
        title={t('animalGroups.nrcRequirements')}
        className={className}
      >
        <div className="text-center py-4">
          <p className="text-gray-500">{t('animalGroups.noNrcData')}</p>
        </div>
      </Card>
    )
  }

  const { dmi, energy, protein, fiber } = formattedData

  return (
    <Card
      title={t('animalGroups.nrcRequirements')}
      className={className}
    >
      <div className="space-y-6">
        {/* DMI Section */}
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-2">
            {t('animalGroups.dryMatterIntake')}
          </h3>
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="flex justify-between">
              <span className="text-gray-600">{t('animalGroups.estimatedDMI')}</span>
              <span className="font-medium">
                {formatNumberWithUnit(dmi.value, dmi.unit)}
              </span>
            </div>
          </div>
        </div>

        {/* Energy Requirements Section */}
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-2">
            {t('animalGroups.energyRequirements')}
          </h3>
          <div className="bg-gray-50 p-3 rounded-md space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">{t('animalGroups.maintenance')}</span>
              <span className="font-medium">
                {formatNumberWithUnit(energy.maintenance.value, energy.maintenance.unit)}
              </span>
            </div>
            {energy.lactation.value > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('animalGroups.lactation')}</span>
                <span className="font-medium">
                  {formatNumberWithUnit(energy.lactation.value, energy.lactation.unit)}
                </span>
              </div>
            )}
            {energy.pregnancy.value > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('animalGroups.pregnancy')}</span>
                <span className="font-medium">
                  {formatNumberWithUnit(energy.pregnancy.value, energy.pregnancy.unit)}
                </span>
              </div>
            )}
            {energy.growth.value > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('animalGroups.growth')}</span>
                <span className="font-medium">
                  {formatNumberWithUnit(energy.growth.value, energy.growth.unit)}
                </span>
              </div>
            )}
            <div className="flex justify-between border-t pt-2 mt-2">
              <span className="text-gray-800 font-medium">{t('animalGroups.totalEnergy')}</span>
              <span className="font-medium">
                {formatNumberWithUnit(energy.total.value, energy.total.unit)}
              </span>
            </div>
            <div className="flex justify-between border-t pt-2 mt-2">
              <span className="text-gray-800 font-medium">{t('animalGroups.requiredConcentration')}</span>
              <span className="font-medium">
                {formatNumberWithUnit(energy.concentration.value, energy.concentration.unit)}
              </span>
            </div>
          </div>
        </div>

        {/* Protein Requirements Section */}
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-2">
            {t('animalGroups.proteinRequirements')}
          </h3>
          <div className="bg-gray-50 p-3 rounded-md space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">{t('animalGroups.maintenance')}</span>
              <span className="font-medium">
                {formatNumberWithUnit(protein.maintenance.value, protein.maintenance.unit)}
              </span>
            </div>
            {protein.lactation.value > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('animalGroups.lactation')}</span>
                <span className="font-medium">
                  {formatNumberWithUnit(protein.lactation.value, protein.lactation.unit)}
                </span>
              </div>
            )}
            {protein.pregnancy.value > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('animalGroups.pregnancy')}</span>
                <span className="font-medium">
                  {formatNumberWithUnit(protein.pregnancy.value, protein.pregnancy.unit)}
                </span>
              </div>
            )}
            {protein.growth.value > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('animalGroups.growth')}</span>
                <span className="font-medium">
                  {formatNumberWithUnit(protein.growth.value, protein.growth.unit)}
                </span>
              </div>
            )}
            <div className="flex justify-between border-t pt-2 mt-2">
              <span className="text-gray-800 font-medium">{t('animalGroups.totalProtein')}</span>
              <span className="font-medium">
                {formatNumberWithUnit(protein.total.value, protein.total.unit)}
              </span>
            </div>
            <div className="flex justify-between border-t pt-2 mt-2">
              <span className="text-gray-800 font-medium">{t('animalGroups.requiredConcentration')}</span>
              <span className="font-medium">
                {formatNumberWithUnit(protein.concentration.value, protein.concentration.unit)}
              </span>
            </div>
          </div>
        </div>

        {/* Fiber Requirements Section */}
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-2">
            {t('animalGroups.fiberRequirements')}
          </h3>
          <div className="bg-gray-50 p-3 rounded-md space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">{t('animalGroups.ndfRange')}</span>
              <span className="font-medium">
                {formatNumber(fiber.ndf_min.value)} - {formatNumberWithUnit(fiber.ndf_max.value, fiber.ndf_min.unit)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">{t('animalGroups.forageNdfMin')}</span>
              <span className="font-medium">
                {formatNumberWithUnit(fiber.forage_ndf_min.value, fiber.forage_ndf_min.unit)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">{t('animalGroups.adfMin')}</span>
              <span className="font-medium">
                {formatNumberWithUnit(fiber.adf_min.value, fiber.adf_min.unit)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}

NrcRequirementsCard.propTypes = {
  nrcData: PropTypes.object,
  className: PropTypes.string
}

export default NrcRequirementsCard
