""" knowledge base changed

Revision ID: dc25f7339226
Revises: a96119cfc414
Create Date: 2025-04-14 15:40:44.451591

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dc25f7339226'
down_revision = 'a96119cfc414'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('kb_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('slug', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['ration_app.kb_categories.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug'),
    schema='ration_app'
    )
    op.create_table('kb_tags',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    schema='ration_app'
    )
    op.create_table('kb_articles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('slug', sa.String(length=255), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('summary', sa.Text(), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('published', sa.Boolean(), nullable=True),
    sa.Column('view_count', sa.Integer(), nullable=True),
    sa.Column('helpful_count', sa.Integer(), nullable=True),
    sa.Column('not_helpful_count', sa.Integer(), nullable=True),
    sa.Column('embedding', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('article_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['ration_app.kb_categories.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['ration_app.users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug'),
    schema='ration_app'
    )
    op.create_table('kb_article_tags',
    sa.Column('article_id', sa.Integer(), nullable=False),
    sa.Column('tag_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['article_id'], ['ration_app.kb_articles.id'], ),
    sa.ForeignKeyConstraint(['tag_id'], ['ration_app.kb_tags.id'], ),
    sa.PrimaryKeyConstraint('article_id', 'tag_id'),
    schema='ration_app'
    )
    op.create_table('kb_feedback',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('article_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('helpful', sa.Boolean(), nullable=True),
    sa.Column('comment', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['article_id'], ['ration_app.kb_articles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['ration_app.users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='ration_app'
    )
    # Drop tables in the correct order to respect foreign key dependencies
    op.drop_table('knowledge_documents')
    op.drop_table('knowledge_entries')
    op.drop_table('knowledge_categories')
    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint('animal_groups_herd_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'herds', ['herd_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint('constraints_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint('feed_nutrients_nutrient_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('feed_nutrients_feed_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'nutrients', ['nutrient_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app', ondelete='CASCADE')

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint('feeds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint('herds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint('ration_feeds_feed_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('ration_feeds_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint('rations_animal_group_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('rations_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'animal_groups', ['animal_group_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint('staged_import_sessions_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('staged_import_sessions_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('rations_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('rations_animal_group_id_fkey', 'animal_groups', ['animal_group_id'], ['id'])

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('ration_feeds_ration_id_fkey', 'rations', ['ration_id'], ['id'])
        batch_op.create_foreign_key('ration_feeds_feed_id_fkey', 'feeds', ['feed_id'], ['id'])

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('herds_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feeds_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feed_nutrients_feed_id_fkey', 'feeds', ['feed_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('feed_nutrients_nutrient_id_fkey', 'nutrients', ['nutrient_id'], ['id'])

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('constraints_ration_id_fkey', 'rations', ['ration_id'], ['id'])

    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('animal_groups_herd_id_fkey', 'herds', ['herd_id'], ['id'])

    # Create tables in the correct order to respect foreign key dependencies
    op.create_table('knowledge_categories',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('parent_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['knowledge_categories.id'], name='knowledge_categories_parent_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='knowledge_categories_pkey')
    )

    op.create_table('knowledge_entries',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('knowledge_entries_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('category_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('content_format', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('tags', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('source', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('is_published', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['knowledge_categories.id'], name='knowledge_entries_category_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='knowledge_entries_pkey'),
    postgresql_ignore_search_path=False
    )

    op.create_table('knowledge_documents',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('entry_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('filename', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('file_path', sa.VARCHAR(length=512), autoincrement=False, nullable=False),
    sa.Column('file_size', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('file_type', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('extracted_text', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('doc_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['entry_id'], ['knowledge_entries.id'], name='knowledge_documents_entry_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='knowledge_documents_pkey')
    )
    # Drop tables in the correct order to respect foreign key dependencies
    op.drop_table('kb_feedback', schema='ration_app')
    op.drop_table('kb_article_tags', schema='ration_app')
    op.drop_table('kb_articles', schema='ration_app')
    op.drop_table('kb_tags', schema='ration_app')
    op.drop_table('kb_categories', schema='ration_app')
    # ### end Alembic commands ###
