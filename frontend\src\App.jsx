import { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import useAuthStore from './store/authStore'

// Layouts
import MainLayout from './components/layout/MainLayout'
import AuthLayout from './components/layout/AuthLayout'

// Auth Pages
import LoginPage from './pages/auth/LoginPage'
import RegisterPage from './pages/auth/RegisterPage'

// Main Pages
import DashboardPage from './pages/dashboard/DashboardPage'

// Feeds Pages
import FeedsListPage from './pages/feeds/FeedsListPage'
import FeedDetailsPage from './pages/feeds/FeedDetailsPage'
import CreateFeedPage from './pages/feeds/CreateFeedPage'

// Herds Pages
import HerdsListPage from './pages/herds/HerdsListPage'
import HerdDetailsPage from './pages/herds/HerdDetailsPage'
import CreateHerdPage from './pages/herds/CreateHerdPage'

// Animal Groups Pages
import AnimalGroupsListPage from './pages/animalGroups/AnimalGroupsListPage'
import AnimalGroupDetailsPage from './pages/animalGroups/AnimalGroupDetailsPage'
import CreateAnimalGroupPage from './pages/animalGroups/CreateAnimalGroupPage'

// Rations Pages
import RationsListPage from './pages/rations/RationsListPage'
import RationDetailsPage from './pages/rations/RationDetailsPage'
import UnifiedRationPage  from './pages/rations/UnifiedRationPage'

// Import Data Page
import ExcelImportReviewPage from './pages/import/ExcelImportReviewPage'

// Knowledge Base Pages
import KBHomePage from './pages/kb/KBHomePage'
import KBArticlePage from './pages/kb/KBArticlePage'
import KBCategoryPage from './pages/kb/KBCategoryPage'
import KBAskPage from './pages/kb/KBAskPage'
import KBAdminPage from './pages/kb/KBAdminPage'

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuthStore()

  if (loading) {
    // Don't redirect while checking authentication
    return <div className="flex items-center justify-center h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
    </div>
  }

  if (!isAuthenticated) {
    console.log('Not authenticated, redirecting to login');
    return <Navigate to="/login" replace />
  }

  return children
}

function App() {
  const { checkAuth } = useAuthStore()

  useEffect(() => {
    // Check if user is authenticated on app load
    console.log('App mounted, checking authentication');
    checkAuth().then(result => {
      console.log('Authentication check result:', result ? 'Authenticated' : 'Not authenticated');
    })
  }, [checkAuth])

  return (
    <Routes>
      {/* Auth Routes */}
      <Route element={<AuthLayout />}>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />
      </Route>

      {/* Protected Routes */}
      <Route element={
        <ProtectedRoute>
          <MainLayout />
        </ProtectedRoute>
      }>
        <Route path="/" element={<DashboardPage />} />

        {/* Feeds Routes */}
        <Route path="/feeds" element={<FeedsListPage />} />
        <Route path="/feeds/new" element={<CreateFeedPage />} />
        <Route path="/feeds/:id" element={<FeedDetailsPage />} />
        <Route path="/feeds/:id/edit" element={<CreateFeedPage />} />

        {/* Herds Routes */}
        <Route path="/herds" element={<HerdsListPage />} />
        <Route path="/herds/new" element={<CreateHerdPage />} />
        <Route path="/herds/:id" element={<HerdDetailsPage />} />
        <Route path="/herds/:id/edit" element={<CreateHerdPage />} />

        {/* Animal Groups Routes */}
        <Route path="/animal-groups" element={<AnimalGroupsListPage />} />
        <Route path="/animal-groups/new" element={<CreateAnimalGroupPage />} />
        <Route path="/animal-groups/:id" element={<AnimalGroupDetailsPage />} />
        <Route path="/animal-groups/:id/edit" element={<CreateAnimalGroupPage />} />

        {/* Rations Routes */}
        <Route path="/rations" element={<RationsListPage />} />
        <Route path="/rations/new" element={<UnifiedRationPage />} />
        <Route path="/rations/:id" element={<RationDetailsPage />} />
        <Route path="/rations/:id/edit" element={<UnifiedRationPage />} />
        <Route path="/rations/:id/formulate" element={<UnifiedRationPage />} />
        {/* Import Routes */}
        <Route path="/excel-import-review/:sessionId" element={<ExcelImportReviewPage />} />

        {/* Knowledge Base Routes */}
        <Route path="/kb" element={<KBHomePage />} />
        <Route path="/kb/articles/:slug" element={<KBArticlePage />} />
        <Route path="/kb/categories/:slug" element={<KBCategoryPage />} />
        <Route path="/kb/search" element={<Navigate to={(location) => {
          // Preserve search parameters when redirecting
          return {
            pathname: '/kb',
            search: location.search
          }
        }} replace />} />
        <Route path="/kb/ask" element={<KBAskPage />} />
        <Route path="/kb/admin" element={<KBAdminPage />} />
      </Route>

      {/* Redirect to dashboard if path doesn't match */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  )
}

export default App