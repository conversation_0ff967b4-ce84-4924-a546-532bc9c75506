"""
NRC Coefficient Service

This module provides functions for retrieving and using NRC coefficients from the database.
"""
import logging
from ..models import NrcModelVersion, NrcCoefficientGroup, NrcCoefficient

logger = logging.getLogger(__name__)

def get_active_model_version():
    """
    Get the currently active NRC model version
    
    Returns:
        NrcModelVersion: The active model version, or None if not found
    """
    try:
        return NrcModelVersion.query.filter_by(is_active=True).first()
    except Exception as e:
        logger.error(f"Error getting active NRC model version: {str(e)}")
        return None

def get_coefficient_value(code, group_name=None, model_version_id=None, animal_type=None):
    """
    Get a coefficient value from the database
    
    Args:
        code: The coefficient code
        group_name: Optional group name to narrow the search
        model_version_id: Optional model version ID (defaults to active model)
        animal_type: Optional animal type (e.g., 'lactating', 'dry', 'heifer')
        
    Returns:
        float: The coefficient value, or None if not found
    """
    try:
        # Build the query
        query = NrcCoefficient.query.join(
            NrcCoefficientGroup, 
            NrcCoefficient.group_id == NrcCoefficientGroup.id
        )
        
        # Filter by code
        query = query.filter(NrcCoefficient.code == code)
        
        # Filter by group name if provided
        if group_name:
            query = query.filter(NrcCoefficientGroup.name == group_name)
            
        # Filter by animal type if provided
        if animal_type:
            query = query.filter(NrcCoefficient.animal_type == animal_type)
            
        # Filter by model version
        if model_version_id:
            query = query.filter(NrcCoefficientGroup.model_version_id == model_version_id)
        else:
            # Use the active model version
            active_version = get_active_model_version()
            if active_version:
                query = query.filter(NrcCoefficientGroup.model_version_id == active_version.id)
            else:
                logger.warning("No active NRC model version found")
                return None
                
        # Get the coefficient
        coefficient = query.first()
        
        if coefficient:
            return coefficient.value
        else:
            logger.warning(f"Coefficient not found: code={code}, group={group_name}, animal_type={animal_type}")
            return None
            
    except Exception as e:
        logger.error(f"Error getting coefficient value: {str(e)}")
        return None

def get_coefficients_by_group(group_name, model_version_id=None, animal_type=None):
    """
    Get all coefficients for a group
    
    Args:
        group_name: The group name
        model_version_id: Optional model version ID (defaults to active model)
        animal_type: Optional animal type (e.g., 'lactating', 'dry', 'heifer')
        
    Returns:
        dict: Dictionary of coefficient code -> value
    """
    try:
        # Build the query
        query = NrcCoefficient.query.join(
            NrcCoefficientGroup, 
            NrcCoefficient.group_id == NrcCoefficientGroup.id
        ).filter(NrcCoefficientGroup.name == group_name)
        
        # Filter by animal type if provided
        if animal_type:
            query = query.filter(NrcCoefficient.animal_type == animal_type)
            
        # Filter by model version
        if model_version_id:
            query = query.filter(NrcCoefficientGroup.model_version_id == model_version_id)
        else:
            # Use the active model version
            active_version = get_active_model_version()
            if active_version:
                query = query.filter(NrcCoefficientGroup.model_version_id == active_version.id)
            else:
                logger.warning("No active NRC model version found")
                return {}
                
        # Get the coefficients
        coefficients = query.all()
        
        # Convert to dictionary
        return {coef.code: coef.value for coef in coefficients}
            
    except Exception as e:
        logger.error(f"Error getting coefficients by group: {str(e)}")
        return {}

def get_all_coefficients(model_version_id=None):
    """
    Get all coefficients organized by group
    
    Args:
        model_version_id: Optional model version ID (defaults to active model)
        
    Returns:
        dict: Dictionary of group name -> {coefficient code -> value}
    """
    try:
        # Determine model version
        if model_version_id:
            version_id = model_version_id
        else:
            # Use the active model version
            active_version = get_active_model_version()
            if active_version:
                version_id = active_version.id
            else:
                logger.warning("No active NRC model version found")
                return {}
                
        # Get all groups for this model version
        groups = NrcCoefficientGroup.query.filter_by(model_version_id=version_id).all()
        
        # Build the result dictionary
        result = {}
        for group in groups:
            coefficients = NrcCoefficient.query.filter_by(group_id=group.id).all()
            result[group.name] = {coef.code: coef.value for coef in coefficients}
            
        return result
            
    except Exception as e:
        logger.error(f"Error getting all coefficients: {str(e)}")
        return {}

def get_coefficient_metadata(code, group_name=None, model_version_id=None, animal_type=None):
    """
    Get a coefficient's full metadata from the database
    
    Args:
        code: The coefficient code
        group_name: Optional group name to narrow the search
        model_version_id: Optional model version ID (defaults to active model)
        animal_type: Optional animal type (e.g., 'lactating', 'dry', 'heifer')
        
    Returns:
        dict: The coefficient metadata, or None if not found
    """
    try:
        # Build the query
        query = NrcCoefficient.query.join(
            NrcCoefficientGroup, 
            NrcCoefficient.group_id == NrcCoefficientGroup.id
        )
        
        # Filter by code
        query = query.filter(NrcCoefficient.code == code)
        
        # Filter by group name if provided
        if group_name:
            query = query.filter(NrcCoefficientGroup.name == group_name)
            
        # Filter by animal type if provided
        if animal_type:
            query = query.filter(NrcCoefficient.animal_type == animal_type)
            
        # Filter by model version
        if model_version_id:
            query = query.filter(NrcCoefficientGroup.model_version_id == model_version_id)
        else:
            # Use the active model version
            active_version = get_active_model_version()
            if active_version:
                query = query.filter(NrcCoefficientGroup.model_version_id == active_version.id)
            else:
                logger.warning("No active NRC model version found")
                return None
                
        # Get the coefficient
        coefficient = query.first()
        
        if coefficient:
            return coefficient.to_dict()
        else:
            logger.warning(f"Coefficient not found: code={code}, group={group_name}, animal_type={animal_type}")
            return None
            
    except Exception as e:
        logger.error(f"Error getting coefficient metadata: {str(e)}")
        return None
