from datetime import datetime
from ..extensions import db

class NrcModelVersion(db.Model):
    """
    Represents a version of the NRC model (e.g., 8th edition)
    """
    __tablename__ = 'nrc_model_versions'
    __table_args__ = {'schema': 'ration_app'}
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    publication_date = db.Column(db.Date)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    coefficient_groups = db.relationship('NrcCoefficientGroup', backref='model_version', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'publication_date': self.publication_date.isoformat() if self.publication_date else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }

class NrcCoefficientGroup(db.Model):
    """
    Represents a group of related coefficients (e.g., DMI, energy, protein)
    """
    __tablename__ = 'nrc_coefficient_groups'
    __table_args__ = {'schema': 'ration_app'}
    
    id = db.Column(db.Integer, primary_key=True)
    model_version_id = db.Column(db.Integer, db.ForeignKey('ration_app.nrc_model_versions.id'), nullable=False)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    
    # Relationships
    coefficients = db.relationship('NrcCoefficient', backref='coefficient_group', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'model_version_id': self.model_version_id,
            'name': self.name,
            'description': self.description
        }

class NrcCoefficient(db.Model):
    """
    Represents a specific coefficient used in NRC calculations
    """
    __tablename__ = 'nrc_coefficients'
    __table_args__ = {'schema': 'ration_app'}
    
    id = db.Column(db.Integer, primary_key=True)
    group_id = db.Column(db.Integer, db.ForeignKey('ration_app.nrc_coefficient_groups.id'), nullable=False)
    name = db.Column(db.String(255), nullable=False)
    code = db.Column(db.String(50), nullable=False)  # Short code for programmatic reference
    value = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(50))
    description = db.Column(db.Text)
    animal_type = db.Column(db.String(50))  # e.g., lactating, dry, heifer
    
    def to_dict(self):
        return {
            'id': self.id,
            'group_id': self.group_id,
            'name': self.name,
            'code': self.code,
            'value': self.value,
            'unit': self.unit,
            'description': self.description,
            'animal_type': self.animal_type
        }
