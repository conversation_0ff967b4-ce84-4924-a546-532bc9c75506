<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Excel Data Analyzer</title>

    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.js"></script>

    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css"/>

    <link href="taskpane.css" rel="stylesheet" type="text/css" />
</head>

<body class="ms-font-m ms-Fabric">
    <div id="container" class="ms-Grid">
        <div class="ms-Grid-row">
            <div class="ms-Grid-col ms-sm12">
                <h1 class="ms-font-xxl">AI表格分析</h1>
                <p class="ms-font-m">AI自动分析同步excel表格</p>
                <p id="login-status" class="ms-font-m">Status: Not logged in.</p>
            </div>
        </div>

        <div class="ms-Grid-row section" id="login-section">
            <div class="ms-Grid-col ms-sm12">
                <h2 class="ms-font-xl">Login</h2>
                 <div class="ms-TextField">
                    <label class="ms-Label">Server URL:</label>
                    <input id="server-url" class="ms-TextField-field" type="text" placeholder="http://localhost:5000" value="http://localhost:5000" />
                </div>
                <div class="ms-TextField">
                    <label class="ms-Label">Email:</label>
                    <input id="login-email" class="ms-TextField-field" type="email" placeholder="Enter your platform email" />
                </div>
                <div class="ms-TextField">
                    <label class="ms-Label">Password:</label>
                    <input id="login-password" class="ms-TextField-field" type="password" placeholder="Enter your password" />
                </div>
                <button id="login-button" class="ms-Button ms-Button--primary">
                    <span class="ms-Button-label">Login</span>
                </button>
                <button id="logout-button" class="ms-Button" style="display: none;">
                    <span class="ms-Button-label">Logout</span>
                </button>
            </div>
        </div>

        <div class="ms-Grid-row section">
            <div class="ms-Grid-col ms-sm12">
                <h2 class="ms-font-xl">选择工作表</h2>
                <div class="ms-CheckBox">
                    <input id="show-hidden-sheets" class="ms-CheckBox-input" type="checkbox">
                    <label for="show-hidden-sheets" class="ms-CheckBox-field">
                        <span class="ms-Label">显示隐藏表格</span>
                    </label>
                </div>
                <div class="ms-SelectWrapper">
                    <label class="ms-Label">选择工作簿:</label>
                    <select id="sheet-selector" class="ms-SelectWrapper-select">
                        <option value="">选择工作簿</option>
                    </select>
                </div>
                <button id="get-sheet-names" class="ms-Button">
                    <span class="ms-Button-label">刷新列表</span>
                </button>
            </div>
        </div>

        <div class="ms-Grid-row section">
            <div class="ms-Grid-col ms-sm12">
                <h2 class="ms-font-xl">工作表分析</h2>
                <button id="analyze-workbook" class="ms-Button ms-Button--primary" disabled>
                    <span class="ms-Button-label">开始分析</span>
                </button>
                <div class="ms-TextField ms-TextField--multiline">
                    <label class="ms-Label">分析结果:</label>
                    <div id="workbook-info" class="ms-TextField-field code-block"></div>
                </div>
            </div>
        </div>

        <div class="ms-Grid-row section">
            <div class="ms-Grid-col ms-sm12">
                <h2 class="ms-font-xl">数据预览</h2>
                <div class="preview-controls">
                    <button id="preview-data" class="ms-Button" disabled>
                        <span class="ms-Button-label">数据预览</span>
                    </button>
                    <div class="ms-Toggle" id="preview-toggle-container">
                        <span class="ms-Label">显示原始 JSON 数据</span>
                        <input type="checkbox" id="raw-json-toggle" class="ms-Toggle-input" />
                        <label for="raw-json-toggle" class="ms-Toggle-field"></label>
                    </div>
                </div>
                <div id="data-preview-container" class="preview-container" style="display: none;">
                    <!-- Visual preview will be inserted here -->
                </div>
                <div id="raw-json-container" class="code-block" style="display: none;">
                    <pre id="raw-json-content"></pre>
                </div>
            </div>
        </div>

        <div class="ms-Grid-row section">
            <div class="ms-Grid-col ms-sm12">
                 <button id="send-data" class="ms-Button ms-Button--primary" disabled>
                    <span class="ms-Button-label">提取并处理数据</span>
                </button>
            </div>
        </div>

        <div class="ms-Grid-row section" id="review-link-section" style="display: none;">
            <div class="ms-Grid-col ms-sm12">
                <h2 class="ms-font-xl">数据审核</h2>
                <p class="ms-font-m">您的数据已成功上传并等待审核。请点击下方链接查看并确认导入：</p>
                <div class="review-link-container">
                    <a id="review-page-link" href="#" target="_blank" class="ms-Button ms-Button--primary">
                        <span class="ms-Button-label">查看并确认导入</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="ms-Grid-row section">
            <div class="ms-Grid-col ms-sm12">
                <p id="status-info" class="ms-font-m status-message"></p>
                <p id="error-info" class="ms-font-m error-message"></p>
            </div>
        </div>

        <div class="ms-Grid-row section" id="previous-analyses-section" style="display: none;">
            <div class="ms-Grid-col ms-sm12">
                <h2 class="ms-font-xl">历史上传</h2>
                <button id="clear-history" class="ms-Button ms-Button--small">
                    <span class="ms-Button-icon"><i class="ms-Icon ms-Icon--Delete"></i></span>
                    <span class="ms-Button-label">清空历史</span>
                </button>
                <div id="previous-analyses-list" class="ms-List">
                    </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="taskpane.ts"></script>
</body>
</html>