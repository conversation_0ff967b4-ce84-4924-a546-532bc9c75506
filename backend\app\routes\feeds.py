from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from ..models import Feed, Nutrient, FeedNutrient
from ..extensions import db
import logging

logger = logging.getLogger(__name__)
bp = Blueprint('feeds', __name__, url_prefix='/api/feeds')

@bp.route('', methods=['GET'])
@jwt_required()
def get_feeds():
    current_user_id = get_jwt_identity()

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)

    # Get user's feeds and public feeds with pagination
    query = Feed.query.filter(
        (Feed.user_id == int(current_user_id)) | (Feed.is_public == True)
    ).order_by(Feed.created_at.desc())

    # Apply pagination
    pagination = query.paginate(page=page, per_page=limit, error_out=False)
    feeds = pagination.items

    return jsonify({
        'feeds': [feed.to_dict() for feed in feeds],
        'page': page,
        'totalPages': pagination.pages,
        'totalItems': pagination.total
    }), 200

@bp.route('', methods=['POST'])
@jwt_required()
def create_feed():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    # Validate required fields
    if not data or not data.get('name') or not isinstance(data.get('dry_matter_percentage'), (int, float)) or not isinstance(data.get('cost_per_kg'), (int, float)):
        return jsonify({'message': 'Name, dry matter percentage, and cost per kg are required'}), 400

    # Create new feed
    feed = Feed(
        user_id=int(current_user_id),
        name=data['name'],
        description=data.get('description'),
        dry_matter_percentage=data['dry_matter_percentage'],
        cost_per_kg=data['cost_per_kg'],
        is_public=data.get('is_public', False)
    )

    db.session.add(feed)

    # Add nutrients if provided
    if data.get('nutrients'):
        for nutrient_data in data['nutrients']:
            # Get or create nutrient
            nutrient = Nutrient.query.filter_by(name_en=nutrient_data['name']).first()
            if not nutrient:
                nutrient = Nutrient(
                    name_en=nutrient_data['name'],
                    name_zh=nutrient_data.get('name_zh'),
                    unit=nutrient_data.get('unit', '%'),
                    description=nutrient_data.get('description')
                )
                db.session.add(nutrient)
                db.session.flush()  # Generate ID without committing

            # Add feed nutrient
            feed_nutrient = FeedNutrient(
                feed_id=feed.id,
                nutrient_id=nutrient.id,
                value=nutrient_data['value']
            )
            db.session.add(feed_nutrient)

    db.session.commit()

    return jsonify({
        'message': 'Feed created successfully',
        'feed': feed.to_dict(include_nutrients=True)
    }), 201

@bp.route('/<int:feed_id>', methods=['GET'])
@jwt_required()
def get_feed(feed_id):
    current_user_id = get_jwt_identity()

    # Get feed that belongs to user or is public
    feed = Feed.query.filter_by(id=feed_id).filter(
        (Feed.user_id == int(current_user_id)) | (Feed.is_public == True)
    ).first()

    if not feed:
        return jsonify({'message': 'Feed not found'}), 404

    return jsonify({
        'feed': feed.to_dict(include_nutrients=True)
    }), 200

@bp.route('/<int:feed_id>', methods=['PUT'])
@jwt_required()
def update_feed(feed_id):
    current_user_id = get_jwt_identity()

    # Get feed that belongs to user
    feed = Feed.query.filter_by(id=feed_id, user_id=int(current_user_id)).first()

    if not feed:
        return jsonify({'message': 'Feed not found or you do not have permission to update it'}), 404

    data = request.get_json()

    # Update feed fields
    if data.get('name'):
        feed.name = data['name']
    if data.get('description') is not None:
        feed.description = data['description']
    if isinstance(data.get('dry_matter_percentage'), (int, float)):
        feed.dry_matter_percentage = data['dry_matter_percentage']
    if isinstance(data.get('cost_per_kg'), (int, float)):
        feed.cost_per_kg = data['cost_per_kg']
    if data.get('is_public') is not None:
        feed.is_public = data['is_public']

    # Update nutrients if provided
    if data.get('nutrients'):
        # Remove existing nutrients
        FeedNutrient.query.filter_by(feed_id=feed.id).delete()

        # Add new nutrients
        for nutrient_data in data['nutrients']:
            # Get or create nutrient
            nutrient = Nutrient.query.filter_by(name_en=nutrient_data['name']).first()
            if not nutrient:
                nutrient = Nutrient(
                    name_en=nutrient_data['name'],
                    name_zh=nutrient_data.get('name_zh'),
                    unit=nutrient_data.get('unit', '%'),
                    description=nutrient_data.get('description')
                )
                db.session.add(nutrient)
                db.session.flush()  # Generate ID without committing

            # Add feed nutrient
            feed_nutrient = FeedNutrient(
                feed_id=feed.id,
                nutrient_id=nutrient.id,
                value=nutrient_data['value']
            )
            db.session.add(feed_nutrient)

    db.session.commit()

    return jsonify({
        'message': 'Feed updated successfully',
        'feed': feed.to_dict(include_nutrients=True)
    }), 200

@bp.route('/<int:feed_id>', methods=['DELETE'])
@jwt_required()
def delete_feed(feed_id):
    current_user_id = get_jwt_identity()

    # Get feed that belongs to user
    feed = Feed.query.filter_by(id=feed_id, user_id=int(current_user_id)).first()

    if not feed:
        return jsonify({'message': 'Feed not found or you do not have permission to delete it'}), 404

    db.session.delete(feed)
    db.session.commit()

    return jsonify({
        'message': 'Feed deleted successfully'
    }), 200

@bp.route('/nutrients', methods=['GET'])
def get_nutrients():
    """Get all nutrients"""
    try:
        nutrients = Nutrient.query.all()

        return jsonify({
            'nutrients': [nutrient.to_dict() for nutrient in nutrients]
        }), 200
    except Exception as e:
        logger.error(f"Error getting nutrients: {str(e)}")
        return jsonify({'message': str(e)}), 500