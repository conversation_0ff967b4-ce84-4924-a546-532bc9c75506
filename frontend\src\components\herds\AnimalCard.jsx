import React, { memo, useMemo } from 'react'
import PropTypes from 'prop-types'
import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import Card from '../ui/Card'
import { CheckCircle, AlertTriangle, XCircle, Calendar, Venus, Mars } from 'lucide-react'

/**
 * Visual constants
 */
const STATUS_CONFIG = {
  healthy: {
    color: 'text-green-700',
    bg: 'bg-green-100',
    key: 'herds.healthy',
    Icon: CheckCircle,
  },
  sick: {
    color: 'text-yellow-700',
    bg: 'bg-yellow-100',
    key: 'herds.sick',
    Icon: AlertTriangle,
  },
  notOnFarm: {
    color: 'text-red-700',
    bg: 'bg-red-100',
    key: 'herds.notOnFarm',
    Icon: XCircle,
  },
}

/**
 * Generic meta item renderer
 */
const MetaItem = ({ icon: IconCmp, children }) => (
  <div className="flex items-center gap-1">
    {IconCmp && <IconCmp className="w-4 h-4 text-gray-400" aria-hidden="true" />}
    <span className="text-gray-600">{children}</span>
  </div>
)

const AnimalCard = ({ animal, compact = false, className = '' }) => {
  const { t, i18n } = useTranslation()

  // Emoji icon for breed (since lucide-react has no Cow icon)
  const BreedIcon = () => (
    <span role="img" aria-label={t('herds.breed')} className="w-4 h-4 text-gray-400">
      🐄
    </span>
  )

  // Locale‑aware birth date formatting
  const birthDate = useMemo(() => {
    if (!animal.birth_date) return null
    try {
      const date = new Date(animal.birth_date)
      return date.toLocaleDateString(i18n.language, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      })
    } catch {
      return null
    }
  }, [animal.birth_date, i18n.language])

  // Determine status visual
  const statusBadge = useMemo(() => {
    // Convert numeric is_on_farm to boolean (0 = false, any other value = true)
    const isOnFarm = animal.is_on_farm === 0 ? false : Boolean(animal.is_on_farm)
    if (!isOnFarm) return STATUS_CONFIG.notOnFarm
    return animal.health_status === 'sick' ? STATUS_CONFIG.sick : STATUS_CONFIG.healthy
  }, [animal.is_on_farm, animal.health_status])

  const BadgeIcon = statusBadge.Icon

  return (
    <Link
      to={`/herds/${animal.id}`}
      className="block"
      aria-label={
        animal.name
          ? t('herds.viewAnimal', { id: animal.ear_num, name: animal.name })
          : t('herds.viewUnnamedAnimal', { id: animal.ear_num })
      }
    >
      <Card className={`hover:shadow-lg transition-shadow duration-200 rounded-2xl bg-white overflow-hidden ${className}`}>
        {/* Header */}
        <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-xl shadow px-6 py-3 flex justify-between items-center">
          <h3 className="text-xl font-semibold tracking-wide text-gray-800">
            {animal.ear_num ? `#${animal.ear_num}` : t('herds.unnamed')}
          </h3>

          <span className={`${statusBadge.bg} inline-flex items-center gap-1 px-3 py-1 rounded-full shadow-sm`}>
            <BadgeIcon className={`w-4 h-4 ${statusBadge.color}`} />
            <span className={`text-sm font-medium ${statusBadge.color}`}>{t(statusBadge.key)}</span>
          </span>
        </div>

        {/* Body */}
        <div className="px-6 py-4 space-y-3 text-sm">
          <div className="flex flex-wrap gap-4">
            {animal.breed && (
              <MetaItem icon={BreedIcon}>
                {animal.breed}
              </MetaItem>
            )}
            {animal.gender && (
              <MetaItem icon={animal.gender === 'male' ? Mars : Venus}>
                {animal.gender === 'male' ? t('herds.male') : t('herds.female')}
              </MetaItem>
            )}
            {birthDate && (
              <MetaItem icon={Calendar}>{t('herds.born')}: {birthDate}</MetaItem>
            )}
          </div>

          {!compact && (
            <div className="grid grid-cols-2 gap-y-2 gap-x-6">
              {animal.lactation_number != null && (
                <div className="flex flex-col">
                  <span className="text-xs text-gray-500">{t('herds.lactationNumber')}</span>
                  <span className="font-medium text-gray-700">{animal.lactation_number}</span>
                </div>
              )}
              {animal.group_name && (
                <div className="flex flex-col">
                  <span className="text-xs text-gray-500">{t('herds.group')}</span>
                  <span className="font-medium text-gray-700">{animal.group_name}</span>
                </div>
              )}
              {animal.grow_status && (
                <div className="flex flex-col">
                  <span className="text-xs text-gray-500">{t('herds.growthStatus')}</span>
                  <span className="font-medium text-gray-700">{animal.grow_status}</span>
                </div>
              )}
              {animal.fertility_status && (
                <div className="flex flex-col">
                  <span className="text-xs text-gray-500">{t('herds.fertilityStatus')}</span>
                  <span className="font-medium text-gray-700">{animal.fertility_status}</span>
                </div>
              )}
            </div>
          )}
        </div>
      </Card>
    </Link>
  )
}

AnimalCard.propTypes = {
  animal: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    ear_num: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string,
    birth_date: PropTypes.string,
    is_on_farm: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
    health_status: PropTypes.oneOf(['healthy', 'sick']),
    breed: PropTypes.string,
    gender: PropTypes.oneOf(['male', 'female']),
    lactation_number: PropTypes.number,
    group_name: PropTypes.string,
    grow_status: PropTypes.string,
    fertility_status: PropTypes.string,
  }).isRequired,
  compact: PropTypes.bool,
  className: PropTypes.string,
}

export default memo(AnimalCard)