import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useArticles, useDeleteArticle, useUpdateArticle } from '../../hooks/useArticles';
import { useQuery } from 'react-query';
import { getCategories } from '../../services/kbService';
import FilterBar from '../../components/ui/FilterBar';
import Button from '../../components/ui/Button';
import Select from '../../components/ui/Select';

export default function ArticlesTab() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [filter, setFilter] = useState({ published: 'all', search: '', page: 1, limit: 10 });
  const { data: articlesData = { articles: [], total: 0, pages: 1 }, isLoading, refetch } = useArticles(filter);
  const deleteMutation = useDeleteArticle();
  const updateMutation = useUpdateArticle();

  // State for category edit modal
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingArticle, setEditingArticle] = useState(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState('');

  // Fetch categories for the dropdown
  const { data: categories = [], isLoading: catLoading } = useQuery('kb-admin-categories', () => getCategories());

  const handleFilterChange = (key, value) => setFilter(prev => ({ ...prev, [key]: value, page: 1 }));
  const handlePage = newPage => setFilter(prev => ({ ...prev, page: newPage }));
  const handleDelete = id => {
    if (window.confirm(t('kb.confirmDeleteArticle'))) deleteMutation.mutate(id);
  };

  // Open the category edit modal
  const handleEditCategory = (article) => {
    setEditingArticle(article);
    setSelectedCategoryId(article.category_id.toString());
    setShowCategoryModal(true);
  };

  // Update the article's category
  const handleCategoryUpdate = () => {
    if (!editingArticle || !selectedCategoryId) return;

    updateMutation.mutate({
      articleId: editingArticle.id,
      data: { category_id: parseInt(selectedCategoryId) }
    }, {
      onSuccess: () => {
        setShowCategoryModal(false);
        setEditingArticle(null);
        setSelectedCategoryId('');
      }
    });
  };

  if (isLoading) return (
    <div className="flex justify-center items-center py-16">
      <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
    </div>
  );

  if (articlesData.articles.length === 0) return (
    <div className="bg-white rounded-lg shadow p-8 text-center">
      <p className="text-gray-600 mb-4">{t('kb.noArticles')}</p>
      <Button onClick={() => navigate('/kb/admin/articles/new')}>
        {t('kb.createArticle')}
      </Button>
    </div>
  );

  return (
    <div className="space-y-6">
      <FilterBar
        search={filter.search}
        published={filter.published}
        onSearch={val => handleFilterChange('search', val)}
        onPublished={val => handleFilterChange('published', val)}
        onRefresh={refetch}
        loading={isLoading}
      />
      <div className="overflow-x-auto bg-white rounded-lg shadow">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50 rounded-t-lg">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('kb.title')}</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('kb.category')}</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('common.status')}</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('kb.views')}</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('common.actions')}</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {articlesData.articles.map(a => (
              <tr key={a.id} className="hover:bg-gray-50 transition-colors">
                <td className="px-6 py-4">
                  <div className="text-sm font-medium text-blue-600">{a.title}</div>
                  <div className="text-xs text-gray-500">{new Date(a.created_at).toLocaleDateString()}</div>
                </td>
                <td className="px-6 py-4">
                  <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">{a.category_name}</span>
                </td>
                <td className="px-6 py-4">
                  <span className={`px-2 py-1 text-xs rounded-full ${a.published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                    {a.published ? t('common.published') : t('common.draft')}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">{a.view_count}</td>
                <td className="px-6 py-4 text-sm space-x-2">
                  <a href={`/kb/articles/${a.slug}`} target="_blank" className="text-blue-600 hover:text-blue-800">{t('common.view')}</a>
                  <span className="text-gray-300">|</span>
                  <button onClick={() => handleEditCategory(a)} className="text-blue-600 hover:text-blue-800">{t('common.edit')}</button>
                  <span className="text-gray-300">|</span>
                  <button onClick={() => handleDelete(a.id)} className="text-red-600 hover:text-red-800">{t('common.delete')}</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {articlesData.pages > 1 && (
        <div className="flex justify-between items-center mt-4 bg-white p-4 rounded-lg shadow">
          <Button
            onClick={() => handlePage(filter.page - 1)}
            disabled={filter.page === 1}
            variant="outline"
          >
            {t('common.previous')}
          </Button>
          <span className="text-sm font-medium text-gray-700">
            {t('common.page')} {filter.page} {t('common.of')} {articlesData.pages}
          </span>
          <Button
            onClick={() => handlePage(filter.page + 1)}
            disabled={filter.page === articlesData.pages}
            variant="outline"
          >
            {t('common.next')}
          </Button>
        </div>
      )}

      {/* Category Edit Modal */}
      {showCategoryModal && editingArticle && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {t('kb.editArticleCategory', { title: editingArticle.title })}
            </h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">{t('kb.category')}</label>
              <Select
                value={selectedCategoryId}
                onChange={(e) => setSelectedCategoryId(e.target.value)}
                options={categories.map(c => ({ value: c.id.toString(), label: c.name }))}
                disabled={catLoading || updateMutation.isLoading}
              />
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowCategoryModal(false);
                  setEditingArticle(null);
                  setSelectedCategoryId('');
                }}
                disabled={updateMutation.isLoading}
              >
                {t('common.cancel')}
              </Button>
              <Button
                onClick={handleCategoryUpdate}
                disabled={!selectedCategoryId || updateMutation.isLoading}
              >
                {updateMutation.isLoading ? (
                  <>
                    <span className="animate-spin inline-block h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                    {t('common.saving')}
                  </>
                ) : t('common.save')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}