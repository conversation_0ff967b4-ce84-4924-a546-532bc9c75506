import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import { getFeeds } from '../../services/feedService'
import { getHerds } from '../../services/herdService'
import { getRations } from '../../services/rationService'
import { formatPriceWithUnit, formatNumberWithUnit, formatNumber } from '../../utils/formatters'
import { getAnimalGroups } from '../../services/animalGroupService'


const DashboardPage = () => {
  const { t, i18n } = useTranslation()
  const { data: feedsData = { feeds: [] } } = useQuery('dashboard-feeds', () => getFeeds(1, 5))
  const { data: herdsData = { herds: [] } } = useQuery('dashboard-herds', () => getHerds(1, 5))
  const { data: rationsData = { rations: [] } } = useQuery('dashboard-rations', () => getRations(1, 5))
  const { data: animalGroups = [] } = useQuery('dashboard-animal-groups', () => getAnimalGroups())

  // Extract data for convenience - we need these for the recent items sections
  const feeds = feedsData?.feeds || []
  const rations = rationsData?.rations || []

  // Get total counts
  const totalFeeds = feedsData?.totalItems || 0
  const totalHerds = herdsData?.totalItems || 0
  const totalRations = rationsData?.totalItems || 0

  return (
    <div>
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">{t('nav.dashboard')}</h1>
        <p className="mt-2 text-gray-600">{t('dashboard.welcome')}</p>
      </header>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <StatCard
          title={t('dashboard.feeds')}
          count={totalFeeds}
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          }
          link="/feeds"
        />

        <StatCard
          title={t('dashboard.herds')}
          count={totalHerds}
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          }
          link="/herds"
        />

        <StatCard
          title={t('nav.animalGroups')}
          count={animalGroups.length}
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
          }
          link="/animal-groups"
        />

        <StatCard
          title={t('dashboard.rations')}
          count={totalRations}
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          }
          link="/rations"
        />

        <StatCard
          title={t('dashboard.milkProduction')}
          count={formatNumberWithUnit(28.5, 'kg')}
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          }
          placeholder={true}
        />

        <StatCard
          title={t('dashboard.bcs')}
          count={formatNumber(3.2)}
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          }
          placeholder={true}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card title={t('feeds.title')} subtitle={t('feeds.recentlyAdded')}>
          {feeds.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-gray-500 mb-4">{t('feeds.noFeedsFound')}</p>
              <Link to="/feeds/new">
                <Button>{t('feeds.createNew')}</Button>
              </Link>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {feeds.slice(0, 5).map(feed => (
                <div key={feed.id} className="py-3">
                  <Link to={`/feeds/${feed.id}`} className="block hover:bg-gray-50">
                    <div className="flex justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">{feed.name}</h4>
                        <p className="text-sm text-gray-500 truncate">
                          {t('feeds.dmShort')}: {formatNumber(feed.dry_matter_percentage)}% | {t('feeds.costShort')}: {formatPriceWithUnit(feed.cost_per_kg, 'kg', i18n.language)}
                        </p>
                      </div>
                      <div className="text-sm text-gray-500">
                        {t('common.created')}: {new Date(feed.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          )}
          {feeds.length > 0 && (
            <div className="mt-4 text-right">
              <Link to="/feeds" className="text-sm font-medium text-green-600 hover:text-green-500">
                {t('common.viewAll')} {totalFeeds} {t('feeds.title').toLowerCase()} →
              </Link>
            </div>
          )}
        </Card>

        <Card title={t('animalGroups.title')} subtitle={t('animalGroups.recentlyAdded')}>
          {animalGroups.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-gray-500 mb-4">{t('animalGroups.noGroupsFound')}</p>
              <Link to="/animal-groups/new">
                <Button>{t('animalGroups.addNew')}</Button>
              </Link>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {animalGroups.slice(0, 5).map(group => (
                <div key={group.id} className="py-3">
                  <Link to={`/animal-groups/${group.id}`} className="block hover:bg-gray-50">
                    <div className="flex justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">{group.name}</h4>
                        <p className="text-sm text-gray-500 truncate">
                          {t('animalGroups.animals')}: {group.herd_count || 0}
                        </p>
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(group.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          )}
          {animalGroups.length > 0 && (
            <div className="mt-4 text-right">
              <Link to="/animal-groups" className="text-sm font-medium text-green-600 hover:text-green-500">
                {t('common.viewAll')} {animalGroups.length} {t('nav.animalGroups').toLowerCase()} →
              </Link>
            </div>
          )}
        </Card>

        <Card title={t('rations.recent')} subtitle={t('rations.recentlyFormulated')}>
          {rations.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-gray-500 mb-4">{t('rations.noRationsFound')}</p>
              <Link to="/rations/new">
                <Button>{t('rations.createNew')}</Button>
              </Link>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {rations.slice(0, 5).map(ration => (
                <div key={ration.id} className="py-3">
                  <Link to={`/rations/${ration.id}`} className="block hover:bg-gray-50">
                    <div className="flex justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">{ration.name}</h4>
                        <p className="text-sm text-gray-500 truncate">
                          {ration.last_formulated_at
                            ? `${t('rations.lastFormulated')}: ${new Date(ration.last_formulated_at).toLocaleDateString()}`
                            : t('rations.notYetFormulated')
                          }
                        </p>
                      </div>
                      {ration.last_formulated_at ? (
                        <div className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full flex items-center">
                          {t('rations.formulated')}
                        </div>
                      ) : (
                        <div className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full flex items-center">
                          {t('rations.pending')}
                        </div>
                      )}
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          )}
          {rations.length > 0 && (
            <div className="mt-4 text-right">
              <Link to="/rations" className="text-sm font-medium text-green-600 hover:text-green-500">
                {t('common.viewAll')} {totalRations} {t('rations.title').toLowerCase()} →
              </Link>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}

// Stat Card Component
const StatCard = ({ title, count, icon, link, placeholder = false }) => {
  const { t } = useTranslation()

  // Card content is the same for both regular and placeholder cards
  const cardContent = (
    <div className={`bg-white overflow-hidden shadow rounded-lg ${!placeholder ? 'cursor-pointer hover:shadow-md transition-shadow duration-200' : 'opacity-70'}`}>
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            {icon}
          </div>
          <div className="ml-5 w-0 flex-1">
            <dt className="text-sm font-medium text-gray-500 truncate">
              {title}
            </dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-gray-900">
                {count}
              </div>
            </dd>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-4 py-4 sm:px-6">
        <div className="text-sm">
          <div className={`font-medium ${placeholder ? 'text-gray-400' : 'text-green-600 hover:text-green-500'}`}>
            {placeholder ? t('common.comingSoon') : t('common.viewAll')}<span className="ml-1">{!placeholder && '→'}</span>
          </div>
        </div>
      </div>
    </div>
  )

  // If it's a placeholder, don't wrap in a Link
  return placeholder ? cardContent : <Link to={link}>{cardContent}</Link>
}

export default DashboardPage