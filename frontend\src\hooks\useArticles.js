import { useQuery, useMutation, useQueryClient } from 'react-query';
import { getArticles, deleteArticle, updateArticle } from '../services/kbService';

/**
 * Fetches articles based on filter params.
 * @param {{ page: number, limit: number, published: string, search: string }} filter
 */
export function useArticles(filter) {
  return useQuery(
    ['kb-admin-articles', filter],
    () => {
      const params = {
        page: filter.page,
        limit: filter.limit,
        sort_by: 'created_at',
        sort_order: 'desc',
      };
      if (filter.published !== 'all') {
        params.published = filter.published === 'published';
      }
      if (filter.search) {
        params.search = filter.search;
      }
      return getArticles(params);
    },
    { keepPreviousData: true }
  );
}

/**
 * Mutation hook to delete an article and refresh relevant queries.
 */
export function useDeleteArticle() {
  const queryClient = useQueryClient();
  return useMutation(
    (articleId) => deleteArticle(articleId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('kb-admin-articles');
        queryClient.invalidateQueries('kb-stats');
      },
    }
  );
}

/**
 * Mutation hook to update an article and refresh relevant queries.
 */
export function useUpdateArticle() {
  const queryClient = useQueryClient();
  return useMutation(
    ({ articleId, data }) => updateArticle(articleId, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('kb-admin-articles');
        queryClient.invalidateQueries('kb-stats');
      },
    }
  );
}
