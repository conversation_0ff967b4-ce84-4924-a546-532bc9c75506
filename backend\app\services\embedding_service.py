"""
Vector Embedding Implementation for Knowledge Base

This module provides functions to generate and use vector embeddings for
semantic search in the knowledge base.
"""

import numpy as np
import logging
from typing import Optional, List, Dict, Any, Union
from flask import current_app

# Configure logging
logger = logging.getLogger(__name__)

class EmbeddingService:
    @staticmethod
    def generate_embeddings(text: str, type: str = 'RETRIEVAL_QUERY') -> Optional[List[float]]:
        """
        Generate embeddings for the provided text using the configured embedding model.

        Args:
            text: The text to generate embeddings for

        Returns:
            List of float values representing the embedding vector, or None if generation fails
        """
        if not text:
            logger.warning("Empty text provided for embedding generation")
            return None

        try:
            # Get embedding provider from config
            embedding_provider = current_app.config.get('EMBEDDING_PROVIDER', 'openai').lower()
            dimensions = current_app.config.get('EMBEDDING_DIMENSIONS', 768)

            logger.info(f"Generating embeddings using provider: {embedding_provider}, dimensions: {dimensions}")

            if embedding_provider == 'openai':
                return EmbeddingService._get_openai_embedding(text,type)
            elif embedding_provider == 'google':
                return EmbeddingService._get_google_embedding(text,type)
            elif embedding_provider == 'tongyi':
                return EmbeddingService._get_tongyi_embedding(text,type)
            elif embedding_provider == 'huggingface':
                return EmbeddingService._get_huggingface_embedding(text,type)
            else:
                logger.warning(f"Unknown embedding provider: {embedding_provider}, falling back to OpenAI")
                return EmbeddingService._get_openai_embedding(text)
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}", exc_info=True)
            return None

    @staticmethod
    def _get_openai_embedding(text: str, type: str = 'RETRIEVAL_QUERY') -> List[float]:
        """Generate embeddings using OpenAI's embedding API."""
        import openai

        # Get API key - first try embedding-specific key, fall back to general key
        api_key = current_app.config.get('OPENAI_EMBEDDING_API_KEY') or current_app.config.get('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not configured")

        # Get configured model name
        model = current_app.config.get('EMBEDDING_MODEL', 'text-embedding-ada-002')

        logger.debug(f"Generating OpenAI embedding with model: {model}")
        client = openai.OpenAI(api_key=api_key)

        # Call OpenAI's embeddings API
        response = client.embeddings.create(
            model=model,
            input=text
        )

        # Return the embedding vector
        return response.data[0].embedding

    @staticmethod
    def _get_tongyi_embedding(text: str, type: str = 'RETRIEVAL_QUERY') -> List[float]:
        """Generate embeddings using OpenAI's embedding API."""
        import openai

        client = openai.OpenAI(
        api_key=current_app.config.get('TONGYI_API_KEY'),  # 如果您没有配置环境变量，请在此处用您的API Key进行替换
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"  # 百炼服务的base_url
        )

        completion = client.embeddings.create(
            model=current_app.config.get('EMBEDDING_MODEL'),
            input=text,
            dimensions=current_app.config.get('EMBEDDING_DIMENSIONS'),
            encoding_format="float"
        )

        # Return the embedding vector
        return completion.data[0].embedding


    @staticmethod
    def _get_google_embedding(text: str, type: str = 'RETRIEVAL_QUERY') -> List[float]:
        """Generate embeddings using Google's text embeddings API."""
        from google import genai
        from google.genai import types

        # Get API key - first try embedding-specific key, fall back to general key
        api_key = current_app.config.get('GEMINI_API_KEY')
        if not api_key:
            raise ValueError("Google API key not configured")

        # Get configured model name
        model = current_app.config.get('EMBEDDING_MODEL', 'gemini-embedding-exp-03-07')
        dimensions = current_app.config.get('EMBEDDING_DIMENSIONS', 768)  # Match the current configuration

        logger.debug(f"Generating Google embedding with model: {model}, dimensions: {dimensions}")
        client = genai.Client(api_key=api_key)

        try:
            result = client.models.embed_content(
                model=model,
                contents=text,
                config=types.EmbedContentConfig(output_dimensionality=dimensions, task_type=type)
            )

            # Handle the specific structure: embeddings=[ContentEmbedding(values=[...])]
            embedding_list = list(result.embeddings[0].values)

            return embedding_list

        except Exception as e:
            logger.error(f"Error in Google embedding generation: {str(e)}")
            raise

    @staticmethod
    def _get_huggingface_embedding(text: str, type: str = 'RETRIEVAL_QUERY') -> List[float]:
        """Generate embeddings using a local Hugging Face model."""
        from sentence_transformers import SentenceTransformer

        # Use a pre-trained Sentence Transformer model
        model_name = current_app.config.get('HUGGINGFACE_EMBEDDING_MODEL', 'all-MiniLM-L6-v2')

        logger.debug(f"Generating HuggingFace embedding with model: {model_name}")

        # Create or get model from cache
        model = EmbeddingService._get_cached_huggingface_model(model_name)

        # Generate embeddings
        embedding = model.encode(text)
        return embedding.tolist()  # Convert numpy array to list

    # Cache for HuggingFace models
    _huggingface_models = {}

    @staticmethod
    def _get_cached_huggingface_model(model_name: str):
        """Get or create a HuggingFace model from cache."""
        if model_name not in EmbeddingService._huggingface_models:
            from sentence_transformers import SentenceTransformer
            EmbeddingService._huggingface_models[model_name] = SentenceTransformer(model_name)
        return EmbeddingService._huggingface_models[model_name]

    @staticmethod
    def calculate_similarity(embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings.

        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector

        Returns:
            Similarity score between 0 and 1
        """
        if not embedding1 or not embedding2:
            return 0.0

        # Convert to numpy arrays
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)

        # Calculate cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    @staticmethod
    def prepare_text_for_embedding(title: str, content: str, max_length: int = 8000) -> str:
        """
        Prepare text for embedding by combining title and content with appropriate truncation.

        Args:
            title: The article title
            content: The article content
            max_length: Maximum text length for embedding

        Returns:
            Prepared text string
        """
        # Combine title and content
        title = title or ""
        content = content or ""

        # Give more weight to title by repeating it
        combined_text = f"{title}\n\n{title}\n\n{content}"

        # Truncate if too long
        if len(combined_text) > max_length:
            # Keep the title and truncate the content
            title_part = f"{title}\n\n{title}\n\n"
            content_len = max_length - len(title_part)
            truncated_content = content[:content_len]
            combined_text = f"{title_part}{truncated_content}"

        return combined_text

    @staticmethod
    def get_embedding_dimensions() -> int:
        """Get the configured embedding dimensions."""
        # Default to 768 to match the current configuration
        return current_app.config.get('EMBEDDING_DIMENSIONS', 768)