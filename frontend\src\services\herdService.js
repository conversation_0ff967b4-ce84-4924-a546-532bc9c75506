import api from './api'

// Get all herds with pagination
export const getHerds = async (page = 1, limit = 10) => {
  const response = await api.get('/herds', {
    params: { page, limit }
  })
  return {
    herds: response.data.herds,
    page: response.data.page,
    totalPages: response.data.totalPages,
    totalItems: response.data.totalItems
  }
}

// Get herd by ID
export const getHerdById = async (id) => {
  const response = await api.get(`/herds/${id}`)
  return response.data.herd
}

// Create new herd
export const createHerd = async (herdData) => {
  const response = await api.post('/herds', herdData)
  return response.data
}

// Update herd
export const updateHerd = async (id, herdData) => {
  const response = await api.put(`/herds/${id}`, herdData)
  return response.data
}

// Delete herd
export const deleteHerd = async (id) => {
  const response = await api.delete(`/herds/${id}`)
  return response.data
}

// Get animal groups for a herd
export const getAnimalGroups = async (herdId) => {
  const response = await api.get(`/herds/${herdId}/groups`)
  return response.data.animal_groups
}

// Create animal group
export const createAnimalGroup = async (herdId, groupData) => {
  const response = await api.post(`/herds/${herdId}/groups`, groupData)
  return response.data
}

// Get animal group by ID
export const getAnimalGroupById = async (herdId, groupId) => {
  const response = await api.get(`/herds/${herdId}/groups/${groupId}`)
  return response.data.animal_group
}

// Update animal group
export const updateAnimalGroup = async (herdId, groupId, groupData) => {
  const response = await api.put(`/herds/${herdId}/groups/${groupId}`, groupData)
  return response.data
}

// Delete animal group
export const deleteAnimalGroup = async (herdId, groupId) => {
  const response = await api.delete(`/herds/${herdId}/groups/${groupId}`)
  return response.data
}