import React from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../ui/Button';

/**
 * Component to handle and display formulation errors with guidance
 *
 * @param {Object} props Component props
 * @param {Object} props.error Error object from formulation attempt
 * @param {string} props.status Status of formulation (error, failed, infeasible)
 * @param {Array} props.conflictingConstraints List of conflicting constraints if available
 * @param {Function} props.onRetry Callback function to retry formulation
 * @param {Function} props.onAdjust Callback function to adjust settings
 */
const FormulationErrorHandler = ({
  error,
  status = 'error',
  conflictingConstraints = [],
  onRetry,
  onAdjust
}) => {
  const { t, i18n } = useTranslation();

  // Determine error type and message
  let title = t('rations.formulationError');
  let description = t('rations.formulationErrorMessage');
  let icon = (
    <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );

  // Extract troubleshooting tips if available
  const troubleshootingTips = error && error.troubleshooting_tips ? error.troubleshooting_tips :
                             (error && error.result && error.result.troubleshooting_tips ? error.result.troubleshooting_tips : []);

  // Set appropriate messages based on status
  if (status === 'infeasible') {
    title = t('rations.infeasibleFormulation');
    description = t('rations.infeasibleFormulationDescription');
    icon = (
      <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    );
  } else if (status === 'failed') {
    title = t('rations.formulationFailed');
    description = t('rations.formulationFailedDescription');
  } else if (status === 'incomplete') {
    title = t('rations.formulationIncomplete');
    description = t('rations.formulationIncompleteDescription');
    icon = (
      <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    );
  }

  return (
    <div className="bg-red-50 border border-dashed border-red-300 rounded-lg p-8">
      <div className="text-center">
        {icon}
        <h3 className="mt-4 text-lg font-medium text-red-800">{title}</h3>
        <p className="mt-2 text-sm text-red-700">{description}</p>

        {error && error.message && (
          <div className="mt-4 p-3 bg-white rounded-md text-sm text-gray-700 text-left">
            <p className="font-medium text-red-700">{t('common.error')}:</p>
            <p>
              {/* Translate common error messages */}
              {error.message.includes('No feasible solution exists with the given constraints')
                ? t('rations.noFeasibleSolution')
                : error.message.includes('Solver failed with status')
                  ? t('rations.solverFailed')
                  : error.message}
            </p>
          </div>
        )}

        {/* <div className="mt-6 flex flex-col sm:flex-row justify-center gap-3">
          {onRetry && (
            <Button
              onClick={onRetry}
              variant="outline"
              size="sm"
            >
              {t('rations.tryAgain')}
            </Button>
          )}

          {onAdjust && (
            <Button
              onClick={onAdjust}
              variant="primary"
              size="sm"
            >
              {t('rations.adjustAndRetry')}
            </Button>
          )}
        </div> */}
      </div>

      {conflictingConstraints && conflictingConstraints.length > 0 && (
        <div className="mt-8 pt-6 border-t border-red-200">
          <h4 className="text-md font-medium text-red-800">{t('rations.conflictingConstraints')}</h4>
          <p className="mt-2 text-sm text-red-700">{t('rations.conflictingConstraintsDescription')}</p>

          {/* Definite conflicts */}
          {conflictingConstraints.filter(c => !c.type || c.type !== 'potential').length > 0 && (
            <div className="mt-4 bg-white p-4 rounded-lg shadow-sm">
              <h5 className="font-medium text-red-800 mb-2">{t('rations.definiteConflicts')}</h5>
              <ul className="list-disc list-inside text-sm text-gray-700">
                {conflictingConstraints
                  .filter(c => !c.type || c.type !== 'potential')
                  .map((conflict, index) => {
                    // Translate common constraint tags
                    let translatedTag = conflict.tag;
                    if (conflict.tag === 'General Infeasibility') {
                      translatedTag = t('rations.generalInfeasibility');
                    } else if (conflict.tag === 'Feed Minimums') {
                      translatedTag = t('rations.feedMinimums');
                    } else if (conflict.tag && conflict.tag.startsWith('Feed Min/Max')) {
                      translatedTag = t('rations.feedMinMaxConflict');
                    } else if (conflict.tag && conflict.tag.startsWith('Nutrient Min/Max')) {
                      translatedTag = t('rations.nutrientMinMaxConflict');
                    } else if (conflict.tag && conflict.tag.includes('Nutrient')) {
                      // Extract nutrient name from tag for localization
                      const nutrientTagMatch = conflict.tag.match(/\(([^)]+)\)/);
                      if (nutrientTagMatch) {
                        const nutrientName = nutrientTagMatch[1];
                        // Check if we have a Chinese name for this nutrient
                        if (i18n.language === 'zh' && conflict.nutrient_name_zh) {
                          // Replace the English nutrient name with Chinese in the tag
                          translatedTag = conflict.tag.replace(nutrientName, conflict.nutrient_name_zh);
                        }
                      }
                    }

                    // Translate common constraint details
                    let translatedDetail = conflict.detail;
                    if (conflict.detail && conflict.detail.includes('The combination of constraints might be too restrictive')) {
                      translatedDetail = t('rations.constraintsTooRestrictive');
                    } else if (conflict.detail && conflict.detail.includes('The total minimum inclusion')) {
                      translatedDetail = t('rations.totalMinimumExceeds');
                    } else if (conflict.detail && conflict.detail.includes('Minimum inclusion') && conflict.detail.includes('maximum inclusion')) {
                      // Extract feed name, min and max values for translation
                      const feedMatch = conflict.detail.match(/Feed '([^']+)'/);
                      const minMatch = conflict.detail.match(/Minimum inclusion \(([0-9.]+)%\)/);
                      const maxMatch = conflict.detail.match(/maximum inclusion \(([0-9.]+)%\)/);

                      if (feedMatch && minMatch && maxMatch) {
                        const feedName = feedMatch[1];
                        const minVal = minMatch[1];
                        const maxVal = maxMatch[1];

                        translatedDetail = t('rations.feedMinMaxConflictDetail', {
                          feed: feedName,
                          min: minVal,
                          max: maxVal
                        });

                        // Fallback in case translation with parameters doesn't work
                        if (translatedDetail.includes('{feed}') || translatedDetail.includes('{min}')) {
                          translatedDetail = `饲料'${feedName}'：最小包含量(${minVal}%)大于最大包含量(${maxVal}%)。`;
                        }
                      } else {
                        translatedDetail = t('rations.minGreaterThanMax');
                      }
                    } else if (conflict.detail && conflict.detail.includes('Nutrient') && conflict.detail.includes('Minimum requirement') && conflict.detail.includes('maximum requirement')) {
                      // Use structured data if available
                      if (conflict.nutrient_name && conflict.min_req !== undefined && conflict.max_req !== undefined && conflict.unit) {
                        // Get the appropriate nutrient name based on locale
                        let nutrientName = conflict.nutrient_name;
                        if (i18n.language === 'zh' && conflict.nutrient_name_zh) {
                          nutrientName = conflict.nutrient_name_zh;
                        }

                        translatedDetail = t('rations.nutrientMinMaxConflictDetail', {
                          nutrient: nutrientName,
                          min: conflict.min_req,
                          max: conflict.max_req,
                          unit: conflict.unit
                        });

                        // Fallback in case translation with parameters doesn't work
                        if (translatedDetail.includes('{nutrient}') || translatedDetail.includes('{min}')) {
                          translatedDetail = i18n.language === 'zh'
                            ? `营养素'${nutrientName}'：最小需求(${conflict.min_req} ${conflict.unit})大于最大需求(${conflict.max_req} ${conflict.unit})。`
                            : `For nutrient '${nutrientName}': Minimum requirement (${conflict.min_req} ${conflict.unit}) is greater than maximum requirement (${conflict.max_req} ${conflict.unit}).`;
                        }
                      } else {
                        // Fallback to regex extraction if structured data is not available
                        const nutrientMatch = conflict.detail.match(/Nutrient '([^']+)'/);
                        const minMatch = conflict.detail.match(/Minimum requirement \(([0-9.]+)\s*([^)]+)\)/);
                        const maxMatch = conflict.detail.match(/maximum requirement \(([0-9.]+)\s*([^)]+)\)/);

                        if (nutrientMatch && minMatch && maxMatch) {
                          // Get the appropriate nutrient name based on locale
                          let nutrientName = nutrientMatch[1];
                          if (i18n.language === 'zh' && conflict.nutrient_name_zh) {
                            nutrientName = conflict.nutrient_name_zh;
                          }

                          const minVal = minMatch[1];
                          const maxVal = maxMatch[1];
                          const unitStr = minMatch[2].trim();

                          translatedDetail = t('rations.nutrientMinMaxConflictDetail', {
                            nutrient: nutrientName,
                            min: minVal,
                            max: maxVal,
                            unit: unitStr
                          });

                          // Fallback in case translation with parameters doesn't work
                          if (translatedDetail.includes('{nutrient}') || translatedDetail.includes('{min}')) {
                            translatedDetail = i18n.language === 'zh'
                              ? `营养素'${nutrientName}'：最小需求(${minVal} ${unitStr})大于最大需求(${maxVal} ${unitStr})。`
                              : `For nutrient '${nutrientName}': Minimum requirement (${minVal} ${unitStr}) is greater than maximum requirement (${maxVal} ${unitStr}).`;
                          }
                        }
                      }
                    } else if (conflict.detail && conflict.detail.includes('Nutrient')) {
                      // For other nutrient-related messages, try to replace the nutrient name with localized version
                      const nutrientDetailMatch = conflict.detail.match(/Nutrient '([^']+)'/);
                      if (nutrientDetailMatch && i18n.language === 'zh' && conflict.nutrient_name_zh) {
                        const englishName = nutrientDetailMatch[1];
                        translatedDetail = conflict.detail.replace(englishName, conflict.nutrient_name_zh);
                      }
                    }

                    return (
                      <li key={index} className="mb-2">
                        <span className="font-medium">{translatedTag || t('rations.constraint')}:</span> {translatedDetail}
                      </li>
                    );
                  })}
              </ul>
            </div>
          )}

          {/* Potential conflicts */}
          {conflictingConstraints.filter(c => c.type === 'potential').length > 0 && (
            <div className="mt-4 bg-white p-4 rounded-lg shadow-sm">
              <h5 className="font-medium text-orange-700 mb-2">{t('rations.potentialConflicts')}</h5>
              <ul className="list-disc list-inside text-sm text-gray-700">
                {conflictingConstraints
                  .filter(c => c.type === 'potential')
                  .map((conflict, index) => {
                    // Translate common constraint tags for potential conflicts
                    let translatedTag = conflict.tag;
                    let translatedDetail = conflict.detail;

                    if (conflict.tag && conflict.tag.startsWith('Nutrient Min')) {
                      translatedTag = t('rations.nutrientMinimumUnachievable');

                      // Use structured data if available
                      if (conflict.nutrient_name && conflict.min_req !== undefined && conflict.unit && conflict.max_conc !== undefined) {
                        // Get the appropriate nutrient name based on locale
                        let nutrientName = conflict.nutrient_name;
                        if (i18n.language === 'zh' && conflict.nutrient_name_zh) {
                          nutrientName = conflict.nutrient_name_zh;
                        }

                        translatedDetail = t('rations.nutrientMinDetail', {
                          nutrient: nutrientName,
                          value: conflict.min_req,
                          unit: conflict.unit,
                          maxConc: conflict.max_conc
                        });

                        // Fallback in case translation with parameters doesn't work
                        if (translatedDetail.includes('{nutrient}') || translatedDetail.includes('{value}')) {
                          translatedDetail = i18n.language === 'zh'
                            ? `${nutrientName}的最小需求${conflict.min_req} ${conflict.unit}可能无法达到。可用饲料中的最高浓度为${conflict.max_conc} ${conflict.unit}。`
                            : `Minimum requirement of ${conflict.min_req} ${conflict.unit} for ${nutrientName} might be unachievable. Highest concentration in available feeds is ${conflict.max_conc} ${conflict.unit}.`;
                        }
                      } else {
                        // Fallback to regex extraction if structured data is not available
                        const nutrientMatch = conflict.detail.match(/Nutrient '([^']+)'/);
                        const valueMatch = conflict.detail.match(/Minimum requirement \(([0-9.]+)\s*([^)]+)\)/);
                        const maxConcMatch = conflict.detail.match(/Highest concentration in available feeds is ([0-9.]+)\s*([^.]+)/);

                        if (nutrientMatch && valueMatch && maxConcMatch) {
                          const nutrientName = nutrientMatch[1];
                          const valueNum = valueMatch[1];
                          const unitStr = valueMatch[2].trim();
                          const maxConcNum = maxConcMatch[1];

                          translatedDetail = t('rations.nutrientMinDetail', {
                            nutrient: nutrientName,
                            value: valueNum,
                            unit: unitStr,
                            maxConc: maxConcNum
                          });

                          // Fallback in case translation with parameters doesn't work
                          if (translatedDetail.includes('{nutrient}') || translatedDetail.includes('{value}')) {
                            translatedDetail = i18n.language === 'zh'
                              ? `${nutrientName}的最小需求${valueNum} ${unitStr}可能无法达到。可用饲料中的最高浓度为${maxConcNum} ${unitStr}。`
                              : `Minimum requirement of ${valueNum} ${unitStr} for ${nutrientName} might be unachievable. Highest concentration in available feeds is ${maxConcNum} ${unitStr}.`;
                          }
                        }
                      }
                    } else if (conflict.tag && conflict.tag.startsWith('Nutrient Max')) {
                      translatedTag = t('rations.nutrientMaximumUnachievable');

                      // Use structured data if available
                      if (conflict.nutrient_name && conflict.max_req !== undefined && conflict.unit && conflict.min_conc !== undefined) {
                        // Get the appropriate nutrient name based on locale
                        let nutrientName = conflict.nutrient_name;
                        if (i18n.language === 'zh' && conflict.nutrient_name_zh) {
                          nutrientName = conflict.nutrient_name_zh;
                        }

                        translatedDetail = t('rations.nutrientMaxDetail', {
                          nutrient: nutrientName,
                          value: conflict.max_req,
                          unit: conflict.unit,
                          minConc: conflict.min_conc
                        });

                        // Fallback in case translation with parameters doesn't work
                        if (translatedDetail.includes('{nutrient}') || translatedDetail.includes('{value}')) {
                          translatedDetail = i18n.language === 'zh'
                            ? `${nutrientName}的最大需求${conflict.max_req} ${conflict.unit}可能无法达到。可用饲料中的最低浓度为${conflict.min_conc} ${conflict.unit}。`
                            : `Maximum requirement of ${conflict.max_req} ${conflict.unit} for ${nutrientName} might be unachievable. Lowest concentration in available feeds is ${conflict.min_conc} ${conflict.unit}.`;
                        }
                      } else {
                        // Fallback to regex extraction if structured data is not available
                        const nutrientMatch = conflict.detail.match(/Nutrient '([^']+)'/);
                        const valueMatch = conflict.detail.match(/Maximum requirement \(([0-9.]+)\s*([^)]+)\)/);
                        const minConcMatch = conflict.detail.match(/Lowest concentration in available feeds is ([0-9.]+)\s*([^.]+)/);

                        if (nutrientMatch && valueMatch && minConcMatch) {
                          const nutrientName = nutrientMatch[1];
                          const valueNum = valueMatch[1];
                          const unitStr = valueMatch[2].trim();
                          const minConcNum = minConcMatch[1];

                          translatedDetail = t('rations.nutrientMaxDetail', {
                            nutrient: nutrientName,
                            value: valueNum,
                            unit: unitStr,
                            minConc: minConcNum
                          });

                          // Fallback in case translation with parameters doesn't work
                          if (translatedDetail.includes('{nutrient}') || translatedDetail.includes('{value}')) {
                            translatedDetail = i18n.language === 'zh'
                              ? `${nutrientName}的最大需求${valueNum} ${unitStr}可能无法达到。可用饲料中的最低浓度为${minConcNum} ${unitStr}。`
                              : `Maximum requirement of ${valueNum} ${unitStr} for ${nutrientName} might be unachievable. Lowest concentration in available feeds is ${minConcNum} ${unitStr}.`;
                          }
                        }
                      }
                    }

                    return (
                      <li key={index} className="mb-2">
                        <span className="font-medium text-orange-600">{translatedTag || t('rations.potentialIssue')}:</span> {translatedDetail}
                      </li>
                    );
                  })}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Troubleshooting section */}
      <div className="mt-8 pt-6 border-t border-red-200">
        <h4 className="text-md font-medium text-red-800">{t('rations.troubleshootingTitle')}</h4>
        <p className="mt-2 text-sm text-red-700">{t('rations.troubleshootingDescription')}</p>

        {/* Display backend troubleshooting tips if available */}
        {/* {troubleshootingTips && troubleshootingTips.length > 0 && (
          <div className="mt-4 bg-white p-4 rounded-lg shadow-sm mb-6">
            <h5 className="font-medium text-red-800 mb-2">{t('rations.specificTips')}</h5>
            <ul className="list-disc list-inside text-sm text-gray-700">
              {troubleshootingTips.map((tip, index) => (
                <li key={index} className="mb-2">{tip}</li>
              ))}
            </ul>
          </div>
        )} */}

        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h5 className="font-medium text-red-800">{t('rations.checkConstraints')}</h5>
            <p className="mt-1 text-sm text-gray-600">{t('rations.checkConstraintsDescription')}</p>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h5 className="font-medium text-red-800">{t('rations.checkFeeds')}</h5>
            <p className="mt-1 text-sm text-gray-600">{t('rations.checkFeedsDescription')}</p>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h5 className="font-medium text-red-800">{t('rations.tooRestrictive')}</h5>
            <p className="mt-1 text-sm text-gray-600">{t('rations.tooRestrictiveDescription')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormulationErrorHandler;
