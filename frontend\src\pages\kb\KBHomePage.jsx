import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useQuery, useQueryClient } from 'react-query';
import { useTranslation } from 'react-i18next';

import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import KBArticleCard from '../../components/kb/KBArticleCard';

import {
  getCategories,
  getArticles,
  getPopularTags,
  searchArticles
} from '../../services/kbService';

const KBHomePage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const qs = new URLSearchParams(location.search);
  const queryClient = useQueryClient();

  /* --------------------------- State & params ---------------------------- */
  const initialQuery = qs.get('q') || '';
  const [query, setQuery] = useState(initialQuery);
  const [currentQuery, setCurrentQuery] = useState(initialQuery);
  const [searchTimestamp, setSearchTimestamp] = useState(Date.now());
  const [mode, setMode] = useState(qs.get('mode') === 'keyword' ? 'keyword' : 'semantic');

  /* ------------------------------ Data Fetch ------------------------------ */
  const { data: categories = [], isLoading: catLoad } = useQuery('kb-categories', () => getCategories(true));
  const { data: recent = { articles: [] }, isLoading: recLoad } = useQuery('kb-recent', () =>
    getArticles({ page: 1, limit: 3, sort_by: 'created_at', sort_order: 'desc', published: true })
  );
  const { data: popular = { articles: [] }, isLoading: popLoad } = useQuery('kb-popular', () =>
    getArticles({ page: 1, limit: 6, sort_by: 'view_count', sort_order: 'desc', published: true })
  );
  const { data: tags = [], isLoading: tagLoad } = useQuery('kb-tags', () => getPopularTags(30));
  const {
    data: search = [],
    isLoading: searchLoad,
    error: searchErr,
    refetch: doSearch
  } = useQuery(['kb-search', currentQuery, mode, searchTimestamp], () => searchArticles(currentQuery, 50, mode), {
    enabled: !!currentQuery,
    refetchOnWindowFocus: false
  });
  const globalLoading = catLoad || recLoad || popLoad || tagLoad;

  /* ------------------------------ Handlers -------------------------------- */
  const submitSearch = (e) => {
    e?.preventDefault();
    if (query.trim()) {
      // If the query is the same as the current one, update the timestamp to force a refetch
      if (query.trim() === currentQuery) {
        setSearchTimestamp(Date.now());
      } else {
        setCurrentQuery(query.trim());
      }
    }
  };

  useEffect(() => {
    if (currentQuery) {
      navigate({ pathname: location.pathname, search: `?q=${encodeURIComponent(currentQuery)}&mode=${mode}` }, { replace: true });
    } else {
      navigate({ pathname: location.pathname, search: '' }, { replace: true });
    }
  }, [currentQuery, mode]);

  const clearSearch = () => {
    setQuery('');
    setCurrentQuery('');
  };

  const toggleMode = () => setMode((prev) => (prev === 'semantic' ? 'keyword' : 'semantic'));

  /* -------------------------------- Render -------------------------------- */
  return (
    <div className="space-y-10">
      {/* Hero / Search */}
      <section className="bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-8 shadow">
        <h1 className="text-3xl md:text-4xl font-extrabold text-gray-900 mb-2">{t('kb.title')}</h1>
        <p className="text-gray-600 mb-6 max-w-2xl">{t('kb.description')}</p>

        <form onSubmit={submitSearch} className="flex flex-col md:flex-row gap-3 md:gap-0">
          <Input
            placeholder={t('kb.searchPlaceholder')}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="flex-grow rounded-r-none"
            noMargin
          />
          <Button type="submit" className="rounded-l-none h-10 md:w-40 shrink-0" disabled={searchLoad || !query.trim()}>
            {searchLoad ? t('common.searching') : t('common.search')}
          </Button>
        </form>

        <div className="flex items-center justify-between mt-4 text-sm">
          <button
            onClick={toggleMode}
            className={`px-3 py-1 rounded-full ${mode === 'semantic' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}
          >
            {mode === 'semantic' ? t('kb.semanticSearch') : t('kb.keywordSearch')}
          </button>
          {currentQuery && (
            <button onClick={clearSearch} className="text-gray-500 hover:text-gray-700">
              {t('common.reset')}
            </button>
          )}
        </div>
      </section>

      {/* Search Results */}
      {currentQuery && (
        <section className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold text-gray-900">
              {t('kb.searchResults')}: "{currentQuery}"
            </h2>
            {!searchLoad && !searchErr && (
              <span className="text-sm text-gray-500">
                {search.length} {t('kb.resultsFound')}
              </span>
            )}
          </div>

          {searchLoad ? (
            <div className="flex justify-center py-16">
              <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
            </div>
          ) : searchErr ? (
            <Card className="bg-red-50 text-center">
              <p className="text-red-700 py-6">{t('kb.searchError')}</p>
              <Button variant="outline" size="sm" onClick={doSearch}>
                {t('common.tryAgain')}
              </Button>
            </Card>
          ) : search.length === 0 ? (
            <Card className="bg-gray-50 text-center py-8">
              <p className="text-gray-600 mb-2">{t('kb.noSearchResults')}</p>
              <Link to="/kb/ask">
                <Button variant="outline" size="sm">
                  {t('kb.askQuestion')}
                </Button>
              </Link>
            </Card>
          ) : (
            <div className="grid gap-4">
              {mode === 'semantic' && (
                <div className="bg-blue-50 text-blue-800 p-3 rounded-lg text-sm">
                  <p className="font-medium">{t('kb.semanticSearchInfo')}</p>
                  <p>{t('kb.semanticSearchDetail')}</p>
                </div>
              )}
              {search.map((a) => (
                <KBArticleCard key={a.id} article={a} showScore expandable />
              ))}
            </div>
          )}
        </section>
      )}

      {/* Main Grid */}
      {!currentQuery && (
        <section className="grid gap-8 md:grid-cols-3">
          {/* Categories */}
          <Card title={t('kb.categories')} className="order-2 md:order-1">
            {globalLoading ? (
              <p className="text-center py-4">{t('common.loading')}</p>
            ) : categories.length === 0 ? (
              <p className="text-center py-4">{t('kb.noCategories')}</p>
            ) : (
              <div className="grid gap-3" style={{ gridTemplateColumns: 'repeat(auto-fill,minmax(160px,1fr))' }}>
                {categories.map((c) => (
                  <Link
                    key={c.id}
                    to={`/kb/categories/${c.slug}`}
                    className="block bg-gray-50 hover:bg-gray-100 p-3 rounded-lg"
                  >
                    <span className="font-medium text-blue-700">{c.name}</span>
                    <p className="text-xs text-gray-500 mt-1">
                      {c.article_count} {t('kb.articles')}
                    </p>
                  </Link>
                ))}
              </div>
            )}
          </Card>

          {/* Recent Articles */}
          <Card title={t('kb.recentArticles')} className="md:col-span-2 order-1 md:order-2">
            {globalLoading ? (
              <p className="text-center py-4">{t('common.loading')}</p>
            ) : recent.articles.length === 0 ? (
              <p className="text-center py-4">{t('kb.noArticles')}</p>
            ) : (
              <div className="space-y-4">
                {recent.articles.map((a) => (
                  <KBArticleCard key={a.id} article={a} expandable={false} />
                ))}
              </div>
            )}
          </Card>

          {/* Popular Articles */}
          {/* <Card title={t('kb.popularArticles')} className="md:col-span-2">
            {globalLoading ? (
              <p className="text-center py-4">{t('common.loading')}</p>
            ) : popular.articles.length === 0 ? (
              <p className="text-center py-4">{t('kb.noArticles')}</p>
            ) : (
              <div className="space-y-4">
                {popular.articles.map((a) => (
                  <KBArticleCard key={a.id} article={a} expandable={false} />
                ))}
              </div>
            )}
          </Card> */}

          {/* Popular Tags */}
          {/* <Card title={t('kb.popularTags')} className="order-3">
            {globalLoading ? (
              <p className="text-center py-4">{t('common.loading')}</p>
            ) : tags.length === 0 ? (
              <p className="text-center py-4">{t('kb.noTags')}</p>
            ) : (
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Link
                    key={tag.id}
                    to={`/kb/articles?tag=${encodeURIComponent(tag.name)}`}
                    className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                  >
                    {tag.name}
                    <span className="ml-1 text-xs">({tag.article_count})</span>
                  </Link>
                ))}
              </div>
            )}
          </Card> */}
        </section>
      )}

      {/* Ask CTA */}
      <section className="bg-gradient-to-r from-green-100 to-blue-100 rounded-lg p-8 flex flex-col md:flex-row items-center justify-between gap-6 shadow-inner">
        <div>
          <h2 className="text-2xl font-semibold text-gray-800 mb-1">{t('kb.askQuestion')}</h2>
          <p className="text-gray-600 max-w-lg">{t('kb.askQuestionDescription')}</p>
        </div>
        <Link to="/kb/ask">
          <Button size="lg">{t('kb.askNow')}</Button>
        </Link>
      </section>
    </div>
  );
};

export default KBHomePage;
