import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'

import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import KBArticleContent from '../../components/kb/KBArticleContent'
import {
  getArticleBySlug,
  getRelatedArticles,
  recordFeedback
} from '../../services/kbService'
import useAuthStore from '../../store/authStore'

const KBArticlePage = () => {
  const { t } = useTranslation()
  const { slug } = useParams()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { isAuthenticated, user } = useAuthStore()

  const [feedback, setFeedback] = useState(null)
  const [feedbackComment, setFeedbackComment] = useState('')
  const [showCommentForm, setShowCommentForm] = useState(false)

  // Get article with slug
  const {
    data: article,
    isLoading: articleLoading,
    error: articleError,
    refetch: refetchArticle
  } = useQuery(
    ['kb-article', slug],
    () => getArticleBySlug(slug, true), // increment view
    { refetchOnWindowFocus: false }
  )

  // Get related articles
  const {
    data: relatedArticles = [],
    isLoading: relatedLoading
  } = useQuery(
    ['kb-related-articles', article?.id],
    () => getRelatedArticles(article.id, 5),
    {
      enabled: !!article?.id,
      refetchOnWindowFocus: false
    }
  )

  // Feedback mutation
  const feedbackMutation = useMutation(
    ({ articleId, feedbackData }) => recordFeedback(articleId, feedbackData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['kb-article', slug])
      }
    }
  )

  useEffect(() => {
    // Scroll to top when article changes
    window.scrollTo(0, 0)
  }, [slug])

  const handleFeedback = (isHelpful) => {
    setFeedback(isHelpful)
    setShowCommentForm(true)

    // Submit feedback without comment
    feedbackMutation.mutate({
      articleId: article.id,
      feedbackData: { helpful: isHelpful }
    })
  }

  const handleCommentSubmit = (e) => {
    e.preventDefault()

    if (feedbackComment.trim()) {
      feedbackMutation.mutate({
        articleId: article.id,
        feedbackData: {
          helpful: feedback,
          comment: feedbackComment
        }
      })

      setFeedbackComment('')
      setShowCommentForm(false)
    }
  }

  if (articleLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    )
  }

  if (articleError || !article) {
    return (
      <Card>
        <div className="text-center py-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('kb.articleNotFound')}</h2>
          <p className="text-gray-600 mb-4">{t('kb.articleNotFoundDescription')}</p>
          <Button onClick={() => navigate('/kb')} variant="outline">
            {t('kb.backToKnowledgeBase')}
          </Button>
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-10">
      {/* Hero / Article Header */}
      <section className="bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-8 shadow">
        {/* Breadcrumb */}
        <div className="flex items-center text-sm mb-4">
          <Link to="/kb" className="text-blue-600 hover:text-blue-800">
            {t('kb.home')}
          </Link>
          <span className="mx-2 text-gray-500">/</span>
          {article.category_name && (
            <>
              <Link
                to={`/kb/categories/${article.category_id}`}
                className="text-blue-600 hover:text-blue-800"
              >
                {article.category_name}
              </Link>
              <span className="mx-2 text-gray-500">/</span>
            </>
          )}
          <span className="text-gray-500 truncate">
            {article.title}
          </span>
        </div>

        <h1 className="text-3xl md:text-4xl font-extrabold text-gray-900 mb-2">{article.title}</h1>

        {/* Metadata */}
        <div className="flex flex-wrap items-center text-sm text-gray-600 mb-2">
          <span>
            {t('kb.published')}: {new Date(article.created_at).toLocaleDateString()}
          </span>
          {article.updated_at !== article.created_at && (
            <>
              <span className="mx-2">•</span>
              <span>
                {t('kb.updated')}: {new Date(article.updated_at).toLocaleDateString()}
              </span>
            </>
          )}
          <span className="mx-2">•</span>
          <span>
            {article.view_count} {t('kb.views')}
          </span>
        </div>

        {/* Tags */}
        {article.tags && article.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {article.tags.map((tag, index) => (
              <Link
                key={index}
                to={`/kb/articles?tag=${encodeURIComponent(tag)}`}
                className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200"
              >
                {tag}
              </Link>
            ))}
          </div>
        )}
      </section>

      {/* Article content */}
      <Card className="overflow-hidden">
        <KBArticleContent content={article.content} />
      </Card>

      {/* Feedback section */}
      <Card className="bg-gradient-to-r from-blue-50 to-green-50">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">
          {t('kb.wasThisHelpful')}
        </h3>

        {!feedback ? (
          <div className="flex space-x-4">
            <Button
              onClick={() => handleFeedback(true)}
              variant="outline"
              size="sm"
              className="bg-white"
            >
              {t('common.yes')}
            </Button>
            <Button
              onClick={() => handleFeedback(false)}
              variant="outline"
              size="sm"
              className="bg-white"
            >
              {t('common.no')}
            </Button>
          </div>
        ) : (
          <div className="text-sm text-gray-600 mb-4">
            {feedback
              ? t('kb.thankYouForPositiveFeedback')
              : t('kb.thankYouForFeedback')
            }
          </div>
        )}

        {/* Comment form */}
        {showCommentForm && (
          <div className="mt-4">
            <h4 className="text-md font-medium text-gray-900 mb-2">
              {feedback
                ? t('kb.additionalComments')
                : t('kb.howCanWeImprove')
              }
            </h4>
            <form onSubmit={handleCommentSubmit}>
              <textarea
                className="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:border-blue-500 bg-white"
                rows="3"
                value={feedbackComment}
                onChange={(e) => setFeedbackComment(e.target.value)}
                placeholder={t('kb.feedbackPlaceholder')}
              ></textarea>
              <div className="flex justify-end mt-2">
                <Button
                  type="submit"
                  size="sm"
                  disabled={!feedbackComment.trim() || feedbackMutation.isLoading}
                >
                  {feedbackMutation.isLoading
                    ? t('common.submitting')
                    : t('common.submit')
                  }
                </Button>
              </div>
            </form>
          </div>
        )}
      </Card>

      {/* Related articles */}
      {relatedArticles.length > 0 && (
        <Card title={t('kb.relatedArticles')}>
          <div className="space-y-4">
            {relatedArticles.map(article => (
              <div key={article.id} className="p-3 hover:bg-gray-50 rounded-lg border border-gray-100">
                <Link
                  to={`/kb/articles/${article.slug}`}
                  className="block"
                >
                  <h3 className="text-md font-medium text-blue-600 hover:text-blue-800">
                    {article.title}
                  </h3>
                  {article.summary && (
                    <div className="mt-1 line-clamp-2">
                      <KBArticleContent
                        content={article.summary}
                        isPreview={true}
                      />
                    </div>
                  )}
                </Link>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => navigate(-1)}
          className="bg-white"
        >
          {t('common.back')}
        </Button>

        <Link to="/kb">
          <Button variant="outline" className="bg-white">
            {t('kb.backToKnowledgeBase')}
          </Button>
        </Link>
      </div>
    </div>
  )
}

export default KBArticlePage