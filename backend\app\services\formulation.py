# In app/services/formulation.py

import logging
from ortools.linear_solver import pywraplp
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime
import math

logger = logging.getLogger(__name__)

# Helper function to map OR-Tools LP status to a readable string
def get_lp_status_string(status_code):
    """Maps pywraplp status codes to human-readable strings."""
    status_map = {
        pywraplp.Solver.OPTIMAL: 'OPTIMAL',
        pywraplp.Solver.FEASIBLE: 'FEASIBLE',
        pywraplp.Solver.INFEASIBLE: 'INFEASIBLE',
        pywraplp.Solver.UNBOUNDED: 'UNBOUNDED',
        pywraplp.Solver.ABNORMAL: 'ABNORMAL',
        pywraplp.Solver.MODEL_INVALID: 'MODEL_INVALID',
        pywraplp.Solver.NOT_SOLVED: 'NOT_SOLVED',
    }
    return status_map.get(status_code, 'UNKNOWN_STATUS_CODE')

# Define common energy nutrient names (adjust if needed - used to infer units if not provided)
ENERGY_NUTRIENTS = {'NEL', 'NEM', 'NEg', 'ME', 'DE'}

def log_conflicting_constraints_lp(
    run_id: str,
    feeds_data: List[Dict[str, Any]],
    constraints_data: List[Dict[str, Any]],
    feed_ids_list: List[int],
    feed_names: Dict[int, str],
    feed_min_frac: Dict[int, float], # Renamed from perc to frac for clarity
    feed_max_frac: Dict[int, float], # Renamed from perc to frac for clarity
    feed_dm_frac: Dict[int, float], # Renamed from values to frac
    feed_nutrient_values_dm: Dict[str, Dict[int, float]], # Renamed from values
    nutrient_name_mappings: Dict[str, Dict[str, str]] = None
) -> List[Dict[str, str]]:
    """
    Identifies potential basic conflicting constraints for an infeasible LP model.
    Compares constraint values (converted to fractions if %) with feed nutrient fractions.
    Logs values in user-friendly format (e.g., percentages).

    Args:
        run_id: Unique identifier for the current run
        feeds_data: Original feed data
        constraints_data: Original constraint data
        feed_ids_list: Processed feed ids
        feed_names: Feed names by id
        feed_min_frac: Minimum inclusion fraction (0-1) by feed id
        feed_max_frac: Maximum inclusion fraction (0-1) by feed id
        feed_dm_frac: Dry matter fraction (0-1) by feed id
        feed_nutrient_values_dm: Nutrient values (as fraction if %, per kg DM) by nutrient name and feed id

    Returns:
        A list of dictionaries describing potential conflicts.
    """
    logger.warning(f"[{run_id}] Analyzing infeasible LP model for basic conflicting constraints...")
    conflicts = []

    # Check 1: Incompatible feed inclusion constraints
    total_min_percent = sum(feed_min_frac.get(fid, 0.0) for fid in feed_ids_list) * 100
    if total_min_percent > 100.0 + 1e-6: # Add tolerance for float comparison
        conflict_feeds = [f"{feed_names.get(fid, fid)} (min: {feed_min_frac.get(fid, 0)*100:.2f}%)"
                          for fid in feed_ids_list if feed_min_frac.get(fid, 0) > 0]
        msg = (f"Feed Inclusion Minimums: The total minimum inclusion ({total_min_percent:.2f}%) "
               f"exceeds 100%. Conflicting feeds: {', '.join(conflict_feeds)}")
        logger.error(f"[{run_id}] CONFLICT: {msg}")
        conflicts.append({
            "tag": "Feed Minimums",
            "detail": msg,
            "total_min_percent": total_min_percent,
            "conflict_feeds": conflict_feeds
        })


    # Check 2: Individual feed min > max
    for feed_id in feed_ids_list:
        min_f = feed_min_frac.get(feed_id, 0.0)
        max_f = feed_max_frac.get(feed_id, 1.0)
        if min_f > max_f + 1e-6: # Add tolerance
            msg = (f"Feed '{feed_names.get(feed_id, feed_id)}': Minimum inclusion ({min_f*100:.2f}%) "
                   f"> maximum inclusion ({max_f*100:.2f}%)")
            logger.error(f"[{run_id}] CONFLICT: {msg}")
            conflicts.append({
                "tag": f"Feed Min/Max ({feed_names.get(feed_id, feed_id)})",
                "detail": msg,
                "feed_name": feed_names.get(feed_id, str(feed_id)),
                "feed_id": feed_id,
                "min_inclusion": min_f*100,
                "max_inclusion": max_f*100
            })

    # Check 3: Nutrient min > max
    for constraint in constraints_data:
        nutrient_name = constraint.get('nutrient_name')
        min_val_str = constraint.get('min_value')
        max_val_str = constraint.get('max_value')

        if nutrient_name and min_val_str is not None and max_val_str is not None:
            try:
                min_req_display = float(min_val_str)
                max_req_display = float(max_val_str)
                if min_req_display > max_req_display + 1e-6: # Add tolerance
                     unit = constraint.get('unit', '') # Get unit if available
                     msg = (f"Nutrient '{nutrient_name}': Minimum requirement ({min_req_display} {unit}) "
                            f"> maximum requirement ({max_req_display} {unit})")
                     logger.error(f"[{run_id}] CONFLICT: {msg}")

                     # Get Chinese nutrient name if available
                     nutrient_name_zh = None
                     if nutrient_name_mappings and nutrient_name in nutrient_name_mappings:
                         nutrient_name_zh = nutrient_name_mappings[nutrient_name].get('name_zh')

                     conflict_data = {
                         "tag": f"Nutrient Min/Max ({nutrient_name})",
                         "detail": msg,
                         "nutrient_name": nutrient_name,
                         "min_req": min_req_display,
                         "max_req": max_req_display,
                         "unit": unit
                     }

                     # Add Chinese nutrient name if available
                     if nutrient_name_zh:
                         conflict_data["nutrient_name_zh"] = nutrient_name_zh

                     conflicts.append(conflict_data)
            except (ValueError, TypeError):
                logger.warning(f"[{run_id}] Could not parse min/max float for nutrient {nutrient_name} during conflict check.")


    # Check 4: Basic nutrient achievability (using extremes) - CORRECTED
    for constraint in constraints_data:
        nutrient_name = constraint.get('nutrient_name')
        if not nutrient_name or nutrient_name not in feed_nutrient_values_dm:
             continue

        min_req_str = constraint.get('min_value')
        max_req_str = constraint.get('max_value')
        nutrient_feed_vals_frac = feed_nutrient_values_dm[nutrient_name] # Values are fractions
        available_feed_ids = [fid for fid in feed_ids_list if fid in nutrient_feed_vals_frac]

        if not available_feed_ids:
            continue # Cannot check if no feed provides it

        # Find min/max possible nutrient concentration among available feeds (on DM basis)
        # These values are already fractions (e.g., 0.478 for 47.8%)
        possible_values_frac = [nutrient_feed_vals_frac[fid] for fid in available_feed_ids]
        min_possible_conc_frac = min(possible_values_frac)
        max_possible_conc_frac = max(possible_values_frac)

        # Determine unit and if the constraint value represents a percentage
        unit = constraint.get('unit', '%') # Default to %
        # Assume constraint values need conversion if unit is '%'
        is_percentage_constraint = (unit == '%')

        try:
            # --- Check Minimum Requirement ---
            if min_req_str is not None:
                min_req_display = float(min_req_str) # Value as given in constraint (e.g., 20.0)

                # Convert constraint value to fraction IF it's a percentage, for correct comparison
                min_req_frac = min_req_display / 100.0 if is_percentage_constraint else min_req_display

                # Compare FRACTION requirement with max possible FRACTION concentration
                if min_req_frac > max_possible_conc_frac + 1e-6:
                    # For logging, display constraint value as is, but convert feed concentration
                    # back to the constraint's scale (percentage or original unit)
                    display_conc_feed = max_possible_conc_frac * 100.0 if is_percentage_constraint else max_possible_conc_frac

                    # Get Chinese nutrient name if available
                    nutrient_name_zh = None
                    if nutrient_name_mappings and nutrient_name in nutrient_name_mappings:
                        nutrient_name_zh = nutrient_name_mappings[nutrient_name].get('name_zh')

                    msg = (f"Nutrient '{nutrient_name}': Minimum requirement ({min_req_display:.2f} {unit} DM) " # Display original req
                           f"might be unachievable. Highest concentration in available feeds is "
                           f"{display_conc_feed:.2f} {unit} DM.") # Display feed conc converted to % scale
                    logger.warning(f"[{run_id}] POTENTIAL CONFLICT: {msg}")

                    conflict_data = {
                        "tag": f"Nutrient Min ({nutrient_name})",
                        "detail": msg,
                        "type": "potential",
                        "nutrient_name": nutrient_name,
                        "min_req": min_req_display,
                        "unit": unit,
                        "max_conc": display_conc_feed
                    }

                    # Add Chinese nutrient name if available
                    if nutrient_name_zh:
                        conflict_data["nutrient_name_zh"] = nutrient_name_zh

                    conflicts.append(conflict_data)

            # --- Check Maximum Requirement ---
            if max_req_str is not None:
                max_req_display = float(max_req_str) # Value as given in constraint

                # Convert constraint value to fraction IF it's a percentage, for correct comparison
                max_req_frac = max_req_display / 100.0 if is_percentage_constraint else max_req_display

                 # Compare FRACTION requirement with min possible FRACTION concentration
                if max_req_frac < min_possible_conc_frac - 1e-6:
                    # For logging, display constraint value as is, but convert feed concentration
                    # back to the constraint's scale (percentage or original unit)
                    display_conc_feed = min_possible_conc_frac * 100.0 if is_percentage_constraint else min_possible_conc_frac

                    # Get Chinese nutrient name if available
                    nutrient_name_zh = None
                    if nutrient_name_mappings and nutrient_name in nutrient_name_mappings:
                        nutrient_name_zh = nutrient_name_mappings[nutrient_name].get('name_zh')

                    msg = (f"Nutrient '{nutrient_name}': Maximum requirement ({max_req_display:.2f} {unit} DM) " # Display original req
                           f"might be unachievable. Lowest concentration in available feeds is "
                           f"{display_conc_feed:.2f} {unit} DM.") # Display feed conc converted to % scale
                    logger.warning(f"[{run_id}] POTENTIAL CONFLICT: {msg}")

                    conflict_data = {
                        "tag": f"Nutrient Max ({nutrient_name})",
                        "detail": msg,
                        "type": "potential",
                        "nutrient_name": nutrient_name,
                        "max_req": max_req_display,
                        "unit": unit,
                        "min_conc": display_conc_feed
                    }

                    # Add Chinese nutrient name if available
                    if nutrient_name_zh:
                        conflict_data["nutrient_name_zh"] = nutrient_name_zh

                    conflicts.append(conflict_data)
        except (ValueError, TypeError):
             # Error already logged in Check 3 if parsing failed initially
             pass

    # If no specific basic conflicts found, add a general message
    if not conflicts:
        general_msg = ("The combination of constraints might be too restrictive. "
                       "Consider relaxing nutrient requirements or feed inclusion limits.")
        logger.warning(f"[{run_id}] No obvious single conflicts found. {general_msg}")
        conflicts.append({"tag": "General Infeasibility", "detail": general_msg})

    return conflicts


def formulate_ration(
    feeds_data: List[Dict[str, Any]],
    constraints_data: List[Dict[str, Any]],
    time_limit_seconds: float = 10.0,
    optimization_objective : str = 'minimize_cost',
    nutrient_name_mappings: Dict[str, Dict[str, str]] = None
) -> Dict[str, Any]:
    """
    Formulates a ration using OR-Tools Linear Solver (pywraplp) with a
    floating-point, weight-based approach (kg As-Fed). Correctly handles
    percentage vs. fractional units between constraints and feed data.

    Args:
        feeds_data: List of feed dicts. Required: 'id', 'name', 'cost_per_kg',
                    'dry_matter_percentage' (0-100), 'nutrients' (dict: {name: value DM basis,
                    stored as fraction if it represents a percentage e.g., 0.16 for 16%}).
                    Optional: 'min_incl_perc' (0-100), 'max_incl_perc' (0-100).
        constraints_data: List of constraint dicts. Required: 'nutrient_name'.
                          Optional: 'min_value', 'max_value', 'unit'. Constraint values
                          (min/max) are assumed to be direct percentages (e.g., 20.0 for 20%)
                          if unit is '%', otherwise taken as is (e.g. Mcal/kg).
        time_limit_seconds: Solver time limit.
        optimization_objective: 'minimize_cost' or 'balance_nutrients'.

    Returns:
        dict: Formulation results including status, message, feed percentages,
              nutrient levels, and total cost.
    """
    run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    logger.info(f"[{run_id}] Starting ration formulation with LP (pywraplp). Objective: {optimization_objective}")
    result_template = {
        'status': 'error', 'message': '', 'solver_status': None,
        'conflicting_constraints': [], 'feeds': {}, 'nutrients': {},
        'total_cost': 0.0
    }

    # --- Input Validation ---
    if not isinstance(feeds_data, list) or not feeds_data:
        result_template['message'] = "Input 'feeds_data' must be a non-empty list."
        logger.error(f"[{run_id}] {result_template['message']}")
        return result_template
    if not isinstance(constraints_data, list):
        constraints_data = [] # Allow empty constraints

    try:
        # --- 1. Process Input Data ---
        feed_ids_list = []
        feed_names = {}
        feed_min_frac = {}  # {feed_id: min_fraction_as_fed}
        feed_max_frac = {}  # {feed_id: max_fraction_as_fed}
        feed_costs_per_kg = {} # {feed_id: cost_per_kg_as_fed}
        feed_dm_frac = {}    # {feed_id: dry_matter_fraction}
        feed_nutrient_values_dm = {}  # {nutrient_name: {feed_id: value_per_kg_dm, as fraction}}

        # Find DMI constraint info (absolute kg)
        dmi_constraint_data = next((c for c in constraints_data if c.get('nutrient_name') == 'DMI'), None)
        min_dmi_kg = None
        max_dmi_kg = None
        if dmi_constraint_data:
            try:
                if dmi_constraint_data.get('min_value') is not None:
                    min_dmi_kg = float(dmi_constraint_data['min_value'])
                if dmi_constraint_data.get('max_value') is not None:
                    max_dmi_kg = float(dmi_constraint_data['max_value'])
                logger.info(f"[{run_id}] DMI constraint found: min={min_dmi_kg}, max={max_dmi_kg} kg")
            except (ValueError, TypeError):
                 logger.warning(f"[{run_id}] Invalid DMI min/max value: {dmi_constraint_data}. Ignoring DMI constraint.")
                 min_dmi_kg, max_dmi_kg = None, None

        # Process feed data
        for feed_data in feeds_data:
            feed_id = feed_data.get('id')
            if feed_id is None: continue
            try: feed_id = int(feed_id)
            except (ValueError, TypeError): continue

            feed_name = feed_data.get('name', f'Feed_{feed_id}')
            cost_per_kg = feed_data.get('cost_per_kg', 0.0)
            dm_percentage = feed_data.get('dry_matter_percentage') # Expected as 0-100
            min_incl_perc = feed_data.get('min_incl_perc', 0.0)
            max_incl_perc = feed_data.get('max_incl_perc', 100.0)
            nutrients_input = feed_data.get('nutrients', {})

            # --- Data Validation and Conversion ---
            try: cost_per_kg = float(cost_per_kg)
            except (ValueError, TypeError): cost_per_kg = 0.0

            if dm_percentage is None:
                 logger.error(f"[{run_id}] Missing 'dry_matter_percentage' for feed {feed_name} ({feed_id}). Skipping feed.")
                 continue
            try:
                dm_percentage = float(dm_percentage)
                if not (0 <= dm_percentage <= 100): raise ValueError("DM% out of range 0-100")
                feed_dm_frac[feed_id] = dm_percentage # already fraction
            except (ValueError, TypeError) as e:
                logger.error(f"[{run_id}] Invalid 'dry_matter_percentage' for feed {feed_name} ({feed_id}): {dm_percentage}. Error: {e}. Skipping feed.")
                continue

            try: min_incl_perc = max(0.0, float(min_incl_perc))
            except (ValueError, TypeError): min_incl_perc = 0.0
            try: max_incl_perc = min(100.0, float(max_incl_perc))
            except (ValueError, TypeError): max_incl_perc = 100.0

            if min_incl_perc > max_incl_perc:
                logger.warning(f"[{run_id}] Min inclusion ({min_incl_perc}%) > Max inclusion ({max_incl_perc}%) for feed {feed_name} ({feed_id}). Adjusting max to min.")
                max_incl_perc = min_incl_perc # Adjust to make feasible

            feed_ids_list.append(feed_id)
            feed_names[feed_id] = feed_name
            feed_costs_per_kg[feed_id] = cost_per_kg
            feed_min_frac[feed_id] = min_incl_perc / 100.0
            feed_max_frac[feed_id] = max_incl_perc / 100.0

            # Process nutrients (assuming they are DM basis and fractions if %)
            processed_nutrients_dm_frac = {}
            if isinstance(nutrients_input, dict):
                for name, value in nutrients_input.items():
                    if value is not None:
                        try: processed_nutrients_dm_frac[name] = float(value)
                        except (ValueError, TypeError): logger.warning(f"[{run_id}] Nutrient conversion warning for {name} in feed {feed_id}")
            elif isinstance(nutrients_input, list): # Handle alternative format if necessary
                for nutrient_info in nutrients_input:
                     if isinstance(nutrient_info, dict):
                        name = nutrient_info.get('nutrient_name')
                        value = nutrient_info.get('value')
                        if name and value is not None:
                             try: processed_nutrients_dm_frac[name] = float(value)
                             except (ValueError, TypeError): logger.warning(f"[{run_id}] Nutrient conversion warning for {name} in feed {feed_id}")
            else:
                 logger.warning(f"[{run_id}] Invalid 'nutrients' format for feed {feed_id}.")

            # Store nutrient fractions per kg DM
            for name, value_dm_frac in processed_nutrients_dm_frac.items():
                if name not in feed_nutrient_values_dm:
                    feed_nutrient_values_dm[name] = {}
                feed_nutrient_values_dm[name][feed_id] = value_dm_frac
        # End feed processing loop

        if not feed_ids_list:
            result_template['message'] = "No valid feeds could be processed from 'feeds_data'."
            logger.error(f"[{run_id}] {result_template['message']}")
            return result_template

        # --- 2. Model Setup ---
        solver = pywraplp.Solver.CreateSolver('GLOP')
        if not solver:
             logger.error(f"[{run_id}] Could not create GLOP solver.")
             result_template['message'] = "LP Solver (GLOP) not available."
             return result_template
        solver.SetTimeLimit(int(time_limit_seconds * 1000)) # Time limit in milliseconds

        # --- 3. Variables ---
        feed_vars_kg = {}
        for feed_id in feed_ids_list:
            var_name = f"kg_{feed_id}_{feed_names[feed_id]}"
            feed_vars_kg[feed_id] = solver.NumVar(0.0, solver.infinity(), var_name)

        # --- 4. Helper Expressions --- REMOVED

        # --- 5. Feed Percentage Constraints (As-Fed basis) ---
        for feed_id in feed_ids_list:
            min_frac = feed_min_frac[feed_id]
            max_frac = feed_max_frac[feed_id]

            # Min percentage: feed_kg[i] >= min_frac[i] * Sum(feed_kg[j])
            if min_frac > 1e-9:
                constraint = solver.Constraint(0.0, solver.infinity(), f"min_perc_{feed_id}")
                constraint.SetCoefficient(feed_vars_kg[feed_id], 1.0)
                for other_feed_id in feed_ids_list:
                    constraint.SetCoefficient(feed_vars_kg[other_feed_id], -min_frac)

            # Max percentage: feed_kg[i] <= max_frac[i] * Sum(feed_kg[j])
            if max_frac < 1.0 - 1e-9:
                constraint = solver.Constraint(-solver.infinity(), 0.0, f"max_perc_{feed_id}")
                constraint.SetCoefficient(feed_vars_kg[feed_id], 1.0)
                for other_feed_id in feed_ids_list:
                    constraint.SetCoefficient(feed_vars_kg[other_feed_id], -max_frac)

        # --- 6. DMI Constraint (Absolute Kg DM) ---
        if min_dmi_kg is not None:
            constraint = solver.Constraint(min_dmi_kg, solver.infinity(), "min_dmi")
            for feed_id in feed_ids_list:
                constraint.SetCoefficient(feed_vars_kg[feed_id], feed_dm_frac[feed_id])
            logger.info(f"[{run_id}] Added MIN DMI constraint: >= {min_dmi_kg} kg DM")

        if max_dmi_kg is not None:
            constraint = solver.Constraint(-solver.infinity(), max_dmi_kg, "max_dmi")
            for feed_id in feed_ids_list:
                constraint.SetCoefficient(feed_vars_kg[feed_id], feed_dm_frac[feed_id])
            logger.info(f"[{run_id}] Added MAX DMI constraint: <= {max_dmi_kg} kg DM")


        # --- 7. Nutrient Constraints (Concentration on DM basis) - CORRECTED ---
        nutrient_balance_targets = {} # Stores fractional targets for balance objective
        deviation_vars = {} # {nutrient_name: (over_dev_var, under_dev_var)}

        for constraint_info in constraints_data:
            nutrient_name = constraint_info.get('nutrient_name')

            if not nutrient_name or nutrient_name == 'DMI':
                continue

            if nutrient_name not in feed_nutrient_values_dm:
                logger.warning(f"[{run_id}] Nutrient constraint specified for '{nutrient_name}', but no feed provides this nutrient.")
                continue

            current_nutrient_feed_values_dm_frac = feed_nutrient_values_dm[nutrient_name] # These are fractions
            relevant_feed_ids = [fid for fid in feed_ids_list if fid in current_nutrient_feed_values_dm_frac]
            if not relevant_feed_ids:
                 logger.warning(f"[{run_id}] Nutrient constraint for '{nutrient_name}' cannot be applied as no included feeds have data for it.")
                 continue

            min_val_str = constraint_info.get('min_value')
            max_val_str = constraint_info.get('max_value')
            unit = constraint_info.get('unit', '%')
            is_percentage_constraint = (unit == '%')

            # --- Min nutrient concentration constraint ---
            # Reformulated: Sum( feed_kg[i]*dm[i]*nutr_frac[i] ) - min_req_frac * Sum( feed_kg[j]*dm[j] ) >= 0
            if min_val_str is not None:
                try:
                    min_req_display = float(min_val_str) # Value as provided (e.g., 20.0)
                    # Convert to fraction for calculation if needed
                    min_req_calc_frac = min_req_display / 100.0 if is_percentage_constraint else min_req_display

                    constraint = solver.Constraint(0.0, solver.infinity(), f"min_nutr_{nutrient_name}")
                    # Calculate combined coefficient for each feed
                    for feed_id in feed_ids_list:
                        coeff = 0.0
                        # Nutrient amount term (using feed's nutrient fraction)
                        if feed_id in relevant_feed_ids:
                            coeff += feed_dm_frac[feed_id] * current_nutrient_feed_values_dm_frac[feed_id]
                        # Requirement term (using constraint's requirement as fraction)
                        coeff -= min_req_calc_frac * feed_dm_frac[feed_id] # USE FRACTIONAL REQUIREMENT
                        # Set coefficient if non-negligible
                        if abs(coeff) > 1e-12:
                            constraint.SetCoefficient(feed_vars_kg[feed_id], coeff)

                    logger.info(f"[{run_id}] Added MIN NUTRIENT constraint for {nutrient_name}: >= {min_req_display} {unit} DM")
                except (ValueError, TypeError):
                     logger.warning(f"[{run_id}] Invalid min value for {nutrient_name}: {min_val_str}")

            # --- Max nutrient concentration constraint ---
            # Reformulated: Sum( feed_kg[i]*dm[i]*nutr_frac[i] ) - max_req_frac * Sum( feed_kg[j]*dm[j] ) <= 0
            if max_val_str is not None:
                try:
                    max_req_display = float(max_val_str) # Value as provided (e.g., 30.0)
                    # Convert to fraction for calculation if needed
                    max_req_calc_frac = max_req_display / 100.0 if is_percentage_constraint else max_req_display

                    constraint = solver.Constraint(-solver.infinity(), 0.0, f"max_nutr_{nutrient_name}")
                     # Calculate combined coefficient for each feed
                    for feed_id in feed_ids_list:
                         coeff = 0.0
                         # Nutrient amount term (using feed's nutrient fraction)
                         if feed_id in relevant_feed_ids:
                             coeff += feed_dm_frac[feed_id] * current_nutrient_feed_values_dm_frac[feed_id]
                         # Requirement term (using constraint's requirement as fraction)
                         coeff -= max_req_calc_frac * feed_dm_frac[feed_id] # USE FRACTIONAL REQUIREMENT
                         # Set coefficient if non-negligible
                         if abs(coeff) > 1e-12:
                             constraint.SetCoefficient(feed_vars_kg[feed_id], coeff)

                    logger.info(f"[{run_id}] Added MAX NUTRIENT constraint for {nutrient_name}: <= {max_req_display} {unit} DM")
                except (ValueError, TypeError):
                     logger.warning(f"[{run_id}] Invalid max value for {nutrient_name}: {max_val_str}")

            # --- Store info for 'balance_nutrients' objective - CORRECTED ---
            if optimization_objective == 'balance_nutrients' and min_val_str is not None and max_val_str is not None:
                 try:
                    min_req_display = float(min_val_str)
                    max_req_display = float(max_val_str)

                    # Convert constraint bounds to fractions for calculation
                    min_req_calc_frac = min_req_display / 100.0 if is_percentage_constraint else min_req_display
                    max_req_calc_frac = max_req_display / 100.0 if is_percentage_constraint else max_req_display

                    # Compare using fractions
                    if min_req_calc_frac < max_req_calc_frac - 1e-9: # Ensure target range exists
                        # Calculate target concentration as a fraction
                        target_conc_calc_frac = (min_req_calc_frac + max_req_calc_frac) / 2.0
                        nutrient_balance_targets[nutrient_name] = target_conc_calc_frac # Store fractional target

                        over_dev_var = solver.NumVar(0.0, solver.infinity(), f"over_dev_{nutrient_name}")
                        under_dev_var = solver.NumVar(0.0, solver.infinity(), f"under_dev_{nutrient_name}")
                        deviation_vars[nutrient_name] = (over_dev_var, under_dev_var)

                        # Add deviation constraint:
                        # Sum( kg[i]*dm[i]*nutr[i] ) - target_frac * Sum( kg[j]*dm[j] ) - over + under = 0
                        dev_constraint = solver.Constraint(0.0, 0.0, f"dev_bal_{nutrient_name}")
                        # Calculate combined coefficient for each feed
                        for feed_id in feed_ids_list:
                             coeff = 0.0
                             # Nutrient amount term
                             if feed_id in relevant_feed_ids:
                                 coeff += feed_dm_frac[feed_id] * current_nutrient_feed_values_dm_frac[feed_id]
                             # Target amount term (using fractional target)
                             coeff -= target_conc_calc_frac * feed_dm_frac[feed_id] # USE FRACTIONAL TARGET
                             if abs(coeff) > 1e-12:
                                 dev_constraint.SetCoefficient(feed_vars_kg[feed_id], coeff)

                        # Deviation var terms
                        dev_constraint.SetCoefficient(over_dev_var, -1.0)
                        dev_constraint.SetCoefficient(under_dev_var, 1.0)

                        # Log the original display target for user clarity
                        target_conc_display = (min_req_display + max_req_display) / 2.0
                        logger.info(f"[{run_id}] Added balancing constraint for {nutrient_name} (Target: {target_conc_display:.2f} {unit} DM)")

                 except (ValueError, TypeError):
                    logger.warning(f"[{run_id}] Cannot balance nutrient {nutrient_name} due to invalid min/max values.")


        # --- 8. Objective Function ---
        objective = solver.Objective()
        objective.SetMinimization() # Default to minimization

        if optimization_objective == 'minimize_cost':
            logger.info(f"[{run_id}] Setting objective: Minimize Cost")
            for feed_id in feed_ids_list:
                objective.SetCoefficient(feed_vars_kg[feed_id], feed_costs_per_kg[feed_id])
        elif optimization_objective == 'balance_nutrients':
             logger.info(f"[{run_id}] Setting objective: Balance Nutrients (Minimize Deviations)")
             if not deviation_vars:
                 logger.warning(f"[{run_id}] Balance objective selected, but no valid nutrients found to balance. Falling back to cost minimization.")
                 for feed_id in feed_ids_list:
                     objective.SetCoefficient(feed_vars_kg[feed_id], feed_costs_per_kg[feed_id])
             else:
                 # Minimize Sum(over_deviation + under_deviation)
                 for nutrient_name, (over_dev, under_dev) in deviation_vars.items():
                     objective.SetCoefficient(over_dev, 1.0)
                     objective.SetCoefficient(under_dev, 1.0)
        else:
            logger.warning(f"[{run_id}] Unknown optimization objective '{optimization_objective}'. Defaulting to Minimize Cost.")
            for feed_id in feed_ids_list:
                objective.SetCoefficient(feed_vars_kg[feed_id], feed_costs_per_kg[feed_id])


        # --- 9. Solve ---
        logger.info(f"[{run_id}] Solving the LP model with {solver.SolverVersion()}...")
        status = solver.Solve()
        status_str = get_lp_status_string(status)
        logger.info(f"[{run_id}] Solver status: {status_str}, Time: {solver.wall_time()/1000.0:.2f}s, Iterations: {solver.iterations()}")
        result_template['solver_status'] = status_str


        # --- 10. Process Results ---
        if status == pywraplp.Solver.OPTIMAL or status == pywraplp.Solver.FEASIBLE:
            result_template['status'] = 'success'
            result_template['message'] = f"Solution found ({status_str}). Objective value: {objective.Value():.2f}"

            # Get solved total weights
            solved_total_kg_af = sum(var.solution_value() for var in feed_vars_kg.values())
            solved_total_kg_dm = sum(feed_vars_kg[fid].solution_value() * feed_dm_frac[fid] for fid in feed_ids_list)

            # Handle potential near-zero solutions robustly
            if solved_total_kg_af < 1e-9:
                 logger.warning(f"[{run_id}] Solution found but total As-Fed weight is near zero ({solved_total_kg_af:.4e}). Results might be unreliable or represent an empty ration.")
                 result_template['status'] = 'warning'
                 result_template['message'] += " Total weight is near zero."
                 # Set floors for division, but expect potentially zero percentages/amounts
                 solved_total_kg_af = max(solved_total_kg_af, 1e-12)
            if solved_total_kg_dm < 1e-9:
                 solved_total_kg_dm = max(solved_total_kg_dm, 1e-12)


            # Process feed results
            solved_feeds = {}
            calculated_total_cost = 0.0

            # First, calculate the total cost per kg of the ration
            for feed_id in feed_ids_list:
                solved_kg = feed_vars_kg[feed_id].solution_value()
                solved_kg = max(0.0, solved_kg) # Ensure non-negative

                inclusion_perc = (solved_kg / solved_total_kg_af) * 100.0
                cost_contribution = solved_kg * feed_costs_per_kg[feed_id]
                calculated_total_cost += cost_contribution

                solved_feeds[feed_id] = {
                    'feed_name': feed_names.get(feed_id, f'Feed_{feed_id}'),
                    'inclusion_percentage': round(inclusion_perc, 2),
                    'cost_contribution': round(cost_contribution, 4),
                    'kg_as_fed': round(solved_kg, 4)
                }

            # Calculate cost per kg of the ration (as-fed basis)
            cost_per_kg_af = calculated_total_cost / solved_total_kg_af if solved_total_kg_af > 1e-9 else 0.0

            # Calculate cost per kg of the ration (dry matter basis)
            cost_per_kg_dm = calculated_total_cost / solved_total_kg_dm if solved_total_kg_dm > 1e-9 else 0.0

            # Get DMI value from constraints or use the calculated total DM
            dmi_value = solved_total_kg_dm
            if dmi_constraint_data and dmi_constraint_data.get('min_value') is not None:
                try:
                    dmi_value = float(dmi_constraint_data.get('min_value'))
                except (ValueError, TypeError):
                    # Fall back to calculated value if constraint value is invalid
                    pass

            # Calculate actual cost per animal per day based on DMI
            actual_total_cost = cost_per_kg_dm * dmi_value

            # Update feed entries with actual costs based on their DM contribution
            for feed_id in feed_ids_list:
                feed_dm_kg = feed_vars_kg[feed_id].solution_value() * feed_dm_frac[feed_id]
                feed_dm_ratio = feed_dm_kg / solved_total_kg_dm if solved_total_kg_dm > 1e-9 else 0.0
                actual_cost = feed_dm_ratio * actual_total_cost

                solved_feeds[feed_id]['actual_cost'] = round(actual_cost, 4)

            result_template['feeds'] = solved_feeds
            result_template['total_cost'] = round(calculated_total_cost, 4)
            result_template['actual_total_cost'] = round(actual_total_cost, 4)
            result_template['cost_per_kg_dm'] = round(cost_per_kg_dm, 4)
            result_template['cost_per_kg_af'] = round(cost_per_kg_af, 4)


            # Calculate final nutrient values
            calculated_nutrients = {}

            # Report actual DMI
            dmi_min_report, dmi_max_report = None, None
            if dmi_constraint_data:
                dmi_min_report = dmi_constraint_data.get('min_value')
                dmi_max_report = dmi_constraint_data.get('max_value')

            calculated_nutrients['DMI'] = {
                'actual_value': round(solved_total_kg_dm, 4),
                'min_value': dmi_min_report,
                'max_value': dmi_max_report,
                'unit': 'kg'
            }

            # Calculate other nutrient concentrations (DM Basis)
            for nutrient_name, feed_values_dict_dm_frac in feed_nutrient_values_dm.items():
                 total_nutrient_amount_dm = sum(
                     feed_vars_kg[feed_id].solution_value() * feed_dm_frac[feed_id] * feed_values_dict_dm_frac[feed_id]
                     for feed_id in feed_ids_list if feed_id in feed_values_dict_dm_frac # Sum over all feeds that have the nutrient
                 )

                 actual_concentration_dm_frac = total_nutrient_amount_dm / solved_total_kg_dm if solved_total_kg_dm > 1e-9 else 0.0

                 # Get original min/max bounds and unit from input constraints for reporting
                 min_val_report, max_val_report, report_unit = None, None, '%' # Default unit
                 nutrient_constraint = next((c for c in constraints_data if c.get('nutrient_name') == nutrient_name), None)
                 is_percentage_report = True # Assume percentage unless unit suggests otherwise
                 if nutrient_constraint:
                     min_val_report = nutrient_constraint.get('min_value')
                     max_val_report = nutrient_constraint.get('max_value')
                     report_unit = nutrient_constraint.get('unit', report_unit)
                     if report_unit != '%':
                         is_percentage_report = False

                 # Convert calculated fraction back to percentage for display if needed
                 actual_value_display = actual_concentration_dm_frac * 100.0 if is_percentage_report else actual_concentration_dm_frac

                 calculated_nutrients[nutrient_name] = {
                     'actual_value': round(actual_value_display, 4),
                     'min_value': min_val_report,
                     'max_value': max_val_report,
                     'unit': f"{report_unit} DM" # Indicate DM basis
                 }
            result_template['nutrients'] = calculated_nutrients

        elif status == pywraplp.Solver.INFEASIBLE:
            result_template['status'] = 'failed'
            result_template['message'] = "No feasible solution exists with the given constraints."
            logger.warning(f"[{run_id}] Model is INFEASIBLE.")
            result_template['conflicting_constraints'] = log_conflicting_constraints_lp(
                 run_id, feeds_data, constraints_data, feed_ids_list, feed_names,
                 feed_min_frac, feed_max_frac, feed_dm_frac, feed_nutrient_values_dm,
                 nutrient_name_mappings
            )

        elif status == pywraplp.Solver.UNBOUNDED:
             result_template['status'] = 'failed'
             result_template['message'] = "The problem is unbounded. Constraints may be missing or too loose (e.g., max limits)."
             logger.error(f"[{run_id}] Model is UNBOUNDED.")
             result_template['conflicting_constraints'] = log_conflicting_constraints_lp(
                 run_id, feeds_data, constraints_data, feed_ids_list, feed_names,
                 feed_min_frac, feed_max_frac, feed_dm_frac, feed_nutrient_values_dm,
                 nutrient_name_mappings
            )
        else: # ABNORMAL, MODEL_INVALID, NOT_SOLVED, etc.
            result_template['status'] = 'failed'
            result_template['message'] = f"Solver failed with status: {status_str}. Unable to find a solution."
            logger.error(f"[{run_id}] {result_template['message']}")
            if status == pywraplp.Solver.MODEL_INVALID:
                 result_template['message'] += " Check model constraints and variable definitions."
                 result_template['conflicting_constraints'] = log_conflicting_constraints_lp(
                     run_id, feeds_data, constraints_data, feed_ids_list, feed_names,
                     feed_min_frac, feed_max_frac, feed_dm_frac, feed_nutrient_values_dm,
                     nutrient_name_mappings
                 )

        return result_template

    except Exception as e:
        logger.exception(f"[{run_id}] Error during ration formulation: {e}", exc_info=True)
        result_template['message'] = f"An unexpected error occurred: {str(e)}"
        result_template['status'] = 'error'
        return result_template