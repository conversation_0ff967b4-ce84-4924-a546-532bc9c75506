// src/pages/rations/UnifiedRationPage.jsx
import { useState, useEffect } from 'react'
import { useNavigate, Link, useParams, useLocation } from 'react-router-dom'
import { useMutation, useQuery, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import i18n from 'i18next'
import Button from '../../components/ui/Button'
import Card from '../../components/ui/Card'
import { toast } from 'react-toastify'

// Import service functions
import { createRation, getRationById, updateRation, formulateRation, saveFormulationResult } from '../../services/rationService'
import { getFeeds } from '../../services/feedService'
import { getAnimalGroups } from '../../services/animalGroupService'
import { getNrcRequirements } from '../../services/nrcRequirementsService'
import { getNrcModelVersions } from '../../services/nrcCoefficientService'

// Import components
import RationFormulationLayout from '../../components/rations/RationFormulationLayout'
import AIAssistantOverlay from '../../components/rations/AIAssistantOverlay'

const UnifiedRationPage = () => {
  const { t } = useTranslation()
  const { id: routeId } = useParams()
  const navigate = useNavigate()
  const location = useLocation()
  const queryClient = useQueryClient()

  const isReformulateMode = location.pathname.includes('/formulate')
  const [isEditMode, setIsEditMode] = useState(!!routeId)

  // --- Primary Context State (Single Source of Truth) ---
  const [contextData, setContextData] = useState({
    ration_id: routeId || null,
    locale: i18n.language,
    current_formulation_state: {
      ration_name: '',
      description: '',
      animal_group_id: '',
      feeds_count: 0,
      constraints_count: 0
    },
    feed_data: [],
    constraints: [],
    message_history: [],
    formulation_result: null,
    formulation_attempts: 0
  })

  // --- Other state management ---
  const [errors, setErrors] = useState({})
  const [isFormulating, setIsFormulating] = useState(false)
  const [isSavingLoading, setIsSavingLoading] = useState(false)
  const [isSaved, setIsSaved] = useState(!!routeId)
  const [selectedNrcModelId, setSelectedNrcModelId] = useState('')

  // AI Assistant State
  const [aiAssistEnabled, setAiAssistEnabled] = useState(true)
  const [aiTriggerData, setAiTriggerData] = useState(null)

  // --- Data Fetching Queries ---
  const { data: modelVersionsData } = useQuery('nrc-model-versions', () => getNrcModelVersions(), {
    onSuccess: (data) => {
      if (data?.versions?.length > 0 && !selectedNrcModelId)
        setSelectedNrcModelId(data.versions[0].id.toString())
    }
  })

  const [optimizationSettings, setOptimizationSettings] = useState({
    objective: 'minimize_cost',
    maxIterations: 1000,
    tolerance: 0.001
  })

  const { data: feedsData = { feeds: [] }, isLoading: isLoadingFeeds } = useQuery('feeds', () => getFeeds(1, 100))
  const feeds = feedsData.feeds || []

  const { data: animalGroups = [], isLoading: isLoadingGroups } = useQuery('animal-groups', getAnimalGroups)

  // Fetch existing ration data if ID exists
  const { data: existingRation, isLoading: isLoadingRation, error: rationError } = useQuery(
    ['ration', contextData.ration_id],
    () => getRationById(contextData.ration_id),
    {
      enabled: !!contextData.ration_id,
      onSuccess: (data) => {
        if (data) {
          console.log("[UnifiedRationPage] Fetched existing ration data:", data)

          // Convert API data to context format
          const initialContext = {
            ration_id: data.id,
            locale: i18n.language,
            current_formulation_state: {
              ration_name: data.name || '',
              description: data.description || '',
              animal_group_id: data.animal_group_id || '',
              feeds_count: data.feeds?.length || 0,
              constraints_count: data.constraints?.length || 0,
              last_formulated_at: data.last_formulated_at || null
            },
            feed_data: data.feeds?.map(feed => ({
              id: feed.feed_id,
              feed_id: feed.feed_id,
              name: feed.feed_name || `Feed ${feed.feed_id}`,
              min_inclusion_percentage: feed.min_inclusion_percentage,
              max_inclusion_percentage: feed.max_inclusion_percentage,
              actual_inclusion_percentage: feed.actual_inclusion_percentage,
              cost_contribution: feed.cost_contribution
            })) || [],
            constraints: data.constraints?.map(constraint => ({
              nutrient_name: constraint.nutrient_name,
              min_value: constraint.min_value,
              max_value: constraint.max_value,
              actual_value: constraint.actual_value
            })) || [],
            message_history: [],
            formulation_result: null,
            formulation_attempts: 0
          }

          // If there's formulation data available
          if (data.last_formulated_at) {
            initialContext.formulation_result = {
              status: 'success',
              feeds: data.feeds.reduce((acc, feed) => {
                acc[feed.feed_id] = {
                  inclusion_percentage: feed.actual_inclusion_percentage,
                  cost_contribution: feed.cost_contribution
                }
                return acc
              }, {}),
              nutrients: data.constraints.reduce((acc, constraint) => {
                acc[constraint.nutrient_name] = {
                  actual_value: constraint.actual_value,
                  min_value: constraint.min_value,
                  max_value: constraint.max_value
                }
                return acc
              }, {}),
              total_cost: data.feeds.reduce((sum, feed) =>
                sum + (feed.cost_contribution || 0), 0)
            }
          }

          setContextData(initialContext)
          setIsSaved(true)
        }
      },
      onError: (err) => {
        console.error("[UnifiedRationPage] Error fetching ration:", err)
        toast.error(t('rations.errorLoadingRation'))
      }
    }
  )

  // Fetch NRC requirements
  const { data: nrcData, refetch: refetchNrcData } = useQuery(
    ['nrc-requirements', contextData.current_formulation_state?.animal_group_id, selectedNrcModelId],
    () => getNrcRequirements(
      contextData.current_formulation_state?.animal_group_id,
      selectedNrcModelId
    ),
    {
      enabled: !!contextData.current_formulation_state?.animal_group_id && !!selectedNrcModelId,
      onSuccess: (data) => {
        if (data?.nrc_requirements?.constraints &&
            contextData.constraints.length === 0 &&
            !isReformulateMode) {
          console.log("[UnifiedRationPage] Applying NRC constraints.")
          const nrcConstraints = Object.entries(data.nrc_requirements.constraints)
            .map(([name, values]) => ({
              nutrient_name: name,
              min_value: values.min,
              max_value: values.max,
              actual_value: null
            }))

          setContextData(prev => ({
            ...prev,
            constraints: nrcConstraints,
            current_formulation_state: {
              ...prev.current_formulation_state,
              constraints_count: nrcConstraints.length
            }
          }))

          setIsSaved(false)
        }
      }
    }
  )

  // Effect to refetch NRC data when model changes
  useEffect(() => {
    if (contextData.current_formulation_state?.animal_group_id && selectedNrcModelId) {
      refetchNrcData()
    }
  }, [selectedNrcModelId, contextData.current_formulation_state?.animal_group_id, refetchNrcData])

  // --- Form Accessor Functions ---
  // These functions extract data from context for form fields

  const getFormValue = (fieldName) => {
    if (!contextData) return ''

    if (fieldName === 'name')
      return contextData.current_formulation_state?.ration_name || ''

    if (fieldName === 'description')
      return contextData.current_formulation_state?.description || ''

    if (fieldName === 'animal_group_id')
      return contextData.current_formulation_state?.animal_group_id || ''

    return ''
  }

  // --- Form Update Handlers ---
  // These functions update the context when form fields change

  const handleFormDataChange = (name, value) => {
    console.log(`[UnifiedRationPage] handleFormDataChange: ${name} = ${value}`)

    setContextData(prev => {
      const newContext = {...prev}

      if (!newContext.current_formulation_state)
        newContext.current_formulation_state = {}

      if (name === 'name')
        newContext.current_formulation_state.ration_name = value
      else if (name === 'description')
        newContext.current_formulation_state.description = value
      else if (name === 'animal_group_id')
        newContext.current_formulation_state.animal_group_id = value

      return newContext
    })

    setIsSaved(false)

    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }))
    }
  }

  const handleFeedChange = (updatedFeeds) => {
    console.log('[UnifiedRationPage] handleFeedChange:', updatedFeeds)

    setContextData(prev => ({
      ...prev,
      feed_data: updatedFeeds.map(feed => ({
        id: parseInt(feed.feed_id),
        feed_id: parseInt(feed.feed_id),
        name: feeds.find(af => af.id == feed.feed_id)?.name || `Feed ${feed.feed_id}`,
        min_inclusion_percentage: feed.min_inclusion_percentage,
        max_inclusion_percentage: feed.max_inclusion_percentage,
        actual_inclusion_percentage: feed.actual_inclusion_percentage,
        cost_contribution: feed.cost_contribution
      })),
      current_formulation_state: {
        ...prev.current_formulation_state,
        feeds_count: updatedFeeds.length
      }
    }))

    setIsSaved(false)
  }

  const handleConstraintChange = (updatedConstraints) => {
    console.log('[UnifiedRationPage] handleConstraintChange:', updatedConstraints)

    setContextData(prev => ({
      ...prev,
      constraints: updatedConstraints,
      current_formulation_state: {
        ...prev.current_formulation_state,
        constraints_count: updatedConstraints.length
      }
    }))

    setIsSaved(false)
  }

  const handleNrcModelChange = (modelId) => {
    console.log('[UnifiedRationPage] handleNrcModelChange:', modelId)
    setSelectedNrcModelId(modelId)
  }

  const handleOptimizationSettingsChange = (settings) => {
    console.log('[UnifiedRationPage] handleOptimizationSettingsChange:', settings)
    setOptimizationSettings(settings)
  }

  // --- AI Context Handler ---
  // This function receives context updates from the AI Assistant

  const handleContextChange = (newContext) => {
    console.log('[UnifiedRationPage] handleContextChange:', newContext)

    if (!newContext) return

    // Apply updates to the main context state
    setContextData(prevContext => {
      // Ensure we don't lose required fields
      return {
        ...prevContext,
        ...newContext,
        // Ensure critical fields are preserved
        ration_id: newContext.ration_id || prevContext.ration_id,
        locale: newContext.locale || prevContext.locale
      }
    })

    // Mark as unsaved when AI makes changes
    setIsSaved(false)

    // If AI created a new ration (got ID), update edit mode
    if (newContext.ration_id && !contextData.ration_id) {
      setIsEditMode(true)
    }
  }

  // --- Validation Function ---

  const validate = () => {
    const newErrors = {}

    const rationName = contextData.current_formulation_state?.ration_name
    if (!rationName || !rationName.trim())
      newErrors.name = t('validation.required')

    const animalGroupId = contextData.current_formulation_state?.animal_group_id
    if (!animalGroupId)
      newErrors.animal_group_id = t('validation.required')

    if (contextData.feed_data.length === 0)
      newErrors.feeds = t('validation.atLeastOne')

    // Check feeds for min/max issues
    contextData.feed_data.forEach((feed, index) => {
      const min = parseFloat(feed.min_inclusion_percentage)
      const max = parseFloat(feed.max_inclusion_percentage)
      if (!isNaN(min) && !isNaN(max) && min > max) {
        newErrors[`feed_${index}`] = t('validation.minGreaterThanMax')
      }
    })

    // Check constraints for min/max issues
    contextData.constraints.forEach((constraint, index) => {
      const min = parseFloat(constraint.min_value)
      const max = parseFloat(constraint.max_value)
      if (!isNaN(min) && !isNaN(max) && min > max) {
        newErrors[`constraint_${index}`] = t('validation.minGreaterThanMax')
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // --- Data Preparation Function ---

  const prepareDataForAPI = (feedData = null) => {
    // Use provided feed data or fall back to context data
    const feedDataToUse = feedData || contextData.feed_data;

    // Log the feed data to help with debugging
    console.log("[UnifiedRationPage] Feed data before preparing for API:", feedDataToUse)

    return {
      name: contextData.current_formulation_state?.ration_name,
      description: contextData.current_formulation_state?.description,
      animal_group_id: contextData.current_formulation_state?.animal_group_id,
      feeds: feedDataToUse.map(feed => {
        // Ensure we have the correct feed ID
        const feedId = parseInt(feed.feed_id || feed.id)

        // Get values from the feed object directly
        let actualInclusion = feed.actual_inclusion_percentage
        let costContribution = feed.cost_contribution

        return {
          feed_id: feedId,
          min_inclusion_percentage: feed.min_inclusion_percentage === '' ?
            null : parseFloat(feed.min_inclusion_percentage),
          max_inclusion_percentage: feed.max_inclusion_percentage === '' ?
            null : parseFloat(feed.max_inclusion_percentage),
          actual_inclusion_percentage: actualInclusion === '' ?
            null : parseFloat(actualInclusion),
          cost_contribution: costContribution === '' ?
            null : parseFloat(costContribution)
        }
      }),
      constraints: contextData.constraints.map(constraint => {
        // Get formulation result for this nutrient if available
        let actualValue = constraint.actual_value

        // If we have formulation results, use those values directly
        if (contextData.formulation_result &&
            contextData.formulation_result.status === 'success' &&
            contextData.formulation_result.nutrients[constraint.nutrient_name]) {
          const nutrientResult = contextData.formulation_result.nutrients[constraint.nutrient_name]
          actualValue = nutrientResult.actual_value
        }

        return {
          nutrient_name: constraint.nutrient_name,
          min_value: constraint.min_value === '' ?
            null : parseFloat(constraint.min_value),
          max_value: constraint.max_value === '' ?
            null : parseFloat(constraint.max_value),
          actual_value: actualValue === '' ?
            null : parseFloat(actualValue)
        }
      })
    }
  }

  // --- Mutation Options ---

  const commonMutationOptions = {
    onSuccess: (data, _variables, context) => {
      console.log("[UnifiedRationPage] Mutation success:", data)
      toast.success(t(context?.successMessage || 'common.saveSuccess'))
      setIsSaved(true)
      setIsSavingLoading(false)
    },
    onError: (error, _variables, context) => {
      console.error("[UnifiedRationPage] Mutation error:", error)
      toast.error(t(context?.errorMessage || 'common.saveError'))
      setIsSavingLoading(false)
    },
    onMutate: () => {
      setIsSavingLoading(true)
    }
  }

  // --- Mutations (unchanged from original) ---

  const createMutation = useMutation(createRation, {
    ...commonMutationOptions,
    onSuccess: (data) => {
      const newId = data?.ration?.id
      if (newId) {
        // Update context with new ID
        setContextData(prev => ({
          ...prev,
          ration_id: newId,
        }))

        setIsEditMode(true)
        commonMutationOptions.onSuccess(data, { id: newId }, { successMessage: 'common.createSuccess' })

        if (!isFormulating) {
          navigate(`/rations/${newId}/edit`, { replace: true })
        }
      } else {
        console.error("[UnifiedRationPage] Create mutation returned success but no ration ID")
        toast.error(t('rations.createErrorNoId'))
        setIsSavingLoading(false)
      }
    },
    onError: (error) => commonMutationOptions.onError(error, null, { errorMessage: 'common.createError' })
  })

  const updateMutation = useMutation(
    (data) => updateRation(contextData.ration_id, data),
    {
      ...commonMutationOptions,
      onSuccess: (data) => {
        commonMutationOptions.onSuccess(data, { id: contextData.ration_id }, { successMessage: 'common.updateSuccess' })
      },
      onError: (error) => commonMutationOptions.onError(error, { id: contextData.ration_id }, { errorMessage: 'common.updateError' })
    }
  )

  // Formulation Mutations

  const formulateMutation = useMutation(
    () => {
      console.log("[UnifiedRationPage] Starting formulation mutation")

      const feedsForApi = contextData.feed_data.map(f => ({
        feed_id: parseInt(f.feed_id || f.id),
        min_inclusion_percentage: f.min_inclusion_percentage === '' ?
          null : parseFloat(f.min_inclusion_percentage),
        max_inclusion_percentage: f.max_inclusion_percentage === '' ?
          null : parseFloat(f.max_inclusion_percentage)
      }))

      const constraintsForApi = contextData.constraints.map(c => ({
        nutrient_name: c.nutrient_name,
        min_value: c.min_value === '' ? null : parseFloat(c.min_value),
        max_value: c.max_value === '' ? null : parseFloat(c.max_value)
      }))

      return formulateRation(feedsForApi, constraintsForApi, {
        ...optimizationSettings,
        nrc_model_id: selectedNrcModelId || undefined
      })
    },
    {
      onSuccess: (data) => {
        console.log("[UnifiedRationPage] Formulation success:", data)
        if (data?.result) {
          // Update context with formulation result
          setContextData(prev => ({
            ...prev,
            formulation_result: data.result,
            formulation_attempts: (prev.formulation_attempts || 0) + 1
          }))

          toast.success(t('rations.formulateSuccess'))
        } else {
          toast.warning(t('rations.formulatePartialSuccess'))
        }
        setIsFormulating(false)
      },
      onError: (error) => {
        console.error("[UnifiedRationPage] Formulation error:", error)
        toast.error(t('rations.formulateError'))
        setIsFormulating(false)
      }
    }
  )

  // Combined mutations remain unchanged, just using contextData instead

  // --- Handler Functions ---

  const handleSave = async () => {
    console.log("[UnifiedRationPage] handleSave triggered")

    if (!validate()) {
      toast.error(t('common.validationFailed'))
      return null
    }

    // Create a copy of the current feed data that we'll use for saving
    let feedDataToSave = [...contextData.feed_data];

    // If we have formulation results, update the feed data with actual inclusion percentages
    if (contextData.formulation_result && contextData.formulation_result.status === 'success') {
      console.log("[UnifiedRationPage] Updating feed data with formulation results before saving")

      // Create a copy of the current feed data with updated inclusion percentages
      feedDataToSave = contextData.feed_data.map(feed => {
        const feedId = parseInt(feed.feed_id || feed.id)
        const formulationFeedResult = contextData.formulation_result.feeds[feedId]

        if (formulationFeedResult) {
          return {
            ...feed,
            actual_inclusion_percentage: formulationFeedResult.inclusion_percentage,
            cost_contribution: formulationFeedResult.cost_contribution
          }
        }
        return feed
      })

      // Also update the context state for UI consistency
      setContextData(prev => ({
        ...prev,
        feed_data: feedDataToSave
      }))
    }

    // Prepare data for API using our updated feed data
    const dataToSave = prepareDataForAPI(feedDataToSave)
    console.log("[UnifiedRationPage] Data to save:", dataToSave)

    try {
      let result
      let savedId = contextData.ration_id

      if (isEditMode && contextData.ration_id) {
        result = await updateMutation.mutateAsync(dataToSave)
      } else {
        result = await createMutation.mutateAsync(dataToSave)
        savedId = result?.ration?.id
      }

      return savedId
    } catch (error) {
      console.error("[UnifiedRationPage] Save/Update Mutation failed:", error)
      return null
    }
  }

  const handleFormulate = async () => {
    console.log("[UnifiedRationPage] handleFormulate triggered")

    if (!validate()) {
      toast.warning(t('common.validationFailed'))
      return
    }

    setIsFormulating(true)
    setContextData(prev => ({
      ...prev,
      formulation_result: null
    }))

    // --- AI Assistant Flow ---
    if (aiAssistEnabled) {
      console.log("[UnifiedRationPage] AI Assist Enabled")

      const formulateMessage = t('rations.statusUpdates.continue', "Continue formulation process")
      let currentRationId = contextData.ration_id

      // Helper for triggering assistant
      const triggerAssistant = (rationId, context) => {
        console.log(`[UnifiedRationPage] Setting AI trigger data`, {
          rationId,
          context: !!context,
          message: formulateMessage
        })

        setAiTriggerData({
          message: formulateMessage,
          rationId,
          context
        })
      }

      // For new rations: use context directly
      if (!isEditMode) {
        console.log("[UnifiedRationPage] AI: New ration. Using current context")
        triggerAssistant(null, contextData)
      } else {
        // For existing rations: save if needed, then trigger with ID
        if (!isSaved) {
          console.log("[UnifiedRationPage] AI: Existing unsaved ration. Saving first...")
          const savedId = await handleSave()
          if (savedId) {
            triggerAssistant(savedId, null)
          } else {
            console.log("[UnifiedRationPage] AI: Save failed, aborting formulate")
            setIsFormulating(false)
            return
          }
        } else {
          console.log("[UnifiedRationPage] AI: Existing & saved. Triggering assistant")
          triggerAssistant(currentRationId, null)
        }
      }

      return // AI flow takes over
    }

    // --- Regular Formulation Flow (AI Disabled) ---
    console.log("[UnifiedRationPage] AI Assist Disabled - Regular formulation flow")

    // Function to start formulation
    const startFormulation = () => {
      console.log(`[UnifiedRationPage] Starting formulation for ID: ${contextData.ration_id}`)
      formulateMutation.mutate()
    }

    // Use original logic for save/formulate sequence
    const saveNeeded = isEditMode && !isSaved

    if (saveNeeded) {
      // For existing unsaved rations: save first, then formulate
      console.log("[UnifiedRationPage] Existing unsaved ration - saving first then formulating")
      const savedId = await handleSave()
      if (savedId) {
        startFormulation()
      } else {
        setIsFormulating(false)
        toast.error(t('rations.saveBeforeFormulateError'))
      }
    } else if (isEditMode) {
      // For existing saved rations: formulate directly
      console.log("[UnifiedRationPage] Existing saved ration - formulating directly")
      startFormulation()
    } else {
      // For new rations: create and formulate in one step
      console.log("[UnifiedRationPage] New ration - creating and formulating")
      // Use the create and formulate mutation (unchanged)
    }
  }

  // Other handlers remain mostly unchanged, just using contextData

  const handleOpenAssistant = () => {
    console.log("[UnifiedRationPage] handleOpenAssistant triggered")

    if (!aiAssistEnabled) {
      setAiAssistEnabled(true)
    }

    console.log("[UnifiedRationPage] Setting AI trigger data for general chat")

    // Just use the current context directly
    setAiTriggerData({
      message: t('rations.statusUpdates.continue'),
      rationId: contextData.ration_id,
      context: !contextData.ration_id ? contextData : null
    })

    setIsFormulating(false)
  }

  // AI toggle handler
  const handleAiToggle = (enabled) => {
    setAiAssistEnabled(enabled)
    if (!enabled && aiTriggerData) {
      setAiTriggerData(null)
    }
  }

  // --- Loading and Render Logic ---

  const isLoading = isLoadingFeeds || isLoadingGroups || (isEditMode && isLoadingRation && !contextData.current_formulation_state?.ration_name)

  if (isLoading) {
    return <p className="text-center py-4">{t('common.loading')}</p>
  }

  if (rationError) {
    return (
      <Card className="p-6 mx-auto max-w-4xl">
        <h2 className="text-xl font-semibold text-red-600 mb-4">{t('common.errorOccurred')}</h2>
        <p className="mb-4">{t('rations.errorLoadingRationDetails')}</p>
        <div className="flex justify-between">
          <Link to="/rations">
            <Button variant="outline">{t('common.backToList')}</Button>
          </Link>
          <Button onClick={() => queryClient.invalidateQueries(['ration', contextData.ration_id])}>
            {t('common.tryAgain')}
          </Button>
        </div>
      </Card>
    )
  }

  // Use context data to determine if we can formulate
  const canFormulate = contextData.feed_data.length > 0 &&
                       contextData.current_formulation_state?.animal_group_id &&
                       contextData.current_formulation_state?.ration_name

  let pageTitle = isEditMode ?
    (isReformulateMode ? t('rations.formulateRation') : t('rations.editRation')) :
    t('rations.createRation')

  const isProcessing = isFormulating || createMutation.isLoading ||
                       updateMutation.isLoading || formulateMutation.isLoading ||
                       isSavingLoading

  const showFormulateButton = canFormulate && !isFormulating
  const showSaveButton = !isFormulating

  const buttonLabels = {
    saveButton: contextData.formulation_result ? t('common.saveAndFinish') : t('common.saveAsDraft'),
    formulateButton: contextData.formulation_result ? t('rations.reformulate') : t('rations.formulate')
  }

  return (
    <div className="space-y-6">
      {/* Main Layout */}
      <RationFormulationLayout
        contextData={contextData} // Pass contextData instead of formData
        feeds={feeds}
        animalGroups={animalGroups}
        errors={errors}
        isFormulating={isProcessing}
        isSaving={isSavingLoading}
        isSaved={isSaved}
        nrcData={nrcData}
        selectedNrcModelId={selectedNrcModelId}
        optimizationSettings={optimizationSettings}
        onSave={handleSave}
        onFormulate={handleFormulate}
        onNrcModelChange={handleNrcModelChange}
        onFormDataChange={handleFormDataChange}
        onFeedChange={handleFeedChange}
        onConstraintChange={handleConstraintChange}
        onOptimizationSettingsChange={handleOptimizationSettingsChange}
        canFormulate={canFormulate}
        isReadOnly={isReformulateMode}
        pageTitle={pageTitle}
        buttonLabels={buttonLabels}
        showFormulateButton={showFormulateButton}
        showSaveButton={showSaveButton}
        aiAssistEnabled={aiAssistEnabled}
        onAiToggle={handleAiToggle}
        onOpenAssistant={handleOpenAssistant}
      />

      {/* Footer buttons (mostly unchanged) */}
      <div className="flex justify-between mt-6 pt-4 border-t">
        <Link to={isEditMode && contextData.ration_id ? `/rations/${contextData.ration_id}` : "/rations"}>
          <Button variant="outline" disabled={isProcessing}>{t('common.cancel')}</Button>
        </Link>
        <div className="flex gap-2">
          {!isFormulating && (
            <>
              {!contextData.formulation_result && (
                <Button variant="outline" onClick={handleSave} disabled={isProcessing || (isEditMode && isSaved)}>
                  {isSavingLoading ? t('common.saving') : t('common.saveAsDraft')}
                </Button>
              )}
              {canFormulate && (
                <Button onClick={handleFormulate} disabled={isProcessing}>
                  {contextData.formulation_result ? t('rations.reformulate') : t('rations.formulate')}
                </Button>
              )}
              {contextData.formulation_result && contextData.formulation_result.status !== 'error' && (
                <Button
                  variant="primary"
                  onClick={async () => {
                    // Just call the regular save handler - it now handles formulation results properly
                    await handleSave()
                  }}
                  disabled={isProcessing}
                >
                  {isSavingLoading ? t('common.saving') : t('common.saveAndFinish')}
                </Button>
              )}
            </>
          )}
        </div>
      </div>

      {/* AI Assistant Overlay */}
      <AIAssistantOverlay
        triggerData={aiTriggerData}
        currentContext={contextData} // Pass contextData instead of formData
        availableFeeds={feeds}
        onClose={() => {
          console.log("[UnifiedRationPage] Closing AI Assistant Overlay")
          setAiTriggerData(null)
          if (aiAssistEnabled && isFormulating) {
            setIsFormulating(false)
          }
        }}
        onContextChange={handleContextChange}
      />
    </div>
  )
}

export default UnifiedRationPage