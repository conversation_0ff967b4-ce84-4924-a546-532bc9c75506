import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { nord } from 'react-syntax-highlighter/dist/esm/styles/prism'

/**
 * KBArticleContent: A reusable component for rendering KB article content consistently.
 *
 * @param {Object} props
 * @param {string} props.content - The markdown content to render
 * @param {Object} props.style - Additional style to apply to the container
 * @param {string} props.className - Additional classes to apply to the container
 * @param {boolean} props.showFade - Whether to show a fade effect at the bottom (for collapsed content)
 * @param {boolean} props.isPreview - Whether this is a preview (uses smaller prose)
 */
const KBArticleContent = ({
  content,
  style = {},
  className = '',
  showFade = false,
  isPreview = false
}) => {
  if (!content) return null

  return (
    <div
      className={`prose ${isPreview ? 'prose-sm' : 'prose-blue'} text-gray-600 max-w-none relative ${className} prose-table:m-0`}
      style={style}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // Consistent table styling
          table: ({ node, ...props }) => (
            <div className="overflow-x-auto my-6 rounded-lg border border-gray-200 shadow-sm">
              <table className="min-w-full divide-y divide-gray-200 table-auto" {...props} />
            </div>
          ),
          thead: ({ node, ...props }) => (
            <thead className="bg-gray-50 sticky top-0" {...props} />
          ),
          th: ({ node, ...props }) => (
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b" {...props} />
          ),
          td: ({ node, ...props }) => (
            <td className="px-4 py-3 text-sm text-gray-700 align-top break-words" {...props} />
          ),
          tr: ({ node, ...props }) => (
            <tr className="hover:bg-gray-100 transition-colors even:bg-gray-50" {...props} />
          ),
          // Code block syntax highlighting
          code({ node, inline, className, children, ...props }) {
            const match = /language-(\w+)/.exec(className || '')
            return !inline && match ? (
              <SyntaxHighlighter
                children={String(children).replace(/\n$/, '')}
                style={nord}
                language={match[1]}
                PreTag="div"
                {...props}
              />
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            )
          }
        }}
      >
        {content}
      </ReactMarkdown>
      {showFade && (
        <div className="absolute bottom-0 left-0 w-full h-8 bg-gradient-to-t from-white/90 to-transparent pointer-events-none" />
      )}
    </div>
  )
}

export default KBArticleContent
