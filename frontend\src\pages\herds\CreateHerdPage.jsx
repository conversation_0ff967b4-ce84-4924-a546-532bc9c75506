import { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom'
import { useMutation, useQuery, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Input from '../../components/ui/Input'
import Select from '../../components/ui/Select'
import { createHerd, getHerdById, updateHerd } from '../../services/herdService'

const CreateHerdPage = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const isEditMode = !!id

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    ear_num: '',
    gender: '',
    birth_weight: '',
    breed: '',
    color: '',
    father_ear_num: '',
    mother_ear_num: '',
    entry_date: '',
    entry_type: '',
    group_name: '',
    lactation_number: '',
    month_age: '',
    grow_status: '',
    fertility_status: '',
    high: '',
    bust: '',
    measure_date: '',
    birth_date: '',
    is_on_farm: '',
    exit_date: '',
    exit_type: '',
    exit_reason: '',
    calving_date: '',
    cure_date: '',
    abortion_date: '',
    insem_date: '',
    health_status: '',
    disease_name: '',
    remark: '',
    dry_date: ''
  })

  const [errors, setErrors] = useState({})

  // Fetch herd data if in edit mode
  const { data: herd } = useQuery(
    ['herd', id],
    () => getHerdById(id),
    {
      enabled: isEditMode,
      onSuccess: (data) => {
        if (data) {
          // Format dates for input fields
          const formatDate = (dateString) => {
            if (!dateString) return ''
            try {
              const date = new Date(dateString)
              return date.toISOString().split('T')[0]
            } catch (e) {
              return ''
            }
          }

          setFormData({
            name: data.name || '',
            description: data.description || '',
            ear_num: data.ear_num || '',
            gender: data.gender || '',
            birth_weight: data.birth_weight !== null ? data.birth_weight : '',
            breed: data.breed || '',
            color: data.color || '',
            father_ear_num: data.father_ear_num || '',
            mother_ear_num: data.mother_ear_num || '',
            entry_date: formatDate(data.entry_date),
            entry_type: data.entry_type || '',
            group_name: data.group_name || '',
            lactation_number: data.lactation_number !== null ? data.lactation_number : '',
            month_age: data.month_age !== null ? data.month_age : '',
            grow_status: data.grow_status || '',
            fertility_status: data.fertility_status || '',
            high: data.high || '',
            bust: data.bust || '',
            measure_date: formatDate(data.measure_date),
            birth_date: formatDate(data.birth_date),
            is_on_farm: data.is_on_farm !== null ? data.is_on_farm : '',
            exit_date: formatDate(data.exit_date),
            exit_type: data.exit_type || '',
            exit_reason: data.exit_reason || '',
            calving_date: formatDate(data.calving_date),
            cure_date: formatDate(data.cure_date),
            abortion_date: formatDate(data.abortion_date),
            insem_date: formatDate(data.insem_date),
            health_status: data.health_status || '',
            disease_name: data.disease_name || '',
            remark: data.remark || '',
            dry_date: formatDate(data.dry_date)
          })
        }
      }
    }
  )

  const createMutation = useMutation(createHerd, {
    onSuccess: (data) => {
      queryClient.invalidateQueries('herds')
      navigate(`/herds/${data.herd.id}`)
    }
  })

  const updateMutation = useMutation((data) => updateHerd(id, data), {
    onSuccess: () => {
      queryClient.invalidateQueries(['herd', id])
      queryClient.invalidateQueries('herds')
      navigate(`/herds/${id}`)
    }
  })

  const handleChange = (e) => {
    const { name, value, type } = e.target
    let processedValue = value

    // Handle numeric inputs
    if (type === 'number') {
      processedValue = value === '' ? '' : Number(value)
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }))

    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }))
    }
  }

  const validate = () => {
    const newErrors = {}

    if (!formData.name) {
      newErrors.name = t('validation.nameRequired')
    }

    // Validate numeric fields
    if (formData.birth_weight && isNaN(Number(formData.birth_weight))) {
      newErrors.birth_weight = t('validation.mustBeNumber')
    }

    if (formData.lactation_number && isNaN(Number(formData.lactation_number))) {
      newErrors.lactation_number = t('validation.mustBeNumber')
    }

    if (formData.month_age && isNaN(Number(formData.month_age))) {
      newErrors.month_age = t('validation.mustBeNumber')
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (!validate()) {
      return
    }

    // Format data for submission
    const formattedData = { ...formData }

    // Convert empty strings to null for numeric fields
    const numericFields = ['birth_weight', 'lactation_number', 'month_age', 'is_on_farm']

    numericFields.forEach(field => {
      if (formattedData[field] === '') {
        formattedData[field] = null
      }
    })

    if (isEditMode) {
      updateMutation.mutate(formattedData)
    } else {
      createMutation.mutate(formattedData)
    }
  }

  const genderOptions = [
    { value: 'male', label: t('herds.male') },
    { value: 'female', label: t('herds.female') }
  ]

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? t('herds.editHerd') : t('herds.addNew')}
        </h1>
      </div>

      <form onSubmit={handleSubmit}>
        <Card title={t('herds.basicInformation')}>
          <div className="space-y-4">
            <Input
              id="name"
              name="name"
              label={t('herds.herdName')}
              value={formData.name}
              onChange={handleChange}
              error={errors.name}
              required
            />

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.description')}
              </label>
              <textarea
                id="description"
                name="description"
                rows="3"
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                value={formData.description}
                onChange={handleChange}
              ></textarea>
            </div>
          </div>
        </Card>

        <Card title={t('herds.animalDetails')} className="mt-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                id="ear_num"
                name="ear_num"
                label={t('herds.earNumber')}
                value={formData.ear_num}
                onChange={handleChange}
              />

              <Select
                id="gender"
                name="gender"
                label={t('herds.gender')}
                options={genderOptions}
                value={formData.gender}
                onChange={handleChange}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                id="birth_weight"
                name="birth_weight"
                label={t('herds.birthWeightKg')}
                type="number"
                step="0.1"
                value={formData.birth_weight}
                onChange={handleChange}
                error={errors.birth_weight}
              />

              <Input
                id="breed"
                name="breed"
                label={t('herds.breed')}
                value={formData.breed}
                onChange={handleChange}
              />

              <Input
                id="color"
                name="color"
                label={t('herds.color')}
                value={formData.color}
                onChange={handleChange}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                id="father_ear_num"
                name="father_ear_num"
                label={t('herds.fatherEarNum')}
                value={formData.father_ear_num}
                onChange={handleChange}
              />

              <Input
                id="mother_ear_num"
                name="mother_ear_num"
                label={t('herds.motherEarNum')}
                value={formData.mother_ear_num}
                onChange={handleChange}
              />
            </div>
          </div>
        </Card>

        <Card title={t('herds.datesStatus')} className="mt-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                id="birth_date"
                name="birth_date"
                label={t('herds.birthDate')}
                type="date"
                value={formData.birth_date}
                onChange={handleChange}
              />

              <Input
                id="entry_date"
                name="entry_date"
                label={t('herds.entryDate')}
                type="date"
                value={formData.entry_date}
                onChange={handleChange}
              />

              <Input
                id="entry_type"
                name="entry_type"
                label={t('herds.entryType')}
                value={formData.entry_type}
                onChange={handleChange}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                id="lactation_number"
                name="lactation_number"
                label={t('herds.lactationNumber')}
                type="number"
                value={formData.lactation_number}
                onChange={handleChange}
                error={errors.lactation_number}
              />

              <Input
                id="month_age"
                name="month_age"
                label={t('herds.ageMonths')}
                type="number"
                step="0.1"
                value={formData.month_age}
                onChange={handleChange}
                error={errors.month_age}
              />

              <Input
                id="group_name"
                name="group_name"
                label={t('herds.groupName')}
                value={formData.group_name}
                onChange={handleChange}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                id="grow_status"
                name="grow_status"
                label={t('herds.growthStatus')}
                value={formData.grow_status}
                onChange={handleChange}
              />

              <Input
                id="fertility_status"
                name="fertility_status"
                label={t('herds.fertilityStatus')}
                value={formData.fertility_status}
                onChange={handleChange}
              />

              <Input
                id="health_status"
                name="health_status"
                label={t('herds.healthStatus')}
                value={formData.health_status}
                onChange={handleChange}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                id="high"
                name="high"
                label={t('herds.height')}
                value={formData.high}
                onChange={handleChange}
              />

              <Input
                id="bust"
                name="bust"
                label={t('herds.bust')}
                value={formData.bust}
                onChange={handleChange}
              />
            </div>
          </div>
        </Card>

        <Card title={t('herds.additionalDates')} className="mt-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                id="measure_date"
                name="measure_date"
                label={t('herds.measureDate')}
                type="date"
                value={formData.measure_date}
                onChange={handleChange}
              />

              <Input
                id="calving_date"
                name="calving_date"
                label={t('herds.calvingDate')}
                type="date"
                value={formData.calving_date}
                onChange={handleChange}
              />

              <Input
                id="dry_date"
                name="dry_date"
                label={t('herds.dryDate')}
                type="date"
                value={formData.dry_date}
                onChange={handleChange}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                id="insem_date"
                name="insem_date"
                label={t('herds.inseminationDate')}
                type="date"
                value={formData.insem_date}
                onChange={handleChange}
              />

              <Input
                id="abortion_date"
                name="abortion_date"
                label={t('herds.abortionDate')}
                type="date"
                value={formData.abortion_date}
                onChange={handleChange}
              />

              <Input
                id="cure_date"
                name="cure_date"
                label={t('herds.cureDate')}
                type="date"
                value={formData.cure_date}
                onChange={handleChange}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                id="is_on_farm"
                name="is_on_farm"
                label={t('herds.isOnFarm')}
                type="number"
                min="0"
                max="1"
                value={formData.is_on_farm}
                onChange={handleChange}
              />

              <Input
                id="exit_date"
                name="exit_date"
                label={t('herds.exitDate')}
                type="date"
                value={formData.exit_date}
                onChange={handleChange}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                id="exit_type"
                name="exit_type"
                label={t('herds.exitType')}
                value={formData.exit_type}
                onChange={handleChange}
              />

              <Input
                id="exit_reason"
                name="exit_reason"
                label={t('herds.exitReason')}
                value={formData.exit_reason}
                onChange={handleChange}
              />
            </div>
          </div>
        </Card>

        <Card title={t('herds.healthNotes')} className="mt-6">
          <div className="space-y-4">
            <Input
              id="disease_name"
              name="disease_name"
              label={t('herds.disease')}
              value={formData.disease_name}
              onChange={handleChange}
            />

            <div>
              <label htmlFor="remark" className="block text-sm font-medium text-gray-700 mb-1">
                {t('herds.remarks')}
              </label>
              <textarea
                id="remark"
                name="remark"
                rows="3"
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                value={formData.remark}
                onChange={handleChange}
              ></textarea>
            </div>
          </div>
        </Card>

        {(createMutation.isError || updateMutation.isError) && (
          <div className="mt-6 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  {(createMutation.error || updateMutation.error)?.response?.data?.message || t('common.errorOccurred')}
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 flex justify-end space-x-3">
          <Link to="/herds">
            <Button variant="outline">{t('common.cancel')}</Button>
          </Link>
          <Button
            type="submit"
            disabled={createMutation.isLoading || updateMutation.isLoading}
          >
            {createMutation.isLoading || updateMutation.isLoading ? t('common.saving') : isEditMode ? t('herds.updateHerd') : t('herds.saveHerd')}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default CreateHerdPage