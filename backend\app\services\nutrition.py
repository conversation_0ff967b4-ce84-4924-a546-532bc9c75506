from ..models import Feed, FeedNutrient, Nutrient, Ration, RationFeed, AnimalGroup
from .nrc8_requirements import get_nrc8_requirements

def calculate_ration_nutrients(ration_id):
    """
    Calculate the nutritional content of a ration based on feed inclusion rates

    Args:
        ration_id: ID of the ration to calculate

    Returns:
        dict: Nutrient values in the ration
    """
    # Get all ration feeds with inclusion rates
    ration_feeds = RationFeed.query.filter_by(ration_id=ration_id).all()

    if not ration_feeds:
        return {}

    # Get all nutrient data for the feeds
    feed_ids = [rf.feed_id for rf in ration_feeds]
    feed_nutrients = FeedNutrient.query.filter(FeedNutrient.feed_id.in_(feed_ids)).all()

    # Create dictionary of all nutrients used
    nutrients = {}
    for fn in feed_nutrients:
        nutrient = Nutrient.query.get(fn.nutrient_id)
        if nutrient.id not in nutrients:
            nutrients[nutrient.id] = {
                'name': nutrient.name_en,
                'unit': nutrient.unit,
                'value': 0
            }

    # Calculate nutrient values based on inclusion rates
    for rf in ration_feeds:
        # Skip if feed has no inclusion rate
        if rf.actual_inclusion_percentage is None or rf.actual_inclusion_percentage == 0:
            continue

        # Get all nutrients for this feed
        for fn in feed_nutrients:
            if fn.feed_id == rf.feed_id:
                # Calculate contribution to total nutrients
                contribution = (rf.actual_inclusion_percentage / 100) * fn.value
                nutrients[fn.nutrient_id]['value'] += contribution

    # Format the results
    result = {}
    for nutrient_id, data in nutrients.items():
        result[data['name']] = {
            'value': round(data['value'], 4),
            'unit': data['unit']
        }

    return result

def nutrient_requirements(animal_group_id):
    """
    Get nutrient requirements based on animal group details using NRC 8th edition

    Args:
        animal_group_id: ID of the animal group

    Returns:
        dict: Recommended nutrient constraints
    """
    # Get animal group data
    animal_group = AnimalGroup.query.get(animal_group_id)

    if not animal_group:
        # Return default requirements if animal group not found
        default_requirements = {
            'Crude Protein': {'min': 16, 'max': 18, 'unit': '%'},
            'NDF': {'min': 30, 'max': 35, 'unit': '%'},
            'ADF': {'min': 19, 'max': 21, 'unit': '%'},
            'Fat': {'min': 3, 'max': 7, 'unit': '%'},
            'Calcium': {'min': 0.8, 'max': 1.0, 'unit': '%'},
            'Phosphorus': {'min': 0.4, 'max': 0.6, 'unit': '%'},
            'NEL': {'min': 1.65, 'max': None, 'unit': 'Mcal/kg'}
        }
        return default_requirements

    # Check if we have enough data to calculate NRC requirements
    if animal_group.weight_kg is None:
        # Return default requirements if not enough data
        default_requirements = {
            'Crude Protein': {'min': 16, 'max': 18, 'unit': '%'},
            'NDF': {'min': 30, 'max': 35, 'unit': '%'},
            'ADF': {'min': 19, 'max': 21, 'unit': '%'},
            'Fat': {'min': 3, 'max': 7, 'unit': '%'},
            'Calcium': {'min': 0.8, 'max': 1.0, 'unit': '%'},
            'Phosphorus': {'min': 0.4, 'max': 0.6, 'unit': '%'},
            'NEL': {'min': 1.65, 'max': None, 'unit': 'Mcal/kg'}
        }
        return default_requirements

    # Calculate NRC 8th edition requirements
    nrc_data = get_nrc8_requirements(animal_group_id)

    # Add some additional requirements not covered by NRC
    nrc_data['constraints']['Fat'] = {'min': 3, 'max': 7, 'unit': '%'}

    return nrc_data['constraints']