import React, { useState } from 'react'
import { useParams, useNavigate, Link } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import AnimalCard from '../../components/herds/AnimalCard'
import NrcRequirementsCard from '../../components/animalGroups/NrcRequirementsCard'
import { getAnimalGroupById, deleteAnimalGroup } from '../../services/animalGroupService'
import { getNrcRequirements } from '../../services/nrcRequirementsService'

const AnimalGroupDetailsPage = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const { data, isLoading, error } = useQuery(
    ['animal-group', id],
    () => getAnimalGroupById(id)
  )

  // Fetch NRC requirements
  const { data: nrcData, isLoading: isLoadingNrc } = useQuery(
    ['nrc-requirements', id],
    () => getNrcRequirements(id),
    {
      // Only fetch if we have animal group data with weight
      enabled: !!(data?.animalGroup?.weight_kg),
      // Don't show errors if requirements can't be calculated
      onError: () => {}
    }
  )

  const deleteMutation = useMutation(() => deleteAnimalGroup(id), {
    onSuccess: () => {
      queryClient.invalidateQueries('animal-groups')
      navigate('/animal-groups')
    }
  })

  const handleDelete = () => {
    deleteMutation.mutate()
  }

  if (isLoading) {
    return <p className="text-center py-4">{t('common.loading')}</p>
  }

  if (error) {
    return (
      <Card>
        <p className="text-red-500">{t('animalGroups.errorLoading')}: {error.message}</p>
        <div className="mt-4">
          <Link to="/animal-groups">
            <Button variant="outline">{t('animalGroups.backToGroups')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  if (!data || !data.animalGroup) {
    return (
      <Card>
        <p className="text-gray-500">{t('animalGroups.groupNotFound')}</p>
        <div className="mt-4">
          <Link to="/animal-groups">
            <Button variant="outline">{t('animalGroups.backToGroups')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  const { animalGroup, herds = [] } = data

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{animalGroup.name}</h1>
        <div className="flex space-x-2">
          <Link to={`/animal-groups/${id}/edit`}>
            <Button variant="outline">{t('common.edit')}</Button>
          </Link>
          <Button variant="danger" onClick={() => setShowDeleteConfirm(true)}>{t('common.delete')}</Button>
        </div>
      </div>

      <Card className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">{t('animalGroups.details')}</h3>
            {animalGroup.description && (
              <p className="text-gray-600 mb-4">{animalGroup.description}</p>
            )}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-xs text-gray-500">{t('animalGroups.animals')}</p>
              <p className="text-xl font-medium">{herds.length}</p>
            </div>
            {animalGroup.lactation_number !== null && (
              <div>
                <p className="text-xs text-gray-500">{t('animalGroups.avgLactation')}</p>
                <p className="text-xl font-medium">{animalGroup.lactation_number.toFixed(1)}</p>
              </div>
            )}
            {animalGroup.milk_production_kg !== null && (
              <div>
                <p className="text-xs text-gray-500">{t('animalGroups.milkProduction')}</p>
                <p className="text-xl font-medium">{animalGroup.milk_production_kg.toFixed(1)} kg</p>
              </div>
            )}
            {animalGroup.days_in_milk !== null && (
              <div>
                <p className="text-xs text-gray-500">{t('animalGroups.daysInMilk')}</p>
                <p className="text-xl font-medium">{animalGroup.days_in_milk}</p>
              </div>
            )}
            {animalGroup.weight_kg !== null && (
              <div>
                <p className="text-xs text-gray-500">{t('animalGroups.avgWeight')}</p>
                <p className="text-xl font-medium">{animalGroup.weight_kg.toFixed(1)} kg</p>
              </div>
            )}
            {animalGroup.bcs !== null && (
              <div>
                <p className="text-xs text-gray-500">{t('animalGroups.avgBCS')}</p>
                <p className="text-xl font-medium">{animalGroup.bcs.toFixed(1)}</p>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* NRC Requirements Card */}
      {animalGroup.weight_kg !== null && (
        <NrcRequirementsCard
          nrcData={nrcData?.nrc_requirements}
          className="mb-6"
        />
      )}

      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        {t('animalGroups.animalsInGroup', { count: herds.length })}
      </h2>

      {herds.length === 0 ? (
        <Card>
          <div className="text-center py-4">
            <p className="text-gray-500 mb-4">{t('animalGroups.noAnimalsInGroup')}</p>
            <Link to="/herds/new">
              <Button>{t('herds.addNew')}</Button>
            </Link>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {herds.map(herd => (
            <AnimalCard key={herd.id} animal={herd} />
          ))}
        </div>
      )}

      <div className="mt-6">
        <Link to="/animal-groups">
          <Button variant="outline">{t('animalGroups.backToGroups')}</Button>
        </Link>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">{t('animalGroups.confirmDeletion')}</h3>
            <p className="text-gray-500 mb-4">
              {t('animalGroups.deleteConfirmationMessage', { name: animalGroup.name })}
            </p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
                {t('common.cancel')}
              </Button>
              <Button variant="danger" onClick={handleDelete}>
                {t('common.delete')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AnimalGroupDetailsPage