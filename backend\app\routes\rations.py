from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from ..models import Ration, RationFeed, Constraint, Feed, AnimalGroup
from ..extensions import db
from ..services.formulation import formulate_ration
from collections import defaultdict
from ..models import Feed, Nutrient, FeedNutrient
from ..extensions import db
import logging

bp = Blueprint('rations', __name__, url_prefix='/api/rations')
logger = logging.getLogger(__name__)

@bp.route('', methods=['GET'])
@jwt_required()
def get_rations():
    current_user_id = get_jwt_identity()

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)

    # Get user's rations with pagination
    query = Ration.query.filter_by(user_id=int(current_user_id)).order_by(Ration.created_at.desc())

    # Apply pagination
    pagination = query.paginate(page=page, per_page=limit, error_out=False)
    rations = pagination.items

    return jsonify({
        'rations': [ration.to_dict() for ration in rations],
        'page': page,
        'totalPages': pagination.pages,
        'totalItems': pagination.total
    }), 200

@bp.route('', methods=['POST'])
@jwt_required()
def create_ration():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    # Validate required fields
    if not data or not data.get('name') or not data.get('animal_group_id'):
        return jsonify({'message': 'Name and animal group ID are required'}), 400

    # Verify animal group belongs to user
    group = AnimalGroup.query.filter(
        AnimalGroup.id == data['animal_group_id'],
        AnimalGroup.user_id == current_user_id
    ).first()

    if not group:
        return jsonify({'message': 'Animal group not found or you do not have permission to access it'}), 404

    # Create new ration
    ration = Ration(
        user_id=int(current_user_id),
        animal_group_id=data['animal_group_id'],
        name=data['name'],
        description=data.get('description')
    )

    db.session.add(ration)
    db.session.flush()  # Generate ID without committing

    # Add feeds if provided
    if data.get('feeds'):
        for feed_data in data['feeds']:
            # Verify feed exists and user has access to it
            feed = Feed.query.filter(
                Feed.id == feed_data['feed_id'],
                (Feed.user_id == current_user_id) | (Feed.is_public == True)
            ).first()

            if not feed:
                return jsonify({'message': f'Feed with ID {feed_data["feed_id"]} not found or you do not have permission to access it'}), 404

            # Add ration feed
            ration_feed = RationFeed(
                ration_id=ration.id,
                feed_id=feed_data['feed_id'],
                min_inclusion_percentage=feed_data.get('min_inclusion_percentage'),
                max_inclusion_percentage=feed_data.get('max_inclusion_percentage'),
                actual_inclusion_percentage=feed_data.get('actual_inclusion_percentage'),
                cost_contribution=feed_data.get('cost_contribution')
            )
            db.session.add(ration_feed)

    # Add constraints if provided
    if data.get('constraints'):
        for constraint_data in data['constraints']:
            constraint = Constraint(
                ration_id=ration.id,
                nutrient_name=constraint_data['nutrient_name'],
                min_value=constraint_data.get('min_value'),
                max_value=constraint_data.get('max_value')
            )
            db.session.add(constraint)

    db.session.commit()

    return jsonify({
        'message': 'Ration created successfully',
        'ration': ration.to_dict(include_feeds=True, include_constraints=True)
    }), 201

@bp.route('/<int:ration_id>', methods=['GET'])
@jwt_required()
def get_ration(ration_id):
    current_user_id = get_jwt_identity()

    # Get ration that belongs to user
    ration = Ration.query.filter_by(id=ration_id, user_id=int(current_user_id)).first()

    if not ration:
        return jsonify({'message': 'Ration not found'}), 404

    return jsonify({
        'ration': ration.to_dict(include_feeds=True, include_constraints=True)
    }), 200

@bp.route('/<int:ration_id>', methods=['PUT'])
@jwt_required()
def update_ration(ration_id):
    current_user_id = get_jwt_identity()

    # Get ration that belongs to user
    ration = Ration.query.filter_by(id=ration_id, user_id=int(current_user_id)).first()

    if not ration:
        return jsonify({'message': 'Ration not found or you do not have permission to update it'}), 404

    data = request.get_json()

    # Update ration fields
    if data.get('name'):
        ration.name = data['name']
    if data.get('description') is not None:
        ration.description = data['description']
    if data.get('animal_group_id'):
        # Verify animal group belongs to user
        group = AnimalGroup.query.filter(
            AnimalGroup.id == data['animal_group_id'],
            AnimalGroup.user_id == current_user_id
        ).first()

        if not group:
            return jsonify({'message': 'Animal group not found or you do not have permission to access it'}), 404

        ration.animal_group_id = data['animal_group_id']

    # Update feeds if provided
    if data.get('feeds') is not None:
        # Remove existing feeds
        RationFeed.query.filter_by(ration_id=ration.id).delete()

        # Add new feeds
        for feed_data in data['feeds']:
            # Verify feed exists and user has access to it
            feed = Feed.query.filter(
                Feed.id == feed_data['feed_id'],
                (Feed.user_id == current_user_id) | (Feed.is_public == True)
            ).first()

            if not feed:
                return jsonify({'message': f'Feed with ID {feed_data["feed_id"]} not found or you do not have permission to access it'}), 404

            # Add ration feed
            ration_feed = RationFeed(
                ration_id=ration.id,
                feed_id=feed_data['feed_id'],
                min_inclusion_percentage=feed_data.get('min_inclusion_percentage'),
                max_inclusion_percentage=feed_data.get('max_inclusion_percentage'),
                actual_inclusion_percentage=feed_data.get('actual_inclusion_percentage'),
                cost_contribution=feed_data.get('cost_contribution')
            )
            db.session.add(ration_feed)

    # Update constraints if provided
    if data.get('constraints') is not None:
        # Remove existing constraints
        Constraint.query.filter_by(ration_id=ration.id).delete()

        # Add new constraints
        for constraint_data in data['constraints']:
            constraint = Constraint(
                ration_id=ration.id,
                nutrient_name=constraint_data['nutrient_name'],
                min_value=constraint_data.get('min_value'),
                max_value=constraint_data.get('max_value')
            )
            db.session.add(constraint)

    db.session.commit()

    return jsonify({
        'message': 'Ration updated successfully',
        'ration': ration.to_dict(include_feeds=True, include_constraints=True)
    }), 200

@bp.route('/<int:ration_id>', methods=['DELETE'])
@jwt_required()
def delete_ration(ration_id):
    current_user_id = get_jwt_identity()

    # Get ration that belongs to user
    ration = Ration.query.filter_by(id=ration_id, user_id=int(current_user_id)).first()

    if not ration:
        return jsonify({'message': 'Ration not found or you do not have permission to delete it'}), 404

    db.session.delete(ration)
    db.session.commit()

    return jsonify({
        'message': 'Ration deleted successfully'
    }), 200

@bp.route('/<int:ration_id>/save-formulation', methods=['POST'])
@jwt_required()
def save_formulation_result(ration_id):
    current_user_id = get_jwt_identity()

    # Get ration that belongs to user
    ration = Ration.query.filter_by(id=ration_id, user_id=int(current_user_id)).first()

    if not ration:
        return jsonify({'message': 'Ration not found or you do not have permission to update it'}), 404

    data = request.get_json()
    formulation_result = data.get('formulation_result')

    if not formulation_result:
        return jsonify({'message': 'Formulation result is required'}), 400

    # Update ration feeds with actual inclusion percentages and cost contributions
    if formulation_result.get('feeds'):
        # First, get all existing ration feeds
        ration_feeds = RationFeed.query.filter_by(ration_id=ration.id).all()
        ration_feeds_by_feed_id = {rf.feed_id: rf for rf in ration_feeds}

        # Update each feed with the formulation result
        for feed_id_str, feed_result in formulation_result['feeds'].items():
            try:
                feed_id = int(feed_id_str)
                if feed_id in ration_feeds_by_feed_id:
                    ration_feed = ration_feeds_by_feed_id[feed_id]
                    ration_feed.actual_inclusion_percentage = feed_result.get('inclusion_percentage')
                    ration_feed.cost_contribution = feed_result.get('cost_contribution')
            except (ValueError, TypeError):
                logger.warning(f"Invalid feed ID in formulation result: {feed_id_str}")

    # Update constraints with actual values
    if formulation_result.get('nutrients'):
        # First, get all existing constraints
        constraints = Constraint.query.filter_by(ration_id=ration.id).all()
        constraints_by_name = {c.nutrient_name: c for c in constraints}

        # Update each constraint with the formulation result
        for nutrient_name, nutrient_result in formulation_result['nutrients'].items():
            if nutrient_name in constraints_by_name:
                constraint = constraints_by_name[nutrient_name]
                constraint.actual_value = nutrient_result.get('actual_value')

    # Update ration's last_formulated_at timestamp
    from datetime import datetime, timezone
    ration.last_formulated_at = datetime.now(timezone.utc)

    db.session.commit()

    return jsonify({
        'message': 'Formulation result saved successfully',
        'ration': ration.to_dict(include_feeds=True, include_constraints=True)
    }), 200

@bp.route('/formulate', methods=['POST'])
@jwt_required()
def formulate():
    """
    Formulate a ration based on provided feeds and constraints.
    Compatible with both direct calls and AI-assisted formulation.
    """
    data = request.get_json()
    if not data:
        return jsonify({"status": "error", "message": "No data provided"}), 400

    feeds_data = data.get('feeds', [])
    constraints_data = data.get('constraints', [])
    formulation_settings = data.get('formulation_settings', {})

    # Get optimization settings
    time_limit = formulation_settings.get('time_limit_seconds', 10.0)
    objective = formulation_settings.get('objective', 'minimize_cost')

    try:
        # Use the same formulate_ration function that the agent uses
        from ..services.formulation import formulate_ration
        from ..services.db_schema_service import get_nutrients_data

        # Get nutrient mappings
        all_nutrients_db = get_nutrients_data()
        nutrient_name_mappings = {
            data['name_en']: {'name_zh': data['name_zh'], 'unit': data['unit']}
            for _, data in all_nutrients_db.items()
            if data.get('name_en')
        }

        # Format feeds correctly
        formatted_feeds = []
        for feed in feeds_data:
            # Ensure feed has a valid ID
            feed_id = feed.get('feed_id')
            if not feed_id:
                continue

            # Get feed details from database for non-API formulations
            if 'dry_matter_percentage' not in feed or 'nutrients' not in feed:
                from ..models import Feed
                db_feed = Feed.query.get(feed_id)
                if not db_feed:
                    continue

                feed_details = db_feed.to_dict(include_nutrients=True)
                dry_matter = feed_details.get('dry_matter_percentage')
                cost_per_kg = feed_details.get('cost_per_kg')

                # Format nutrients as needed
                nutrients = {}
                for nutrient in feed_details.get('nutrients', []):
                    nutrient_name = nutrient.get('nutrient_name')
                    if nutrient_name:
                        nutrients[nutrient_name] = nutrient.get('value')
            else:
                # For API formulations, use provided values
                dry_matter = feed.get('dry_matter_percentage')
                cost_per_kg = feed.get('cost_per_kg')
                nutrients = feed.get('nutrients', {})

            formatted_feed = {
                'id': feed_id,
                'name': feed.get('name', f"Feed_{feed_id}"),
                'min_incl_perc': feed.get('min_inclusion_percentage', 0),
                'max_incl_perc': feed.get('max_inclusion_percentage', 100),
                'dry_matter_percentage': dry_matter,
                'cost_per_kg': cost_per_kg,
                'nutrients': nutrients
            }
            formatted_feeds.append(formatted_feed)

        # Run formulation
        result = formulate_ration(
            feeds_data=formatted_feeds,
            constraints_data=constraints_data,
            time_limit_seconds=time_limit,
            optimization_objective=objective,
            nutrient_name_mappings=nutrient_name_mappings
        )

        return jsonify({"status": "success", "result": result}), 200

    except Exception as e:
        return jsonify({"status": "error", "message": str(e), "result": {"status": "error"}}), 500