import uuid
import traceback
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from ..services.tasks import queue_batch_animal_group_processing
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime
import json
import logging


# Import DB instance and relevant Models (for code execution context)
from ..extensions import db
from ..models import Feed, Herd, Nutrient, FeedNutrient, AnimalGroup, Ration, RationFeed, User

# Import services
from ..services.excel_service import sample_excel_data
from ..services.llm_code_generator import generate_data_import_code, fix_generated_code

# Define which models are available for import
MODEL_MAP = {
    "Feed": Feed,
    "Herd": Herd,
    "Nutrient": Nutrient,
    "AnimalGroup": AnimalGroup,
    "FeedNutrient": FeedNutrient,
    "Ration" : Ration,
    "RationFeed" : RationFeed
}

logger = logging.getLogger(__name__)

bp = Blueprint('excel_import', __name__, url_prefix='/api')

def safely_execute_llm_code(code, context_vars, max_retries=1, fix_code_fn=None):
    """
    Safely execute LLM-generated code with proper transaction handling,
    error catching, and optional retry with fixed code.
    
    Args:
        code (str): The Python code to execute
        context_vars (dict): Variables to make available in the execution context
        max_retries (int): Maximum number of retry attempts if execution fails
        fix_code_fn (callable): Function to fix code if execution fails
                              Signature: fix_code_fn(code, error_details) -> fixed_code
    
    Returns:
        tuple: (success_flag, updated_context, error_details, final_code)
    """

    # Replace placeholders in the code
    current_code = code

    execution_success = False
    error_details = None
    connection = None
    
    # Initialize a clean execution context with the provided variables
    exec_vars = context_vars.copy()
    
    # Track the highest attempt number for logging
    total_attempts = max_retries + 1
    
    for attempt in range(1, total_attempts + 1):
        logger.info(f"Executing code attempt {attempt}/{total_attempts}")
        
        try:
            # Get a fresh connection
            connection = db.engine.connect()
            
            # Start a transaction
            with connection.begin():
                # Get a cursor for the code to use
                cursor = connection.connection.cursor()
                
                # Add the cursor to the execution variables
                exec_vars['cursor'] = cursor
                
                logger.debug(f"start to execute llm generated code:{current_code}")
                # Execute the code in the context
                exec(current_code, exec_vars)
                
                # If we get here, the execution was successful
                execution_success = True
                
                # Transaction commits automatically on exit without exception
                logger.info("Code executed successfully")
                
                # Break the retry loop on success
                break
                
        except Exception as e:
            # Capture error details
            error_details = f"Error during code execution (attempt {attempt}):\n{str(e)}\n{traceback.format_exc()}"
            logger.error(error_details)
            
            # The transaction will be rolled back automatically on exception
            
            # If this is our last attempt, don't try to fix the code
            if attempt >= total_attempts or not fix_code_fn:
                logger.warning(f"Last attempt ({attempt}/{total_attempts}) failed, no more retries")
                break
                
            # Try to fix the code for the next attempt
            logger.info(f"Attempting to fix code after error in attempt {attempt}")
            fixed_code = fix_code_fn(current_code, error_details)
            
            # Validate the fixed code
            if not fixed_code or len(fixed_code) < 50:
                logger.error("Code fixing failed to produce valid code")
                break
                
            # Use the fixed code for the next attempt
            current_code = fixed_code
            
        finally:
            # Always ensure the connection is closed
            if connection:
                connection.close()
                connection = None
    
    # Return a tuple with execution results
    return (execution_success, exec_vars, error_details, current_code)


@bp.route('/analyze_and_stage', methods=['POST'])
@jwt_required()
def analyze_and_stage():
    """
    Improved implementation for analyze_and_stage endpoint.
    
    Receives Excel data, uses LLM to generate code for importing the data,
    executes the code with proper error handling and feedback loop, and
    provides a clean response for the client.
    """
    current_user_id = get_jwt_identity()
    excel_payload = request.get_json()
    import_session_id = str(uuid.uuid4())

    # --- Input Validation ---
    if not excel_payload or 'clusters' not in excel_payload:
        return jsonify({
            "success": False, 
            "message": "Invalid payload structure", 
            "sessionId": import_session_id
        }), 400

    sheet_name = excel_payload.get('sourceSheetName', 'Unknown')
    range_address = excel_payload.get('usedRangeAddress', 'Unknown')
    
    logger.info(f"Analyze & Stage request for sheet '{sheet_name}' from user {current_user_id}. Session: {import_session_id}")

    try:
        # --- Step 1: Sample Excel Data ---
        sampled_data = sample_excel_data(excel_payload)

        # --- Step 2: Generate Initial Import Code ---
        logger.info(f"Generating import code for session {import_session_id}")
        
        original_code = generate_data_import_code(
            sheet_name,
            range_address,
            sampled_data,
            import_session_id
        )

            
        # --- Step 3: Execute Code with Clean Retry Logic ---
        # Define a function to fix code when execution fails
        def fix_failed_code(code, error_details):
            return fix_generated_code(code, error_details, import_session_id)
        
        # Setup execution environment
        exec_vars = {
            'excel_data': excel_payload,
            'session_id': import_session_id,
            'user_id': int(current_user_id),
            'record_type': None,  # Will be set by the executed code
            'import_status': 'pending_review',
            'logger': logger
        }
        
        # Execute the code with our utility function
        execution_success, updated_vars, error_details, final_code = safely_execute_llm_code(
            code=original_code,
            context_vars=exec_vars,
            max_retries=3,  # Try original + 1 retry
            fix_code_fn=fix_failed_code
        )
        
        # Extract the record type if execution was successful
        record_type = updated_vars.get('record_type') if execution_success else None
        
        # --- Step 4: Handle Success or Failure ---
        if not execution_success:
            raise ValueError("Import failed after all attempts")
            
        # If record_type wasn't explicitly set, try to infer it
        if not record_type:
            logger.warning(f"Record type not explicitly set by code for session {import_session_id}")
            
            # Simple inference based on table names in code
            if "feeds" in final_code.lower():
                record_type = "Feed"
            elif "herds" in final_code.lower():
                record_type = "Herd"
            elif "rations" in final_code.lower():
                record_type = "Ration"
            else:
                record_type = "Unknown"
                
            logger.info(f"Inferred record type: {record_type}")
            
        # --- Step 5: Return Success Response ---
        logger.info(f"Import successful for session {import_session_id}, type: {record_type}")
        
        # Construct review URL
        review_path = f"/excel-import-review/{import_session_id}"
        if record_type and record_type != "Unknown":
            review_path += f"?recordType={record_type}"
        
        return jsonify({
            "success": True,
            "sessionId": import_session_id,
            "recordType": record_type,
            "message": "Data staged successfully for review.",
            "reviewUrl": review_path
        }), 200
        
    except ValueError as e:
        # Handle specific validation/logic errors
        logger.error(f"Value error in analyze_and_stage: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Import failed: {str(e)}",
            "sessionId": import_session_id
        }), 400
        
    except SQLAlchemyError as e:
        # Handle database-specific errors
        logger.error(f"Database error in analyze_and_stage: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": "Database error during import process.",
            "sessionId": import_session_id
        }), 500
        
    except Exception as e:
        # Handle any other unexpected errors
        logger.error(f"Unexpected error in analyze_and_stage: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": "An unexpected error occurred during the import process.",
            "sessionId": import_session_id
        }), 500


@bp.route('/pending_import_data/<string:session_id>', methods=['GET'])
@jwt_required()
def get_pending_import_data(session_id):
    """Provides paginated data directly from the target table with 'pending_review' status."""
    current_user_id = get_jwt_identity()
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 100, type=int)
    record_type = request.args.get('recordType')  # Get recordType from query param

    if not record_type:
        return jsonify({"success": False, "message": "Missing 'recordType' parameter."}), 400

    ModelClass = MODEL_MAP.get(record_type)
    if not ModelClass:
        return jsonify({"success": False, "message": f"Invalid 'recordType': {record_type}"}), 400

    current_app.logger.debug(f"Fetching pending data for session {session_id}, type {record_type}, page {page}")

    try:
        query = ModelClass.query.filter_by(
            user_id=int(current_user_id),
            import_session_id=session_id,
            import_status='pending_review'
        ).order_by(ModelClass.id)  # Add ordering for consistent pagination

        pagination_obj = query.paginate(page=page, per_page=limit, error_out=False)
        pending_data = pagination_obj.items
        total_records = pagination_obj.total

        data_dicts = []
        related_data_map = {}  # Store related data if needed: {main_id: [related_dicts]}

        # Fetch related data if applicable (e.g., FeedNutrient for Feed)
        if record_type == "Feed" and pending_data:
            main_ids = [item.id for item in pending_data]
            related_items = FeedNutrient.query.filter(FeedNutrient.feed_id.in_(main_ids)).all()
            nutrients_info = {n.id: n.name_en for n in Nutrient.query.all()}  # Cache nutrient names

            for related in related_items:
                if related.feed_id not in related_data_map:
                    related_data_map[related.feed_id] = []
                related_data_map[related.feed_id].append({
                    'nutrient_id': related.nutrient_id,
                    'nutrient_name_en': nutrients_info.get(related.nutrient_id, 'Unknown'),
                    'value': related.value
                })

        # Convert main data to dicts and add related data
        for item in pending_data:
            item_dict = item.to_dict() if hasattr(item, 'to_dict') else {c.name: getattr(item, c.name) for c in item.__table__.columns}
            if record_type == "Feed" and item.id in related_data_map:
                item_dict['_related_nutrients'] = related_data_map[item.id]
            data_dicts.append(item_dict)

        return jsonify({
            "success": True,
            "sessionId": session_id,
            "recordType": record_type,
            "data": data_dicts,
            "page": page,
            "limit": limit,
            "totalRecords": total_records,
            "totalPages": pagination_obj.pages
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching pending data for session {session_id}, type {record_type}: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"Error fetching pending data: {e}"}), 500

@bp.route('/import_session_types/<string:session_id>', methods=['GET'])
@jwt_required()
def get_import_session_types(session_id):
    """Get all record types that have data for a given import session."""
    current_user_id = get_jwt_identity()

    if not session_id:
        return jsonify({"success": False, "message": "Missing session ID"}), 400

    try:
        # Check each main table that supports imports
        record_types_with_data = []

        for model_name, ModelClass in MODEL_MAP.items():
            # Skip models that don't have import_session_id
            if not hasattr(ModelClass, 'import_session_id'):
                continue

            # Count records for this type and session
            try:
                count = ModelClass.query.filter_by(
                    user_id=int(current_user_id),
                    import_session_id=session_id,
                    import_status='pending_review'
                ).count()

                if count > 0:
                    record_types_with_data.append({
                        "type": model_name,
                        "count": count
                    })
            except Exception as table_error:
                current_app.logger.warning(f"Error checking {model_name} table: {str(table_error)}")
                continue

        return jsonify({
            "success": True,
            "sessionId": session_id,
            "recordTypes": record_types_with_data
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting record types for session {session_id}: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"Error retrieving session data: {str(e)}"}), 500


@bp.route('/confirm_import/<string:session_id>', methods=['POST'])
@jwt_required()
def confirm_import(session_id):
    """Confirms the pending import data by changing its status."""
    current_user_id = get_jwt_identity()
    mode = request.json.get('mode', 'replace')
    record_type = request.json.get('recordType')  # Get recordType from body/param

    if not record_type:
        return jsonify({"success": False, "message": "Missing 'recordType' in request."}), 400

    ModelClass = MODEL_MAP.get(record_type)
    if not ModelClass:
        return jsonify({"success": False, "message": f"Invalid 'recordType': {record_type}"}), 400

    # Determine related model if applicable (only Feed->FeedNutrient for now)
    RelatedModelClass = None
    link_col_parent = None

    if record_type == "Feed":
        RelatedModelClass = FeedNutrient
        link_col_parent = 'feed_id'
    elif record_type == "Ration":
        RelatedModelClass = RationFeed
        link_col_parent = 'ration_id'

    current_app.logger.info(f"Confirming import {session_id} mode '{mode}' user {current_user_id} for type {record_type}")

    try:
        # Make sure we have a clean session before starting our database operations
        db.session.remove()

        # Find the records staged for this session
        pending_records_query = ModelClass.query.filter_by(
            user_id=int(current_user_id),
            import_session_id=session_id,
            import_status='pending_review'
        )

        # Fetch IDs needed for related data handling before update/delete
        pending_record_ids = [r.id for r in pending_records_query.with_entities(ModelClass.id).all()]

        if not pending_record_ids:
            return jsonify({"success": True, "message": "No pending records found for this session to confirm."}), 200

        if mode == 'replace':
            # Delete existing 'confirmed' records for this user/type
            delete_confirmed_query = ModelClass.query.filter_by(
                user_id=int(current_user_id),
                import_status='confirmed'
            )

            existing_confirmed_records_for_delete = delete_confirmed_query.all()  # Fetch before delete
            deleted_count = len(existing_confirmed_records_for_delete)

            if deleted_count > 0:
                current_app.logger.warning(f"Confirm/Replace: Deleting {deleted_count} existing confirmed '{record_type}' records.")

                # Delete related data for confirmed records being deleted
                if RelatedModelClass and link_col_parent:
                    parent_ids_to_delete = [r.id for r in existing_confirmed_records_for_delete if hasattr(r, 'id')]

                    if parent_ids_to_delete:
                        related_delete_count = db.session.query(RelatedModelClass).filter(
                            getattr(RelatedModelClass, link_col_parent).in_(parent_ids_to_delete)
                        ).delete(synchronize_session=False)

                        current_app.logger.info(f"Confirm/Replace: Deleted {related_delete_count} related {RelatedModelClass.__name__} records.")

                # Delete main confirmed records
                delete_confirmed_query.delete(synchronize_session=False)

            # Update pending records to confirmed status
            update_count = db.session.query(ModelClass).filter(
                ModelClass.id.in_(pending_record_ids)  # Update by ID
            ).update({"import_status": "confirmed"}, synchronize_session=False)

            current_app.logger.info(f"Confirm/Replace: Updated {update_count} pending records to confirmed.")

        elif mode == 'upsert':
            # Simplified: Just confirm pending records. Real upsert is complex.
            update_count = db.session.query(ModelClass).filter(
                ModelClass.id.in_(pending_record_ids)
            ).update({"import_status": "confirmed"}, synchronize_session=False)

            current_app.logger.info(f"Confirm/Upsert (Simplified): Updated {update_count} pending records to confirmed. NOTE: Duplicates may occur if keys conflict.")

        else:
            raise ValueError(f"Unsupported confirmation mode: {mode}")

        # Explicitly commit the transaction
        db.session.commit()

        if record_type == "Herd" and mode == 'replace':
            success = queue_batch_animal_group_processing(session_id,mode)
            if success:
                logger.info(f"Queued batch animal group processing for import session {session_id}")
            else:
                logger.warning(f"Failed to queue batch animal group processing for session {session_id}")

        return jsonify({
            "success": True,
            "message": f"Import session {session_id} confirmed successfully in '{mode}' mode."
        }), 200

    except (SQLAlchemyError, ValueError) as e:
        db.session.rollback()  # Explicitly rollback on errors
        current_app.logger.error(f"Error confirming import {session_id}: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"Error confirming import: {str(e)}"}), 500

    except Exception as e:
        db.session.rollback()  # Explicitly rollback on unexpected errors
        current_app.logger.error(f"Unexpected error confirming import {session_id}: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"An unexpected error occurred: {str(e)}"}), 500


@bp.route('/discard_import/<string:session_id>', methods=['DELETE'])
@jwt_required()
def discard_import(session_id):
    """Discards the pending import data by deleting it from target tables."""
    current_user_id = get_jwt_identity()
    record_type = request.args.get('recordType')  # Get recordType from query param

    if not record_type:
        return jsonify({"success": False, "message": "Missing 'recordType' parameter."}), 400

    ModelClass = MODEL_MAP.get(record_type)
    if not ModelClass:
        return jsonify({"success": False, "message": f"Invalid 'recordType': {record_type}"}), 400

    RelatedModelClass = FeedNutrient if record_type == "Feed" else None
    link_col_parent = 'feed_id' if record_type == "Feed" else None

    current_app.logger.info(f"Discarding import session {session_id} by user {current_user_id} for type {record_type}")

    try:
        # Make sure we have a clean session before starting our database operations
        db.session.remove()

        # Find IDs of main pending records
        pending_record_ids_query = ModelClass.query.filter_by(
            user_id=int(current_user_id),
            import_session_id=session_id,
            import_status='pending_review'
        ).with_entities(ModelClass.id)

        pending_record_ids = [r[0] for r in pending_record_ids_query.all()]

        if not pending_record_ids:
            return jsonify({"success": True, "message": "No pending records found for this session to discard."}), 200

        # Delete related records first using the fetched IDs
        if RelatedModelClass and link_col_parent:
            deleted_related_count = db.session.query(RelatedModelClass).filter(
                getattr(RelatedModelClass, link_col_parent).in_(pending_record_ids)
            ).delete(synchronize_session=False)

            current_app.logger.info(f"Discard: Deleted {deleted_related_count} related {RelatedModelClass.__name__} records.")

        # Delete main pending records using the fetched IDs
        deleted_main_count = db.session.query(ModelClass).filter(
            ModelClass.id.in_(pending_record_ids)
        ).delete(synchronize_session=False)

        current_app.logger.info(f"Discard: Deleted {deleted_main_count} main pending {ModelClass.__name__} records.")

        # Explicitly commit the transaction
        db.session.commit()

        return jsonify({"success": True, "message": "Pending import data discarded successfully."}), 200

    except SQLAlchemyError as e:
        db.session.rollback()  # Explicitly rollback on errors
        current_app.logger.error(f"Error discarding import {session_id}: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"Error discarding import: {str(e)}"}), 500

    except Exception as e:
        db.session.rollback()  # Explicitly rollback on unexpected errors
        current_app.logger.error(f"Unexpected error discarding import {session_id}: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"An unexpected error occurred: {str(e)}"}), 500