import api from './api'

/**
 * Get NRC 8th edition requirements for an animal group
 * @param {number} groupId - The ID of the animal group
 * @param {number|string} [modelVersionId=null] - Optional model version ID to use
 * @returns {Promise<Object>} - The NRC requirements data
 */
export const getNrcRequirements = async (groupId, modelVersionId = null) => {
  let url = `/herds/groups/${groupId}/nrc-requirements`

  // Add model version ID as a query parameter if provided
  if (modelVersionId) {
    url += `?model_version_id=${modelVersionId}`
  }

  const response = await api.get(url)
  return response.data
}

/**
 * Format NRC requirements for display
 * @param {Object} nrcData - The NRC requirements data
 * @returns {Object} - Formatted requirements for display
 */
export const formatNrcRequirements = (nrcData) => {
  if (!nrcData || !nrcData.detailed_requirements) {
    return {}
  }

  const { detailed_requirements } = nrcData

  // Format DMI
  const dmi = {
    value: detailed_requirements.dmi_kg,
    unit: 'kg/day'
  }

  // Format energy requirements
  const energy = {
    maintenance: {
      value: detailed_requirements.energy.maintenance_nel,
      unit: 'Mcal/day'
    },
    lactation: {
      value: detailed_requirements.energy.lactation_nel,
      unit: 'Mcal/day'
    },
    pregnancy: {
      value: detailed_requirements.energy.pregnancy_nel,
      unit: 'Mcal/day'
    },
    growth: {
      value: detailed_requirements.energy.growth_nel,
      unit: 'Mcal/day'
    },
    total: {
      value: detailed_requirements.energy.total_nel,
      unit: 'Mcal/day'
    },
    concentration: {
      value: detailed_requirements.energy.nel_concentration,
      unit: 'Mcal/kg DM'
    }
  }

  // Format protein requirements
  const protein = {
    maintenance: {
      value: detailed_requirements.protein.maintenance_mp,
      unit: 'g/day'
    },
    lactation: {
      value: detailed_requirements.protein.lactation_mp,
      unit: 'g/day'
    },
    pregnancy: {
      value: detailed_requirements.protein.pregnancy_mp,
      unit: 'g/day'
    },
    growth: {
      value: detailed_requirements.protein.growth_mp,
      unit: 'g/day'
    },
    total: {
      value: detailed_requirements.protein.total_mp,
      unit: 'g/day'
    },
    concentration: {
      value: detailed_requirements.protein.crude_protein_percent,
      unit: '% of DM'
    }
  }

  // Format mineral requirements
  const minerals = {
    calcium: {
      value: detailed_requirements.minerals.calcium_percent,
      unit: '% of DM'
    },
    phosphorus: {
      value: detailed_requirements.minerals.phosphorus_percent,
      unit: '% of DM'
    },
    magnesium: {
      value: detailed_requirements.minerals.magnesium_percent,
      unit: '% of DM'
    },
    potassium: {
      value: detailed_requirements.minerals.potassium_percent,
      unit: '% of DM'
    },
    sodium: {
      value: detailed_requirements.minerals.sodium_percent,
      unit: '% of DM'
    },
    chlorine: {
      value: detailed_requirements.minerals.chlorine_percent,
      unit: '% of DM'
    },
    sulfur: {
      value: detailed_requirements.minerals.sulfur_percent,
      unit: '% of DM'
    }
  }

  // Format fiber requirements
  const fiber = {
    ndf_min: {
      value: detailed_requirements.fiber.ndf_min_percent,
      unit: '% of DM'
    },
    ndf_max: {
      value: detailed_requirements.fiber.ndf_max_percent,
      unit: '% of DM'
    },
    forage_ndf_min: {
      value: detailed_requirements.fiber.forage_ndf_min_percent,
      unit: '% of DM'
    },
    adf_min: {
      value: detailed_requirements.fiber.adf_min_percent,
      unit: '% of DM'
    }
  }

  // Include model version information if available
  const modelVersion = nrcData.model_version || null

  return {
    dmi,
    energy,
    protein,
    minerals,
    fiber,
    modelVersion
  }
}

/**
 * Compare NRC requirements between different models
 * @param {number} groupId - The ID of the animal group
 * @param {Array<number>} modelIds - Array of model version IDs to compare
 * @returns {Promise<Object>} - Comparison data
 */
export const compareNrcRequirements = async (groupId, modelIds) => {
  const response = await api.post(`/herds/groups/${groupId}/compare-requirements`, {
    model_version_ids: modelIds
  })
  return response.data
}