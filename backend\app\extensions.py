from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON>TManager
from flask_cors import CORS

# Initialize extensions
db = SQLAlchemy()
jwt = JWTManager()
cors = CORS()

@jwt.invalid_token_loader
def invalid_token_callback(error_string):
    print(f"JWT ERROR - Invalid token: {error_string}")
    return {'message': f'Invalid token: {error_string}'}, 401

@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    print(f"JWT ERROR - Token expired: {jwt_payload}")
    return {'message': 'Token has expired'}, 401

@jwt.unauthorized_loader
def unauthorized_callback(error_string):
    print(f"JWT ERROR - Unauthorized: {error_string}")
    return {'message': f'Missing authorization: {error_string}'}, 401