from dotenv import load_dotenv
import os
from flask_migrate import Migrate
from app.extensions import db

# Load environment variables from .env file
load_dotenv()

from app import create_app

app = create_app(os.getenv('FLASK_CONFIG', 'default'))
# Don't override the DATABASE_URL from .env
# app.config['SQLALCHEMY_DATABASE_URI'] = 'your_database_uri'
migrate = Migrate(app, db)

if __name__ == '__main__':
    # When running directly with python run.py
    try:
        print("Starting Flask app on 0.0.0.0:5000 for remote access")
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"Failed to start Flask app: {e}")
        print("Socket permission error solutions:")
        print("1. Run as Administrator")
        print("2. Check Windows Firewall settings")
        print("3. Temporarily disable antivirus")
        print("4. Use 'flask run' command instead")