"""pgvector with procrastinate

Revision ID: cbdcd7e0d7f8
Revises: e3d3f148aacf
Create Date: 2025-04-15 13:21:44.119874

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql.sqltypes import Float
import logging

# Configure logging
logger = logging.getLogger('alembic.migration')

# revision identifiers, used by Alembic.
revision = 'cbdcd7e0d7f8'
down_revision = 'e3d3f148aacf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # First, drop dependent functions and types with CASCADE
    logger.info("Dropping procrastinate functions and types with CASCADE")
    try:
        # Drop the function that depends on procrastinate_jobs
        op.execute('DROP FUNCTION IF EXISTS procrastinate_fetch_job_v2(character varying[], bigint) CASCADE')
        # Drop any other functions that might depend on the tables
        op.execute('DROP FUNCTION IF EXISTS procrastinate_finish_job(bigint, procrastinate_job_status) CASCADE')
        op.execute('DROP FUNCTION IF EXISTS procrastinate_retry_job(bigint, timestamp with time zone) CASCADE')
        op.execute('DROP FUNCTION IF EXISTS procrastinate_defer_job(character varying, character varying, jsonb, integer, text, text, timestamp with time zone) CASCADE')
        op.execute('DROP FUNCTION IF EXISTS procrastinate_trigger_defer_job() CASCADE')
        op.execute('DROP FUNCTION IF EXISTS procrastinate_unlink_periodic_defers() CASCADE')

        # Drop the enum types with CASCADE
        op.execute('DROP TYPE IF EXISTS procrastinate_job_status CASCADE')
        op.execute('DROP TYPE IF EXISTS procrastinate_job_event_type CASCADE')
    except Exception as e:
        logger.warning(f"Error dropping procrastinate functions: {e}")

    # Now drop the tables
    try:
        with op.batch_alter_table('procrastinate_events', schema=None) as batch_op:
            batch_op.drop_index('procrastinate_events_job_id_fkey_v1')
        op.execute('DROP TABLE IF EXISTS procrastinate_events CASCADE')
    except Exception as e:
        logger.warning(f"Error dropping procrastinate_events: {e}")

    try:
        with op.batch_alter_table('procrastinate_periodic_defers', schema=None) as batch_op:
            batch_op.drop_index('procrastinate_periodic_defers_job_id_fkey_v1')
        op.execute('DROP TABLE IF EXISTS procrastinate_periodic_defers CASCADE')
    except Exception as e:
        logger.warning(f"Error dropping procrastinate_periodic_defers: {e}")

    try:
        op.execute('DROP TABLE IF EXISTS procrastinate_jobs CASCADE')
    except Exception as e:
        logger.warning(f"Error dropping procrastinate_jobs: {e}")

    try:
        with op.batch_alter_table('procrastinate_workers', schema=None) as batch_op:
            batch_op.drop_index('idx_procrastinate_workers_last_heartbeat')
        op.execute('DROP TABLE IF EXISTS procrastinate_workers CASCADE')
    except Exception as e:
        logger.warning(f"Error dropping procrastinate_workers: {e}")

    # Add pgvector extension
    logger.info("Adding pgvector extension")
    op.execute('CREATE EXTENSION IF NOT EXISTS vector')

    # Create jsonb_to_vector function
    logger.info("Creating jsonb_to_vector function")
    op.execute('''
    CREATE OR REPLACE FUNCTION jsonb_to_vector(data jsonb)
    RETURNS float8[] AS $$
    BEGIN
        RETURN array_agg(x::float8)::float8[] FROM jsonb_array_elements_text(data) AS x;
    EXCEPTION WHEN OTHERS THEN
        -- Handle any errors by returning NULL
        RETURN NULL;
    END;
    $$ LANGUAGE plpgsql IMMUTABLE;
    ''')
    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint('animal_groups_herd_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'herds', ['herd_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint('constraints_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint('feed_nutrients_nutrient_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('feed_nutrients_feed_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'nutrients', ['nutrient_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint('feeds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint('herds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_article_tags', schema=None) as batch_op:
        batch_op.drop_constraint('kb_article_tags_tag_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('kb_article_tags_article_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'kb_tags', ['tag_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'kb_articles', ['article_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_articles', schema=None) as batch_op:
        batch_op.add_column(sa.Column('embedding_status', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('embedding_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
        batch_op.add_column(sa.Column('embedding_vector', postgresql.ARRAY(Float()), nullable=True))
        batch_op.drop_constraint('kb_articles_user_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('kb_articles_category_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'kb_categories', ['category_id'], ['id'], referent_schema='ration_app')
        batch_op.drop_column('embedding')

    with op.batch_alter_table('kb_categories', schema=None) as batch_op:
        batch_op.drop_constraint('kb_categories_parent_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'kb_categories', ['parent_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_feedback', schema=None) as batch_op:
        batch_op.drop_constraint('kb_feedback_user_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('kb_feedback_article_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'kb_articles', ['article_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint('ration_feeds_feed_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('ration_feeds_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint('rations_animal_group_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('rations_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'animal_groups', ['animal_group_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint('staged_import_sessions_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('staged_import_sessions_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('rations_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('rations_animal_group_id_fkey', 'animal_groups', ['animal_group_id'], ['id'])

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('ration_feeds_ration_id_fkey', 'rations', ['ration_id'], ['id'])
        batch_op.create_foreign_key('ration_feeds_feed_id_fkey', 'feeds', ['feed_id'], ['id'])

    with op.batch_alter_table('kb_feedback', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_feedback_article_id_fkey', 'kb_articles', ['article_id'], ['id'])
        batch_op.create_foreign_key('kb_feedback_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('kb_categories', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_categories_parent_id_fkey', 'kb_categories', ['parent_id'], ['id'])

    with op.batch_alter_table('kb_articles', schema=None) as batch_op:
        batch_op.add_column(sa.Column('embedding', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_articles_category_id_fkey', 'kb_categories', ['category_id'], ['id'])
        batch_op.create_foreign_key('kb_articles_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.drop_column('embedding_vector')
        batch_op.drop_column('embedding_json')
        batch_op.drop_column('embedding_status')

    with op.batch_alter_table('kb_article_tags', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_article_tags_article_id_fkey', 'kb_articles', ['article_id'], ['id'])
        batch_op.create_foreign_key('kb_article_tags_tag_id_fkey', 'kb_tags', ['tag_id'], ['id'])

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('herds_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feeds_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feed_nutrients_feed_id_fkey', 'feeds', ['feed_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('feed_nutrients_nutrient_id_fkey', 'nutrients', ['nutrient_id'], ['id'])

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('constraints_ration_id_fkey', 'rations', ['ration_id'], ['id'])

    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('animal_groups_herd_id_fkey', 'herds', ['herd_id'], ['id'])

    op.create_table('procrastinate_workers',
    sa.Column('id', sa.BIGINT(), sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=9223372036854775807, cycle=False, cache=1), autoincrement=True, nullable=False),
    sa.Column('last_heartbeat', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='procrastinate_workers_pkey'),
    postgresql_ignore_search_path=False
    )
    with op.batch_alter_table('procrastinate_workers', schema=None) as batch_op:
        batch_op.create_index('idx_procrastinate_workers_last_heartbeat', ['last_heartbeat'], unique=False)

    op.create_table('procrastinate_jobs',
    sa.Column('id', sa.BIGINT(), server_default=sa.text("nextval('procrastinate_jobs_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('queue_name', sa.VARCHAR(length=128), autoincrement=False, nullable=False),
    sa.Column('task_name', sa.VARCHAR(length=128), autoincrement=False, nullable=False),
    sa.Column('priority', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('lock', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('queueing_lock', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('args', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb"), autoincrement=False, nullable=False),
    sa.Column('status', postgresql.ENUM('todo', 'doing', 'succeeded', 'failed', 'cancelled', 'aborting', 'aborted', name='procrastinate_job_status'), server_default=sa.text("'todo'::procrastinate_job_status"), autoincrement=False, nullable=False),
    sa.Column('scheduled_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('attempts', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('abort_requested', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False),
    sa.Column('worker_id', sa.BIGINT(), autoincrement=False, nullable=True),
    sa.CheckConstraint("NOT (status = 'todo'::procrastinate_job_status AND abort_requested = true)", name='check_not_todo_abort_requested'),
    sa.ForeignKeyConstraint(['worker_id'], ['procrastinate_workers.id'], name='procrastinate_jobs_worker_id_fkey', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name='procrastinate_jobs_pkey'),
    postgresql_ignore_search_path=False
    )
    with op.batch_alter_table('procrastinate_jobs', schema=None) as batch_op:
        batch_op.create_index('procrastinate_jobs_queueing_lock_idx_v1', ['queueing_lock'], unique=True, postgresql_where="(status = 'todo'::procrastinate_job_status)")
        batch_op.create_index('procrastinate_jobs_queue_name_idx_v1', ['queue_name'], unique=False)
        batch_op.create_index('procrastinate_jobs_priority_idx_v1', [sa.literal_column('priority DESC'), 'id'], unique=False, postgresql_where="(status = 'todo'::procrastinate_job_status)")
        batch_op.create_index('procrastinate_jobs_lock_idx_v1', ['lock'], unique=True, postgresql_where="(status = 'doing'::procrastinate_job_status)")
        batch_op.create_index('procrastinate_jobs_id_lock_idx_v1', ['id', 'lock'], unique=False, postgresql_where="(status = ANY (ARRAY['todo'::procrastinate_job_status, 'doing'::procrastinate_job_status]))")
        batch_op.create_index('idx_procrastinate_jobs_worker_not_null', ['worker_id'], unique=False, postgresql_where="((worker_id IS NOT NULL) AND (status = 'doing'::procrastinate_job_status))")

    op.create_table('procrastinate_periodic_defers',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('task_name', sa.VARCHAR(length=128), autoincrement=False, nullable=False),
    sa.Column('defer_timestamp', sa.BIGINT(), autoincrement=False, nullable=True),
    sa.Column('job_id', sa.BIGINT(), autoincrement=False, nullable=True),
    sa.Column('periodic_id', sa.VARCHAR(length=128), server_default=sa.text("''::character varying"), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['job_id'], ['procrastinate_jobs.id'], name='procrastinate_periodic_defers_job_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='procrastinate_periodic_defers_pkey'),
    sa.UniqueConstraint('task_name', 'periodic_id', 'defer_timestamp', name='procrastinate_periodic_defers_unique')
    )
    with op.batch_alter_table('procrastinate_periodic_defers', schema=None) as batch_op:
        batch_op.create_index('procrastinate_periodic_defers_job_id_fkey_v1', ['job_id'], unique=False)

    op.create_table('procrastinate_events',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('job_id', sa.BIGINT(), autoincrement=False, nullable=False),
    sa.Column('type', postgresql.ENUM('deferred', 'started', 'deferred_for_retry', 'failed', 'succeeded', 'cancelled', 'abort_requested', 'aborted', 'scheduled', name='procrastinate_job_event_type'), autoincrement=False, nullable=True),
    sa.Column('at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['procrastinate_jobs.id'], name='procrastinate_events_job_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='procrastinate_events_pkey')
    )
    with op.batch_alter_table('procrastinate_events', schema=None) as batch_op:
        batch_op.create_index('procrastinate_events_job_id_fkey_v1', ['job_id'], unique=False)

    # ### end Alembic commands ###
