import api from './api'

// Get all animal groups
export const getAnimalGroups = async () => {
  const response = await api.get('/herds/groups')
  return response.data.animal_groups
}

// Get animal group by ID
export const getAnimalGroupById = async (groupId) => {
  const response = await api.get(`/herds/groups/${groupId}`)
  return {
    animalGroup: response.data.animal_group,
    herds: response.data.herds
  }
}

// Create new animal group
export const createAnimalGroup = async (groupData) => {
  const response = await api.post('/herds/groups', groupData)
  return response.data
}

// Update animal group
export const updateAnimalGroup = async (groupId, groupData) => {
  const response = await api.put(`/herds/groups/${groupId}`, groupData)
  return response.data
}

// Delete animal group
export const deleteAnimalGroup = async (groupId) => {
  const response = await api.delete(`/herds/groups/${groupId}`)
  return response.data
}

// Assign herds to a group
export const assignHerdsToGroup = async (groupId, herdIds) => {
  const response = await api.post(`/herds/groups/${groupId}/herds`, { herd_ids: herdIds })
  return response.data
}

// Get all herds in a group
export const getGroupHerds = async (groupId) => {
  const response = await api.get(`/herds/groups/${groupId}/herds`)
  return {
    group: response.data.group,
    herds: response.data.herds,
    herdCount: response.data.herd_count
  }
}