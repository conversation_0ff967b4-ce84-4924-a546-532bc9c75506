import { useState } from 'react'
import { useParams, useNavigate, Link } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import AnimalCard from '../../components/herds/AnimalCard'
import { getHerdById, deleteHerd } from '../../services/herdService'

const HerdDetailsPage = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showMoreDetails, setShowMoreDetails] = useState(false)

  const { data: herd, isLoading, error } = useQuery(['herd', id], () => getHerdById(id))

  const deleteMutation = useMutation(() => deleteHerd(id), {
    onSuccess: () => {
      queryClient.invalidateQueries('herds')
      navigate('/herds')
    }
  })

  const handleDelete = () => {
    deleteMutation.mutate()
  }

  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    try {
      return new Date(dateString).toLocaleDateString()
    } catch (e) {
      return dateString
    }
  }

  if (isLoading) {
    return <p className="text-center py-4">{t('common.loading')}</p>
  }

  if (error) {
    return (
      <Card>
        <p className="text-red-500">{t('herds.errorLoading')}: {error.message}</p>
        <div className="mt-4">
          <Link to="/herds">
            <Button variant="outline">{t('herds.backToHerds')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  if (!herd) {
    return (
      <Card>
        <p className="text-gray-500">{t('herds.herdNotFound')}</p>
        <div className="mt-4">
          <Link to="/herds">
            <Button variant="outline">{t('herds.backToHerds')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('herds.animalDetails')}</h1>
        <div className="flex space-x-2">
          <Link to={`/herds/${id}/edit`}>
            <Button variant="outline">{t('common.edit')}</Button>
          </Link>
          <Button variant="danger" onClick={() => setShowDeleteConfirm(true)}>{t('common.delete')}</Button>
        </div>
      </div>

      <AnimalCard animal={herd} className="mb-6" />

      {herd.description && (
        <Card className="mb-6">
          <p>{herd.description}</p>
        </Card>
      )}

      <Card title={t('herds.basicInformation')} className="mb-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {herd.breed && (
            <div>
              <p className="text-xs text-gray-500">{t('herds.breed')}</p>
              <p className="text-sm font-medium">{herd.breed}</p>
            </div>
          )}
          {herd.gender && (
            <div>
              <p className="text-xs text-gray-500">{t('herds.gender')}</p>
              <p className="text-sm font-medium">{herd.gender}</p>
            </div>
          )}
          {herd.color && (
            <div>
              <p className="text-xs text-gray-500">{t('herds.color')}</p>
              <p className="text-sm font-medium">{herd.color}</p>
            </div>
          )}
          {herd.birth_weight !== null && (
            <div>
              <p className="text-xs text-gray-500">{t('herds.birthWeight')}</p>
              <p className="text-sm font-medium">{herd.birth_weight} kg</p>
            </div>
          )}
          {herd.birth_date && (
            <div>
              <p className="text-xs text-gray-500">{t('herds.birthDate')}</p>
              <p className="text-sm font-medium">{formatDate(herd.birth_date)}</p>
            </div>
          )}
          {herd.month_age !== null && (
            <div>
              <p className="text-xs text-gray-500">{t('herds.ageMonths')}</p>
              <p className="text-sm font-medium">{herd.month_age}</p>
            </div>
          )}
          {herd.father_ear_num && (
            <div>
              <p className="text-xs text-gray-500">{t('herds.fatherEarNum')}</p>
              <p className="text-sm font-medium">{herd.father_ear_num}</p>
            </div>
          )}
          {herd.mother_ear_num && (
            <div>
              <p className="text-xs text-gray-500">{t('herds.motherEarNum')}</p>
              <p className="text-sm font-medium">{herd.mother_ear_num}</p>
            </div>
          )}
        </div>

        <div className="mt-4 flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowMoreDetails(!showMoreDetails)}
          >
            {showMoreDetails ? t('common.showLess') : t('common.showMoreDetails')}
          </Button>
        </div>
      </Card>

      {showMoreDetails && (
        <>
          <Card title={t('herds.productionStatus')} className="mb-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {herd.lactation_number !== null && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.lactationNumber')}</p>
                  <p className="text-sm font-medium">{herd.lactation_number}</p>
                </div>
              )}
              {herd.group_name && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.group')}</p>
                  <p className="text-sm font-medium">{herd.group_name}</p>
                </div>
              )}
              {herd.grow_status && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.growthStatus')}</p>
                  <p className="text-sm font-medium">{herd.grow_status}</p>
                </div>
              )}
              {herd.fertility_status && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.fertilityStatus')}</p>
                  <p className="text-sm font-medium">{herd.fertility_status}</p>
                </div>
              )}
              {herd.health_status && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.healthStatus')}</p>
                  <p className="text-sm font-medium">{herd.health_status}</p>
                </div>
              )}
              {herd.disease_name && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.disease')}</p>
                  <p className="text-sm font-medium">{herd.disease_name}</p>
                </div>
              )}
              {herd.high && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.height')}</p>
                  <p className="text-sm font-medium">{herd.high}</p>
                </div>
              )}
              {herd.bust && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.bust')}</p>
                  <p className="text-sm font-medium">{herd.bust}</p>
                </div>
              )}
            </div>
          </Card>

          <Card title={t('herds.entryExitInfo')} className="mb-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {herd.entry_date && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.entryDate')}</p>
                  <p className="text-sm font-medium">{formatDate(herd.entry_date)}</p>
                </div>
              )}
              {herd.entry_type && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.entryType')}</p>
                  <p className="text-sm font-medium">{herd.entry_type}</p>
                </div>
              )}
              {herd.is_on_farm !== null && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.onFarm')}</p>
                  <p className="text-sm font-medium">{herd.is_on_farm ? t('common.yes') : t('common.no')}</p>
                </div>
              )}
              {herd.exit_date && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.exitDate')}</p>
                  <p className="text-sm font-medium">{formatDate(herd.exit_date)}</p>
                </div>
              )}
              {herd.exit_type && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.exitType')}</p>
                  <p className="text-sm font-medium">{herd.exit_type}</p>
                </div>
              )}
              {herd.exit_reason && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.exitReason')}</p>
                  <p className="text-sm font-medium">{herd.exit_reason}</p>
                </div>
              )}
            </div>
          </Card>

          <Card title={t('herds.importantDates')} className="mb-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {herd.measure_date && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.measureDate')}</p>
                  <p className="text-sm font-medium">{formatDate(herd.measure_date)}</p>
                </div>
              )}
              {herd.calving_date && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.calvingDate')}</p>
                  <p className="text-sm font-medium">{formatDate(herd.calving_date)}</p>
                </div>
              )}
              {herd.dry_date && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.dryDate')}</p>
                  <p className="text-sm font-medium">{formatDate(herd.dry_date)}</p>
                </div>
              )}
              {herd.insem_date && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.inseminationDate')}</p>
                  <p className="text-sm font-medium">{formatDate(herd.insem_date)}</p>
                </div>
              )}
              {herd.abortion_date && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.abortionDate')}</p>
                  <p className="text-sm font-medium">{formatDate(herd.abortion_date)}</p>
                </div>
              )}
              {herd.cure_date && (
                <div>
                  <p className="text-xs text-gray-500">{t('herds.cureDate')}</p>
                  <p className="text-sm font-medium">{formatDate(herd.cure_date)}</p>
                </div>
              )}
            </div>
          </Card>

          {herd.remark && (
            <Card title={t('herds.remarks')} className="mb-6">
              <p>{herd.remark}</p>
            </Card>
          )}
        </>
      )}

      <div className="mt-6">
        <Link to="/herds">
          <Button variant="outline">{t('herds.backToHerds')}</Button>
        </Link>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">{t('herds.confirmDeletion')}</h3>
            <p className="text-gray-500 mb-4">
              {t('herds.deleteConfirmationMessage', { name: herd.name })}
            </p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
                {t('common.cancel')}
              </Button>
              <Button variant="danger" onClick={handleDelete}>
                {t('common.delete')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default HerdDetailsPage