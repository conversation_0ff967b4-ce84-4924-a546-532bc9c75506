import React, { forwardRef } from 'react'
import { useTranslation } from 'react-i18next'

const Input = forwardRef((
  {
    id,
    label,
    type = 'text',
    placeholder,
    value,
    onChange,
    error,
    helpText,
    required = false,
    disabled = false,
    className = '',
    name,
    noMargin = false,
    ...props
  },
  ref
) => {
  const { t } = useTranslation()
  return (
    <div className={`${noMargin ? '' : 'mb-4'}`}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">{t('common.requiredMark')}</span>}
        </label>
      )}
      <input
        id={id}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        disabled={disabled}
        placeholder={placeholder}
        ref={ref}
        className={`
          block w-full rounded-md border-gray-300 shadow-sm h-10
          focus:border-green-500 focus:ring-green-500 sm:text-sm
          ${error ? 'border-red-300' : 'border-gray-300'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
          ${className}
        `}
        {...props}
      />
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      {helpText && !error && (
        <p className="mt-1 text-sm text-gray-500">{helpText}</p>
      )}
    </div>
  )
})

export default Input