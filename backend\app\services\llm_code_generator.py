# services/llm_code_generator.py
import json
import logging
import traceback
from flask import current_app

# Configure logging
logger = logging.getLogger(__name__)

# Import service for database schema information
from .db_schema_service import get_formatted_schema_for_llm, get_nutrients_data
from .llm_service import LLMFactory

def generate_data_import_code(sheet_name, range_address, sample_data, session_id=None):
    """
    Use LLM to generate Python code that imports Excel data directly into database tables.
    
    Uses a more flexible approach that allows the LLM more freedom in implementation.
    
    Args:
        sheet_name: Name of the worksheet
        range_address: Address of the used range
        sample_data: Sampled Excel data (clusters)
        session_id: Import session ID

    Returns:
        str: Executable Python code that imports the data into appropriate database tables
    """
    llm_provider = LLMFactory.get_current_provider()

    # Get database schema information for context
    schema_code = get_formatted_schema_for_llm()
    nutrients_data = get_nutrients_data()

    logger.info(f"Generating import code for {sheet_name}")
    logger.debug(f"Sheet: {sheet_name}, Range: {range_address}, Session ID: {session_id}")

    try:
        # Create a prompt that provides necessary information but gives LLM more freedom
        #{json.dumps(sample_data[:2] if isinstance(sample_data, list) else sample_data, indent=2, ensure_ascii=False)} alternative dump
        prompt = f"""
Your task is to analyze Excel data and create Python code to import it into a PostgreSQL database.
The Excel could from various souces and might not completely match the database shcema.
Return only runnable Python code that imports the data into appropriate database tables.

# Important Guidelines

# Data Context
- Sheet name: "{sheet_name}"
- Range: {range_address}


# Excel Data Structure
Here's a representative sample of the data:
{json.dumps(sample_data, indent=2, ensure_ascii=False)}

The excel_data variable is structured as follows:
- excel_data['sourceSheetName']: Sheet name
- excel_data['usedRangeAddress']: Excel range address
- excel_data['clusters']: List of data clusters, each containing:
  - 'id': Cluster identifier
  - 'range': Excel range of this cluster
  - 'data': A list of rows, where each row is a list of cell objects with:
    - 'r': Row index (0-based)
    - 'c': Column index (0-based)
    - 'v': Cell value - this is the main data point
    - 't': Data type of the cell

# Database Schema
```python
{schema_code}
```

Additional info for available nutrients in database:
{json.dumps(nutrients_data, indent=2, ensure_ascii=False)}...

# for herd data, animal group info would be processed seperately, you can leave it empty.

# Task Requirements
1. Analyze the Excel data structure to determine what kind of data it contains
2. Determine which database table the data should be imported into (Feed, Herd, or Ration)
3. Create Python code that will:
   - Extract data from the Excel structure
   - Insert it into the appropriate database tables
   - Mark imported records with import_status='pending_review' and import_session_id={session_id}
   - Set a variable 'record_type' to indicate which primary table type is imported (e.g., 'Feed', 'Herd', 'Ration')

# Available Variables and Functions
- 'excel_data': Contains the full Excel data described above
- 'session_id': String identifier for this import
- 'user_id': Integer ID of the current user
- 'cursor': PostgreSQL database cursor for executing SQL statements
- 'record_type': You must set this in your code to indicate the primary table
- 'import_status' : 'pending_review' NEEDED FOR DATA EVALUATION

# Best Practices
- Use parameterized queries with placeholders
- Make appropriate type conversions
- Consider relationships between tables (e.g., Feed nutrients)

# Important Error Handling Guidelines
1. DO NOT use try-except blocks around SQL operations
2. DO NOT catch and print database errors internally
3. Let SQL errors propagate to the caller
4. Our system will capture any errors and provide you with feedback

we want code errors to trigger our error correction system.

Please provide robust Python code that will accomplish this task. Focus on correctly identifying and importing the data.
"""

        logger.debug("Sending code generation prompt to LLM")
        result = llm_provider.analyze_data(prompt, max_tokens=8000, temperature=0)
        logger.info("Code generation complete")

        # Clean up the result - extract just the code
        code = result
        if "```python" in result:
            code = result.split("```python")[1].split("```")[0].strip()
        elif "```" in result:
            code = result.split("```")[1].strip()
        else:
            code = result.strip()

        return code

    except Exception as e:
        logger.error(f"Error generating import code: {str(e)}", exc_info=True)
        return f"# Error generating import code: {str(e)}"

def fix_generated_code(original_code, execution_error, import_session_id):
    """
    Use LLM to fix generated code that failed during execution.
    
    Provides more context about the error but gives LLM freedom in how to fix it.

    Args:
        original_code: The code that failed during execution
        execution_error: Detailed error information including traceback
        import_session_id: The session ID for this import

    Returns:
        str: Improved code that hopefully fixes the error
    """
    llm_provider = LLMFactory.get_current_provider()

    # Get abbreviated database schema for context
    schema_code = get_formatted_schema_for_llm()
    
    logger.info(f"Attempting to fix import code based on execution error for session {import_session_id}")

    # Create error correction prompt with detailed context but more freedom
    prompt = f"""
I need you to fix Python code that encountered an error during execution.

# Original Code
```python
{original_code}
```

# Error Information
```
{execution_error}
```

# Database Schema
```python
{schema_code}...
```



# Task
Please fix the code to address the error. The code is intended to import Excel data into a database.
It is also possible that you have made similar problems,make sure also fix them to ensure a successful execution.

Remember:
1. The code must set a variable named 'record_type' to indicate the primary table type ('Feed', 'Herd', or 'Ration')
2. Use parameterized queries with %s placeholders (PostgreSQL style)

# Available Variables and Functions
- 'excel_data': Contains the full Excel data described above
- 'session_id': String identifier for this import
- 'user_id': Integer ID of the current user
- 'cursor': PostgreSQL database cursor for executing SQL statements
- 'record_type': You must set this in your code to indicate the primary table
- 'import_status' : 'pending_review' NEEDED FOR DATA EVALUATION

Analyze the error carefully and provide a fixed version of the code.

# Important Error Handling Guidelines
1. DO NOT use try-except blocks around SQL operations
2. DO NOT catch and print database errors internally
3. Let SQL errors propagate to the caller
4. Our system will capture any errors and provide you with feedback

# for herd data, animal group info would be processed seperately, you can leave it empty.

we want code errors to trigger our error correction system.
"""

    logger.debug("Sending code fixing prompt to LLM")
    try:
        result = llm_provider.analyze_data(prompt, max_tokens=8000)
        logger.info("Code fixing attempt complete")

        # Clean up the result - extract just the code
        code = result
        if "```python" in result:
            code = result.split("```python")[1].split("```")[0].strip()
        elif "```" in result:
            code = result.split("```")[1].strip()
        else:
            code = result.strip()

        # Basic validation that the resulting code is substantial
        if len(code) < 50:
            logger.warning(f"LLM returned suspiciously short code: {len(code)} characters")
            return original_code
            
        return code

    except Exception as e:
        logger.error(f"Error during code fixing attempt: {str(e)}", exc_info=True)
        return original_code  # Return original code if fixing attempt fails