from datetime import datetime
from ..extensions import db

class Ration(db.Model):
    __tablename__ = 'rations'
    __table_args__ = {'schema': 'ration_app'}

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('ration_app.users.id'))
    animal_group_id = db.Column(db.Integer, db.ForeignKey('ration_app.animal_groups.id'))
    name = db.Column(db.String(255), nullable=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_formulated_at = db.Column(db.DateTime)
    import_status = db.Column(db.String(50), nullable=True, default='confirmed', index=True)
    import_session_id = db.Column(db.String(36), index=True)

    # Relationships
    ration_feeds = db.relationship('RationFeed', backref='ration', lazy=True, cascade='all, delete-orphan')
    constraints = db.relationship('Constraint', backref='ration', lazy=True, cascade='all, delete-orphan')

    def is_formulated(self):
        """Check if the ration has been successfully formulated by looking for feeds with actual inclusion percentages."""
        if not self.ration_feeds:
            return False
        return any(feed.actual_inclusion_percentage is not None and feed.actual_inclusion_percentage > 0 for feed in self.ration_feeds)

    def to_dict(self, include_feeds=False, include_constraints=False):
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'animal_group_id': self.animal_group_id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'last_formulated_at': self.last_formulated_at.isoformat() if self.last_formulated_at else None,
            'import_status': self.import_status,
            'import_session_id': self.import_session_id,
            'is_formulated': self.is_formulated()
        }

        if include_feeds:
            data['feeds'] = [rf.to_dict() for rf in self.ration_feeds]

        if include_constraints:
            data['constraints'] = [c.to_dict() for c in self.constraints]

        return data

class RationFeed(db.Model):
    __tablename__ = 'ration_feeds'
    __table_args__ = {'schema': 'ration_app'}

    id = db.Column(db.Integer, primary_key=True)
    ration_id = db.Column(db.Integer, db.ForeignKey('ration_app.rations.id'))
    feed_id = db.Column(db.Integer, db.ForeignKey('ration_app.feeds.id'))
    min_inclusion_percentage = db.Column(db.Float)
    max_inclusion_percentage = db.Column(db.Float)
    actual_inclusion_percentage = db.Column(db.Float)
    cost_contribution = db.Column(db.Float)

    # Relationship with feed
    feed = db.relationship('Feed')

    def to_dict(self):
        return {
            'id': self.id,
            'ration_id': self.ration_id,
            'feed_id': self.feed_id,
            'feed_name': self.feed.name if self.feed else None,
            'min_inclusion_percentage': self.min_inclusion_percentage,
            'max_inclusion_percentage': self.max_inclusion_percentage,
            'actual_inclusion_percentage': self.actual_inclusion_percentage,
            'cost_contribution': self.cost_contribution
        }