import React, { useState, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import NrcModelSelector from '../../components/rations/NrcModelSelector'
import FormulationErrorHandler from '../../components/rations/FormulationErrorHandler'
import nutrientService from '../../services/nutrientService'
import AIToggle from '../../components/rations/AIToggle'
import AIMenuButton from '../../components/rations/AIMenuButton'

const RationFormulationLayout = ({
  contextData, // Changed: receive contextData instead of formData
  feeds,
  animalGroups,
  errors,
  isFormulating,
  isSaving,
  isSaved,
  nrcData,
  selectedNrcModelId,
  optimizationSettings,
  onSave,
  onFormulate,
  onNrcModelChange,
  onFormDataChange,
  onFeedChange,
  onConstraintChange,
  onOptimizationSettingsChange,
  canFormulate,
  isReadOnly = false,
  pageTitle,
  buttonLabels = {}, // Default empty object for button labels
  showSaveButton = true, // Default to showing save button
  showFormulateButton = true, // Default to showing formulate button
  // AI Assistant props
  aiAssistEnabled = false,
  onAiToggle = () => {},
  onOpenAssistant = () => {}
}) => {
  const { t, i18n } = useTranslation();
  const currentLocale = i18n.language;

  // Extract form values from contextData
  const formName = contextData?.current_formulation_state?.ration_name || '';
  const formDescription = contextData?.current_formulation_state?.description || '';
  const formAnimalGroupId = contextData?.current_formulation_state?.animal_group_id || '';
  const formFeeds = contextData?.feed_data || [];
  const formConstraints = contextData?.constraints || [];
  const formulationResult = contextData?.formulation_result;

  const [feedSearchTerm, setFeedSearchTerm] = useState('')
  const [nutrients, setNutrients] = useState([])
  // Fetch nutrients from API
  useEffect(() => {
    const fetchNutrients = async () => {
      try {
        const data = await nutrientService.getNutrients()
        setNutrients(data)
      } catch (error) {
        console.error('Error fetching nutrients:', error)
      }
    }

    fetchNutrients()
  }, []);

  // Define available constraint types
  const constraintTypes = useMemo(() => {
    // Always include DMI as it's a special constraint
    const baseConstraints = [
      { value: 'DMI', label: t('animalGroups.dryMatterIntake', 'Dry Matter Intake') }
    ]

    // Add nutrients from the database
    const nutrientOptions = nutrients.map(nutrient => ({
      value: nutrient.name_en,
      label: nutrient.name_zh && currentLocale === 'zh' ? nutrient.name_zh : nutrient.name_en
    }))

    // If no nutrients are loaded yet, add some defaults
    if (nutrientOptions.length === 0) {
      return [
        ...baseConstraints,
        { value: 'Crude Protein', label: t('feeds.protein') },
        { value: 'NDF', label: 'NDF' },
        { value: 'ADF', label: 'ADF' },
        { value: 'Fat', label: t('feeds.fat', 'Fat') },
        { value: 'Calcium', label: t('feeds.calcium', 'Calcium') },
        { value: 'Phosphorus', label: t('feeds.phosphorus', 'Phosphorus') },
        { value: 'NEL', label: 'NEL' }
      ]
    }

    return [...baseConstraints, ...nutrientOptions]
  }, [t, nutrients, currentLocale]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target
    onFormDataChange(name, value)
  }

  // Filter feeds based on search term
  const filteredFeeds = feeds.filter(feed =>
    feed.name?.toLowerCase().includes(feedSearchTerm.toLowerCase())
  )

  const [constraintsExpanded, setConstraintsExpanded] = useState(true);

  return (
    <div className="flex flex-col h-full gap-6">
      {/* Header with save/formulate buttons */}
      <div className="bg-white shadow rounded-lg px-6 py-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {pageTitle || (contextData.ration_id ? t('rations.editRation') : t('rations.createRation'))}
            </h1>
            {formName && <p className="text-sm text-gray-500 mt-1">{formName}</p>}
          </div>

          <div className="flex items-center space-x-2 mt-4 sm:mt-0">
            {/* AI Toggle and Menu Button */}
            <div className="flex items-center space-x-2 mr-2">
              <AIToggle enabled={aiAssistEnabled} onChange={onAiToggle} />
              <AIMenuButton
                onOpenAssistant={onOpenAssistant}
                enabled={aiAssistEnabled}
                formData={{
                  name: formName,
                  animal_group_id: formAnimalGroupId
                }}
              />
            </div>

            {!isSaved && !isFormulating && (
              <div className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                {t('common.unsavedChanges')}
              </div>
            )}

            {isFormulating && (
              <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('rations.formulating')}
              </div>
            )}

            {showSaveButton && (
              <Button
                variant={formulationResult ? "primary" : "outline"}
                onClick={onSave}
                disabled={isSaving || isFormulating}
                size="sm"
              >
                {isSaving ? t('common.saving') : buttonLabels.saveButton || (formulationResult ? t('common.saveAndFinish') : t('common.saveAsDraft'))}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col md:flex-row gap-6 flex-1">
        {/* Left sidebar */}
        <div className="w-full md:w-80 flex flex-col gap-6">
          {/* Basic information */}
          <Card className="bg-white shadow rounded-lg">
            <h2 className="text-lg font-medium text-gray-900 mb-4">{t('common.basicInformation')}</h2>

            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('rations.rationName')} <span className="text-red-500">*</span>
                </label>
                <input
                  id="name"
                  name="name"
                  value={formName}
                  onChange={handleChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                  required
                  readOnly={isReadOnly}
                />
                {errors?.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('common.description')}
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows="2"
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                  value={formDescription}
                  onChange={handleChange}
                ></textarea>
              </div>

              <div>
                <label htmlFor="animal_group_id" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('rations.animalGroup')} <span className="text-red-500">*</span>
                </label>
                <select
                  id="animal_group_id"
                  name="animal_group_id"
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                  value={formAnimalGroupId}
                  onChange={handleChange}
                  required
                >
                  <option value="">{t('common.selectOption')}</option>
                  {animalGroups.map(group => (
                    <option key={group.id} value={group.id}>
                      {group.name}{group.milk_production_kg ? ` (${group.milk_production_kg} kg milk)` : ''}
                    </option>
                  ))}
                </select>
                {errors?.animal_group_id && (
                  <p className="mt-1 text-sm text-red-600">{errors.animal_group_id}</p>
                )}
              </div>

              {formAnimalGroupId && (
                <div className="bg-green-50 border-l-4 border-green-400 p-3">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-green-700">
                        {t('rations.nrcRequirementsAvailable')}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Feeds section */}
          <Card className="bg-white shadow rounded-lg flex-grow">
            <h2 className="text-lg font-medium text-gray-900 mb-4">{t('rations.feeds')}</h2>

            {/* Feed selector */}
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">{t('rations.availableFeeds')}</h3>
              <div className="border border-gray-200 rounded-md p-3 bg-gray-50">
                <input
                  placeholder={t('feeds.search')}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm mb-2"
                  value={feedSearchTerm}
                  onChange={(e) => setFeedSearchTerm(e.target.value)}
                />
                <div className="divide-y divide-gray-200 max-h-48 overflow-y-auto">
                  {filteredFeeds.length === 0 ? (
                    <div className="py-4 text-center text-sm text-gray-500">
                      {feedSearchTerm ? t('feeds.noMatchingFeeds') : t('feeds.noFeedsAvailable')}
                    </div>
                  ) : (
                    filteredFeeds.map(feed => {
                    const isSelected = formFeeds.some(f => f.feed_id == feed.id || f.id == feed.id);
                    return (
                      <div key={feed.id} className="py-2 flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{feed.name}</div>
                          <div className="text-xs text-gray-500">
                            {t('feeds.dmShort')}: {(feed.dry_matter_percentage*100).toFixed(2)}%
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant={isSelected ? "secondary" : "outline"}
                          onClick={() => !isSelected && onFeedChange([
                            ...formFeeds,
                            {
                              feed_id: feed.id,
                              min_inclusion_percentage: 0,
                              max_inclusion_percentage: 100,
                              actual_inclusion_percentage: null,
                              cost_contribution: null
                            }
                          ])}
                          disabled={isSelected}
                        >
                          {isSelected ? t('common.added') : t('common.add')}
                        </Button>
                      </div>
                    );
                    })
                  )}
                </div>
              </div>
            </div>

            {/* Selected feeds table */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">{t('rations.selectedFeeds')}</h3>
              {formFeeds.length === 0 ? (
                <div className="text-center py-4 text-gray-500 border border-dashed border-gray-300 rounded-md">
                  {t('rations.noFeedsSelected')}
                </div>
              ) : (
                <div className="overflow-hidden border border-gray-200 rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('feeds.feedName')}
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {formFeeds.map((feed) => {
                        const feedInfo = feeds.find(f => f.id == feed.feed_id || f.id == feed.id);
                        const feedName = feedInfo ? feedInfo.name : `Feed ${feed.feed_id || feed.id}`;
                        return (
                          <tr key={feed.feed_id || feed.id}>
                            <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                              {feedName}
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                type="button"
                                onClick={() => onFeedChange(formFeeds.filter(f =>
                                  (f.feed_id !== feed.feed_id && f.id !== feed.id) &&
                                  (f.feed_id !== feed.id && f.id !== feed.feed_id)
                                ))}
                                className="text-red-600 hover:text-red-900"
                              >
                                {t('common.remove')}
                              </button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
              {errors.feeds && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.feeds}
                </p>
              )}
            </div>
          </Card>
        </div>

        {/* Right main area */}
        <div className="flex-1 flex flex-col gap-6">
          {/* Requirements section */}
          <Card id="nutritional-requirements" className="bg-white shadow rounded-lg">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium text-gray-900">{t('rations.nutritionalRequirements')}</h2>
                <button
                onClick={() => setConstraintsExpanded(!constraintsExpanded)}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
                aria-expanded={constraintsExpanded}
                aria-label={constraintsExpanded ? t('common.collapse') : t('common.expand')}
                >
                {constraintsExpanded ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                    </svg>
                )}
                </button>
            </div>

            {/* NRC Model Selector */}
            <div className="mb-4">
                <NrcModelSelector
                selectedModelId={selectedNrcModelId}
                onModelChange={onNrcModelChange}
                />
            </div>

            {constraintsExpanded && (
                <div className="space-y-4">
                <div className="flex justify-between items-center">
                    <h3 className="text-sm font-medium text-gray-700">{t('rations.nutritionalConstraints')}</h3>
                    <div className="flex space-x-2">
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Find constraint types that aren't already in use
                          const usedTypes = new Set(formConstraints.map(c => c.nutrient_name));
                          const availableType = constraintTypes.find(type => !usedTypes.has(type.value));

                          if (availableType) {
                            onConstraintChange([
                              ...formConstraints,
                              {
                                nutrient_name: availableType.value,
                                min_value: null,
                                max_value: null,
                                actual_value: null
                              }
                            ]);
                          } else {
                            // If all types are used, just add the first one again
                            onConstraintChange([
                              ...formConstraints,
                              {
                                nutrient_name: constraintTypes[0].value,
                                min_value: null,
                                max_value: null,
                                actual_value: null
                              }
                            ]);
                          }
                        }}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="-ml-1 mr-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                        {t('rations.addConstraint')}
                    </Button>

                    <Button
                        type="button"
                        variant="secondary"
                        size="sm"
                        onClick={() => {
                          // Clear all constraints
                          onConstraintChange([]);
                        }}
                    >
                        {t('rations.clearConstraints')}
                    </Button>

                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (nrcData?.nrc_requirements?.constraints) {
                            // Map all constraints including DMI
                            const nrcConstraints = Object.entries(nrcData.nrc_requirements.constraints).map(([name, values]) => ({
                              nutrient_name: name,
                              min_value: values.min,
                              max_value: values.max,
                              actual_value: null
                            }));

                            onConstraintChange(nrcConstraints);
                          }
                        }}
                    >
                        {t('rations.resetToModel')}
                    </Button>
                    </div>
                </div>

                {formConstraints.length > 0 ? (
                    <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                        <tr>
                            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('rations.nutrientName')}
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('rations.min')}
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('rations.max')}
                            </th>
                            <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('common.actions')}
                            </th>
                        </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                        {formConstraints.map((constraint, index) => (
                            <tr key={index}>
                            <td className="px-3 py-2 whitespace-nowrap">
                                <div className="w-full">
                                  <select
                                    value={constraint.nutrient_name}
                                    onChange={(e) => {
                                      const updatedConstraints = [...formConstraints];
                                      updatedConstraints[index] = {
                                        ...updatedConstraints[index],
                                        nutrient_name: e.target.value
                                      };
                                      onConstraintChange(updatedConstraints);
                                    }}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                                  >
                                    {constraintTypes.map((option) => (
                                      <option key={option.value} value={option.value}>
                                        {option.label}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                                <input
                                type="number"
                                step="0.01"
                                value={constraint.min_value === null ? '' : constraint.min_value}
                                onChange={(e) => {
                                    const updatedConstraints = [...formConstraints];
                                    updatedConstraints[index] = {
                                    ...updatedConstraints[index],
                                    min_value: e.target.value === '' ? null : parseFloat(e.target.value)
                                    };
                                    onConstraintChange(updatedConstraints);
                                }}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                                />
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                                <input
                                type="number"
                                step="0.01"
                                value={constraint.max_value === null ? '' : constraint.max_value}
                                onChange={(e) => {
                                    const updatedConstraints = [...formConstraints];
                                    updatedConstraints[index] = {
                                    ...updatedConstraints[index],
                                    max_value: e.target.value === '' ? null : parseFloat(e.target.value)
                                    };
                                    onConstraintChange(updatedConstraints);
                                }}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                                />
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-right">
                                <button
                                type="button"
                                className="text-red-600 hover:text-red-900"
                                onClick={() => {
                                    const updatedConstraints = [...formConstraints];
                                    updatedConstraints.splice(index, 1);
                                    onConstraintChange(updatedConstraints);
                                }}
                                >
                                {t('common.remove')}
                                </button>
                            </td>
                            </tr>
                        ))}
                        </tbody>
                    </table>
                    </div>
                ) : (
                    <div className="py-6 text-center text-gray-500 border border-dashed border-gray-300 rounded-md">
                    {t('rations.noConstraintsDefined')}
                    </div>
                )}
                </div>
            )}
            </Card>

          {/* Formulation section */}
          <Card className="bg-white shadow rounded-lg flex-grow">
            <h2 className="text-lg font-medium text-gray-900 mb-4">{t('rations.formulation')}</h2>

            {canFormulate ? (
              <div className="space-y-6">
                {/* Optimization settings */}
                <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">{t('optimization.settings')}</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">
                        {t('optimization.objective')}
                      </label>
                      <select
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                        value={optimizationSettings.objective}
                        onChange={(e) => onOptimizationSettingsChange({
                          ...optimizationSettings,
                          objective: e.target.value
                        })}
                      >
                        <option value="minimize_cost">{t('optimization.minimizeCost')}</option>
                        <option value="balance_nutrients">{t('optimization.balanceNutrients')}</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">
                        {t('optimization.maxIterations')}
                      </label>
                      <input
                        type="number"
                        min="100"
                        max="10000"
                        step="100"
                        value={optimizationSettings.maxIterations}
                        onChange={(e) => onOptimizationSettingsChange({
                          ...optimizationSettings,
                          maxIterations: parseInt(e.target.value)
                        })}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">
                        {t('optimization.convergenceTolerance')}
                      </label>
                      <input
                        type="number"
                        min="0.0001"
                        max="0.01"
                        step="0.0001"
                        value={optimizationSettings.tolerance}
                        onChange={(e) => onOptimizationSettingsChange({
                          ...optimizationSettings,
                          tolerance: parseFloat(e.target.value)
                        })}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <Button
                      onClick={onFormulate}
                      disabled={isFormulating || !formName}
                      className="w-full"
                      size="sm"
                    >
                      {isFormulating
                        ? t('rations.formulating')
                        : formulationResult
                          ? t('rations.reformulate')
                          : t('rations.formulate')
                      }
                    </Button>
                    {!formName && (
                      <p className="mt-2 text-xs text-red-600 text-center">
                        {t('validation.nameRequired')}
                      </p>
                    )}
                  </div>
                </div>

                {/* Results area */}
                {formulationResult ? (
                  formulationResult.status === 'success' ? (
                    <div className="space-y-6">
                      {/* Status and stats */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-green-50 p-4 rounded-lg text-center">
                          <div className="text-sm font-medium text-green-800">{t('rations.formulationStatus')}</div>
                          <div className="mt-1 text-2xl font-semibold text-green-900">
                            {t('common.success')}
                          </div>
                        </div>

                        <div className="bg-blue-50 p-4 rounded-lg text-center">
                          <div className="text-sm font-medium text-blue-800">{t('rations.totalCost')}</div>
                          <div className="mt-1 text-2xl font-semibold text-blue-900">
                          {t('common.currencySymbol')}{formulationResult.actual_total_cost?.toFixed(2)}
                          </div>
                          <div className="mt-1 text-xs text-blue-700">
                            {t('rations.perDayPerAnimal')}
                          </div>
                        </div>

                        <div className="bg-purple-50 p-4 rounded-lg text-center">
                          <div className="text-sm font-medium text-purple-800">{t('rations.constraintsMet')}</div>
                          <div className="mt-1 text-2xl font-semibold text-purple-900">
                            {Object.values(formulationResult.nutrients || {}).every(n =>
                              (!n.min_value || n.actual_value >= n.min_value) &&
                              (!n.max_value || n.actual_value <= n.max_value)
                            ) ? t('common.yes') : t('common.partial')}
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-yellow-50 p-4 rounded-lg">
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="text-sm font-medium text-yellow-800">{t('animalGroups.dryMatterIntake')}</div>
                              <div className="mt-1 text-2xl font-semibold text-yellow-900">
                                {formulationResult.nutrients?.DMI?.actual_value?.toFixed(2)} kg
                              </div>
                              <div className="mt-1 text-xs text-yellow-700">
                                {t('rations.perDayPerAnimal')}
                              </div>
                            </div>
                            <div className="text-yellow-500">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                              </svg>
                            </div>
                          </div>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg">
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="text-sm font-medium text-green-800">{t('rations.costPerKgDM')}</div>
                              <div className="mt-1 text-2xl font-semibold text-green-900">
                                {t('common.currencySymbol')}{formulationResult.cost_per_kg_dm?.toFixed(2)}
                              </div>
                              <div className="mt-1 text-xs text-green-700">
                                {t('rations.perKgDryMatter')}
                              </div>
                            </div>
                            <div className="text-green-500">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Feed composition table */}
                      <div>
                        <h3 className="text-sm font-medium text-gray-700 mb-2">{t('rations.feedComposition')}</h3>
                        <div className="overflow-hidden border border-gray-200 rounded-md">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('feeds.feedName')}
                                </th>
                                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('rations.inclusion')}
                                </th>
                                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('rations.min')}
                                </th>
                                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('rations.max')}
                                </th>
                                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('rations.cost')}
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {formFeeds.map(feed => {
                                const feedInfo = feeds.find(f => f.id == feed.feed_id || f.id == feed.id);
                                const feedName = feedInfo ? feedInfo.name : `Feed ${feed.feed_id || feed.id}`;
                                const feedResult = formulationResult.feeds[feed.feed_id || feed.id];

                                if (!feedResult) return null;

                                // Check if feed is at min or max inclusion
                                const isAtMin = Math.abs(feedResult.inclusion_percentage - feed.min_inclusion_percentage) < 0.01;
                                const isAtMax = Math.abs(feedResult.inclusion_percentage - feed.max_inclusion_percentage) < 0.01;

                                // Add visual indicator for feeds at their limits
                                let inclusionClassName = "font-medium text-green-600";
                                if (isAtMin) inclusionClassName = "font-medium text-orange-600";
                                if (isAtMax) inclusionClassName = "font-medium text-orange-600";

                                return (
                                  <tr key={feed.feed_id || feed.id}>
                                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                      {feedName}
                                    </td>
                                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                                      <span className={inclusionClassName}>
                                        {(feedResult.inclusion_percentage).toFixed(2)}%
                                        {isAtMin && (
                                          <span className="ml-1 text-xs text-orange-600">
                                            ({t('rations.atMinimumInclusion')})
                                          </span>
                                        )}
                                        {isAtMax && (
                                          <span className="ml-1 text-xs text-orange-600">
                                            ({t('rations.atMaximumInclusion')})
                                          </span>
                                        )}
                                      </span>
                                    </td>
                                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                      {feed.min_inclusion_percentage}%
                                    </td>
                                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                      {feed.max_inclusion_percentage}%
                                    </td>
                                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                      {t('common.currencySymbol')}{feedResult.cost_contribution?.toFixed(2)}
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                            <tfoot className="bg-gray-50">
                              <tr>
                                <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {t('common.total')}
                                </td>
                                <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                  100%
                                </td>
                                <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500"></td>
                                <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500"></td>
                                <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {t('common.currencySymbol')}{formulationResult.actual_total_cost?.toFixed(2)}
                                  <span className="ml-1 text-xs text-gray-500">({t('rations.perDayPerAnimal')})</span>
                                </td>
                              </tr>
                            </tfoot>
                          </table>
                        </div>
                      </div>
                      {/* Nutrients results */}
                      <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">{t('rations.nutrientResults')}</h3>
                      <div className="overflow-hidden border border-gray-200 rounded-md">
                          <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                              <tr>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('rations.nutrient')}
                              </th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('rations.actual')}
                              </th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('rations.min')}
                              </th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('rations.max')}
                              </th>
                              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {t('rations.status')}
                              </th>
                              </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                              {formulationResult && formulationResult.nutrients &&
                              Object.entries(formulationResult.nutrients).map(([name, values]) => {
                                  // Safely check for null/undefined values
                                  const actualValue = values.actual_value !== null && values.actual_value !== undefined;
                                  const minValue = values.min_value !== null && values.min_value !== undefined;
                                  const maxValue = values.max_value !== null && values.max_value !== undefined;

                                  // Check requirements
                                  // Define nutrients that are typically expressed as percentages
                                  const percentageNutrients = [
                                    'Calcium', 'Phosphorus', 'Magnesium', 'Potassium', 'Sodium', 'Sulfur', 'Chloride', 'Chlorine',
                                    'ADF', 'NDF', 'Crude Protein', 'CP', 'Fat', 'Starch', 'Sugar', '酸性洗涤纤维', '中性洗涤纤维', '粗蛋白'
                                  ];

                                  // Handle percentage vs decimal format mismatch
                                  let comparisonValue = values.actual_value;
                                  let minComparisonValue = values.min_value;
                                  let maxComparisonValue = values.max_value;

                                  // If actual is decimal (0-1) but constraints are percentage (0-100)
                                  if (percentageNutrients.includes(name) &&
                                      values.actual_value < 1.0 &&
                                      ((minValue && values.min_value > 1.0) || (maxValue && values.max_value > 1.0))) {
                                    // Convert actual to percentage for comparison
                                    comparisonValue = values.actual_value * 100;
                                  }
                                  // If actual is percentage (0-100) but constraints are decimal (0-1)
                                  else if (percentageNutrients.includes(name) &&
                                          values.actual_value > 1.0 &&
                                          ((minValue && values.min_value < 1.0 && values.min_value > 0) ||
                                          (maxValue && values.max_value < 1.0 && values.max_value > 0))) {
                                    // Convert constraints to percentage for comparison
                                    if (minValue) minComparisonValue = values.min_value * 100;
                                    if (maxValue) maxComparisonValue = values.max_value * 100;
                                  }

                                  const isWithinConstraints =
                                    (!minValue || comparisonValue >= minComparisonValue) &&
                                    (!maxValue || comparisonValue <= maxComparisonValue);

                                  return (
                                  <tr key={name}>
                                      <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                      {/* Display localized nutrient name if available */}
                                      {(() => {
                                        // Find the nutrient in our nutrients list to get the localized name
                                        const nutrient = nutrients.find(n => n.name_en === name);
                                        if (nutrient && nutrient.name_zh && currentLocale === 'zh') {
                                          return nutrient.name_zh;
                                        }
                                        return name;
                                      })()}
                                      </td>
                                      <td className="px-3 py-2 whitespace-nowrap text-sm">
                                      <span className="font-medium text-blue-600">
                                          {actualValue ? (
                                            // For percentage nutrients with decimal values, display as percentage
                                            percentageNutrients.includes(name) && values.actual_value < 1.0 &&
                                            ((minValue && values.min_value > 1.0) || (maxValue && values.max_value > 1.0))
                                              ? (values.actual_value * 100).toFixed(2)
                                              : values.actual_value.toFixed(2)
                                          ) : '-'}
                                      </span>
                                      </td>
                                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                      {minValue ? values.min_value.toFixed(2) : '-'}
                                      </td>
                                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                      {maxValue ? values.max_value.toFixed(2) : '-'}
                                      </td>
                                      <td className="px-3 py-2 whitespace-nowrap text-sm">
                                      {isWithinConstraints ? (
                                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                          {t('rations.requirementMet')}
                                          </span>
                                      ) : (
                                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                          {t('rations.requirementNotMet')}
                                          </span>
                                      )}
                                      </td>
                                  </tr>
                                  );
                              })
                              }
                          </tbody>
                          </table>
                      </div>
                      </div>
                    </div>
                  ) : (
                    <FormulationErrorHandler
                      error={formulationResult}
                      status={formulationResult.status || 'error'}
                      conflictingConstraints={formulationResult.conflicting_constraints || []}
                      onRetry={onFormulate}
                      onAdjust={() => {
                        // Scroll to constraints section
                        document.querySelector('#nutritional-requirements')?.scrollIntoView({
                          behavior: 'smooth',
                          block: 'start'
                        });
                      }}
                    />
                  )
                ) : (
                  <div className="bg-gray-50 border border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">{t('rations.readyToFormulate')}</h3>
                    <p className="mt-2 text-sm text-gray-500">{t('rations.readyToFormulateDescription')}</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-yellow-50 border border-dashed border-yellow-300 rounded-lg p-8">
                <div className="text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01M10 21h4a2 2 0 002-2v-6a2 2 0 00-2-2h-4a2 2 0 00-2 2v6a2 2 0 002 2z" />
                  </svg>
                  <h3 className="mt-4 text-lg font-medium text-yellow-800">{t('rations.cannotFormulate')}</h3>
                  <p className="mt-2 text-sm text-yellow-700">{t('rations.completeBasicInfoFirst')}</p>
                  <ul className="mt-4 list-disc list-inside text-sm text-yellow-700">
                    {!formName && <li>{t('validation.nameRequired')}</li>}
                    {!formAnimalGroupId && <li>{t('rations.selectAnimalGroup')}</li>}
                    {!formFeeds.length && <li>{t('rations.addFeeds')}</li>}
                  </ul>
                </div>

                {/* Troubleshooting section */}
                <div className="mt-8 pt-6 border-t border-yellow-200">
                  <h4 className="text-md font-medium text-yellow-800">{t('rations.troubleshootingTitle')}</h4>
                  <p className="mt-2 text-sm text-yellow-700">{t('rations.troubleshootingDescription')}</p>

                  <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                      <h5 className="font-medium text-yellow-800">{t('rations.checkConstraints')}</h5>
                      <p className="mt-1 text-sm text-gray-600">{t('rations.checkConstraintsDescription')}</p>
                    </div>

                    <div className="bg-white p-4 rounded-lg shadow-sm">
                      <h5 className="font-medium text-yellow-800">{t('rations.checkFeeds')}</h5>
                      <p className="mt-1 text-sm text-gray-600">{t('rations.checkFeedsDescription')}</p>
                    </div>

                    <div className="bg-white p-4 rounded-lg shadow-sm">
                      <h5 className="font-medium text-yellow-800">{t('rations.checkInclusions')}</h5>
                      <p className="mt-1 text-sm text-gray-600">{t('rations.checkInclusionsDescription')}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  )
}

export default RationFormulationLayout