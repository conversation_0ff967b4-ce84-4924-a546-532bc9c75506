"""rename metadata column

Revision ID: rename_metadata_column
Revises: dc25f7339226
Create Date: 2025-04-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'rename_metadata_column'
down_revision = 'dc25f7339226'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # This migration is empty because we've already renamed the column in the model
    # The column was already created with the correct name 'article_metadata' in the previous migration
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # This migration is empty because we've already renamed the column in the model
    # The column was already created with the correct name 'article_metadata' in the previous migration
    pass
    # ### end Alembic commands ###
