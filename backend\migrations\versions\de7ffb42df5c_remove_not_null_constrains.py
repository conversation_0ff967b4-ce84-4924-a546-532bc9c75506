"""remove not-null constrains

Revision ID: de7ffb42df5c
Revises: be0cc1779bc3
Create Date: 2025-04-10 16:54:44.367115

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'de7ffb42df5c'
down_revision = 'be0cc1779bc3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
        batch_op.drop_constraint('animal_groups_herd_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'herds', ['herd_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint('constraints_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.alter_column('feed_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('nutrient_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('value',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
        batch_op.drop_constraint('feed_nutrients_nutrient_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('feed_nutrients_feed_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'nutrients', ['nutrient_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app', ondelete='CASCADE')

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
        batch_op.alter_column('dry_matter_percentage',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
        batch_op.alter_column('cost_per_kg',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
        batch_op.alter_column('import_status',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
        batch_op.drop_constraint('feeds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
        batch_op.alter_column('import_status',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
        batch_op.drop_constraint('herds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('nutrients', schema=None) as batch_op:
        batch_op.alter_column('name_en',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
        batch_op.alter_column('unit',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
        batch_op.drop_constraint('nutrients_name_en_key', type_='unique')

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint('ration_feeds_feed_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('ration_feeds_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
        batch_op.alter_column('import_status',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
        batch_op.drop_constraint('rations_user_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('rations_animal_group_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'animal_groups', ['animal_group_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint('staged_import_sessions_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('staged_import_sessions_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('rations_animal_group_id_fkey', 'animal_groups', ['animal_group_id'], ['id'])
        batch_op.create_foreign_key('rations_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.alter_column('import_status',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('ration_feeds_ration_id_fkey', 'rations', ['ration_id'], ['id'])
        batch_op.create_foreign_key('ration_feeds_feed_id_fkey', 'feeds', ['feed_id'], ['id'])

    with op.batch_alter_table('nutrients', schema=None) as batch_op:
        batch_op.create_unique_constraint('nutrients_name_en_key', ['name_en'])
        batch_op.alter_column('unit',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
        batch_op.alter_column('name_en',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('herds_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.alter_column('import_status',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feeds_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.alter_column('import_status',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
        batch_op.alter_column('cost_per_kg',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
        batch_op.alter_column('dry_matter_percentage',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feed_nutrients_feed_id_fkey', 'feeds', ['feed_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('feed_nutrients_nutrient_id_fkey', 'nutrients', ['nutrient_id'], ['id'])
        batch_op.alter_column('value',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
        batch_op.alter_column('nutrient_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('feed_id',
               existing_type=sa.INTEGER(),
               nullable=False)

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('constraints_ration_id_fkey', 'rations', ['ration_id'], ['id'])

    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('animal_groups_herd_id_fkey', 'herds', ['herd_id'], ['id'])
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)

    # ### end Alembic commands ###
