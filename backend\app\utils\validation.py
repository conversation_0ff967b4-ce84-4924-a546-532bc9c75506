def validate_feed_data(data):
    """
    Validate feed data
    
    Args:
        data: Feed data to validate
        
    Returns:
        tuple: (is_valid, errors)
    """
    errors = {}
    
    # Check required fields
    if not data.get('name'):
        errors['name'] = 'Name is required'
        
    if 'dry_matter_percentage' not in data or not isinstance(data['dry_matter_percentage'], (int, float)):
        errors['dry_matter_percentage'] = 'Dry matter percentage is required and must be a number'
    elif data['dry_matter_percentage'] <= 0 or data['dry_matter_percentage'] > 100:
        errors['dry_matter_percentage'] = 'Dry matter percentage must be between 0 and 100'
        
    if 'cost_per_kg' not in data or not isinstance(data['cost_per_kg'], (int, float)):
        errors['cost_per_kg'] = 'Cost per kg is required and must be a number'
    elif data['cost_per_kg'] < 0:
        errors['cost_per_kg'] = 'Cost per kg must be non-negative'
    
    # Validate nutrients if provided
    if data.get('nutrients'):
        if not isinstance(data['nutrients'], list):
            errors['nutrients'] = 'Nutrients must be a list'
        else:
            nutrient_errors = []
            
            for i, nutrient in enumerate(data['nutrients']):
                nutrient_error = {}
                
                if not nutrient.get('name'):
                    nutrient_error['name'] = 'Nutrient name is required'
                    
                if 'value' not in nutrient or not isinstance(nutrient['value'], (int, float)):
                    nutrient_error['value'] = 'Nutrient value is required and must be a number'
                
                if nutrient_error:
                    nutrient_errors.append({
                        'index': i,
                        'errors': nutrient_error
                    })
            
            if nutrient_errors:
                errors['nutrients'] = nutrient_errors
    
    return (len(errors) == 0, errors)

def validate_herd_data(data):
    """
    Validate herd data
    
    Args:
        data: Herd data to validate
        
    Returns:
        tuple: (is_valid, errors)
    """
    errors = {}
    
    # Check required fields
    if not data.get('name'):
        errors['name'] = 'Name is required'
    
    return (len(errors) == 0, errors)

def validate_animal_group_data(data):
    """
    Validate animal group data
    
    Args:
        data: Animal group data to validate
        
    Returns:
        tuple: (is_valid, errors)
    """
    errors = {}
    
    # Check required fields
    if not data.get('name'):
        errors['name'] = 'Name is required'
        
    # Check numeric fields if provided
    if 'weight_kg' in data and data['weight_kg'] is not None:
        if not isinstance(data['weight_kg'], (int, float)):
            errors['weight_kg'] = 'Weight must be a number'
        elif data['weight_kg'] <= 0:
            errors['weight_kg'] = 'Weight must be positive'
            
    if 'milk_production_kg' in data and data['milk_production_kg'] is not None:
        if not isinstance(data['milk_production_kg'], (int, float)):
            errors['milk_production_kg'] = 'Milk production must be a number'
        elif data['milk_production_kg'] < 0:
            errors['milk_production_kg'] = 'Milk production must be non-negative'
            
    if 'days_in_milk' in data and data['days_in_milk'] is not None:
        if not isinstance(data['days_in_milk'], int):
            errors['days_in_milk'] = 'Days in milk must be an integer'
        elif data['days_in_milk'] < 0:
            errors['days_in_milk'] = 'Days in milk must be non-negative'
    
    return (len(errors) == 0, errors)

def validate_ration_data(data):
    """
    Validate ration data
    
    Args:
        data: Ration data to validate
        
    Returns:
        tuple: (is_valid, errors)
    """
    errors = {}
    
    # Check required fields
    if not data.get('name'):
        errors['name'] = 'Name is required'
        
    if not data.get('animal_group_id'):
        errors['animal_group_id'] = 'Animal group ID is required'
    
    # Validate feeds if provided
    if data.get('feeds'):
        if not isinstance(data['feeds'], list):
            errors['feeds'] = 'Feeds must be a list'
        else:
            feed_errors = []
            
            for i, feed in enumerate(data['feeds']):
                feed_error = {}
                
                if not feed.get('feed_id'):
                    feed_error['feed_id'] = 'Feed ID is required'
                
                # Check inclusion ranges if provided
                if 'min_inclusion_percentage' in feed and feed['min_inclusion_percentage'] is not None:
                    if not isinstance(feed['min_inclusion_percentage'], (int, float)):
                        feed_error['min_inclusion_percentage'] = 'Minimum inclusion percentage must be a number'
                    elif feed['min_inclusion_percentage'] < 0 or feed['min_inclusion_percentage'] > 100:
                        feed_error['min_inclusion_percentage'] = 'Minimum inclusion percentage must be between 0 and 100'
                
                if 'max_inclusion_percentage' in feed and feed['max_inclusion_percentage'] is not None:
                    if not isinstance(feed['max_inclusion_percentage'], (int, float)):
                        feed_error['max_inclusion_percentage'] = 'Maximum inclusion percentage must be a number'
                    elif feed['max_inclusion_percentage'] < 0 or feed['max_inclusion_percentage'] > 100:
                        feed_error['max_inclusion_percentage'] = 'Maximum inclusion percentage must be between 0 and 100'
                    
                # Check if min <= max
                if ('min_inclusion_percentage' in feed and feed['min_inclusion_percentage'] is not None and
                    'max_inclusion_percentage' in feed and feed['max_inclusion_percentage'] is not None and
                    feed['min_inclusion_percentage'] > feed['max_inclusion_percentage']):
                    feed_error['inclusion_range'] = 'Minimum inclusion percentage cannot be greater than maximum'
                
                if feed_error:
                    feed_errors.append({
                        'index': i,
                        'errors': feed_error
                    })
            
            if feed_errors:
                errors['feeds'] = feed_errors
    
    # Validate constraints if provided
    if data.get('constraints'):
        if not isinstance(data['constraints'], list):
            errors['constraints'] = 'Constraints must be a list'
        else:
            constraint_errors = []
            
            for i, constraint in enumerate(data['constraints']):
                constraint_error = {}
                
                if not constraint.get('nutrient_name'):
                    constraint_error['nutrient_name'] = 'Nutrient name is required'
                
                # Check if min <= max
                if ('min_value' in constraint and constraint['min_value'] is not None and
                    'max_value' in constraint and constraint['max_value'] is not None and
                    constraint['min_value'] > constraint['max_value']):
                    constraint_error['value_range'] = 'Minimum value cannot be greater than maximum value'
                
                if constraint_error:
                    constraint_errors.append({
                        'index': i,
                        'errors': constraint_error
                    })
            
            if constraint_errors:
                errors['constraints'] = constraint_errors
    
    return (len(errors) == 0, errors)