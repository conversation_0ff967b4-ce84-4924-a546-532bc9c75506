// src/pages/kb/KBAskPage.jsx
import React, { useState, useRef, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useMutation } from 'react-query'
import { useTranslation } from 'react-i18next'
import i18n from 'i18next'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { nord } from 'react-syntax-highlighter/dist/esm/styles/prism'

import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Input from '../../components/ui/Input'
import { askQuestion } from '../../services/kbService'

// Source Link component
const SourceLink = ({ source }) => {
  const { t } = useTranslation()

  // Format relevance score
  const relevanceScore = source.score ? Math.round(source.score * 100) : null

  // Get color based on relevance
  const getRelevanceColor = (score) => {
    if (!score) return ''
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-blue-600'
    if (score >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <li className="mb-2">
      <Link
        to={`/kb/articles/${source.slug}`}
        className="text-blue-600 hover:text-blue-800 hover:underline flex items-center"
        target="_blank"
        rel="noopener noreferrer"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
        </svg>
        {source.title}
      </Link>
      {relevanceScore && (
        <span className={`ml-2 text-xs ${getRelevanceColor(relevanceScore)}`}>
          ({relevanceScore}% {t('kb.relevance')})
        </span>
      )}
    </li>
  )
}

// QA Pair component
const QAPair = ({ question, answer, sources }) => {
  const { t } = useTranslation()
  const [showSources, setShowSources] = useState(false)

  return (
    <Card className="mb-6">
      <div>
        <h3 className="font-medium text-gray-900 mb-2">
          <span className="text-blue-600 mr-2">Q:</span>{question}
        </h3>

        <div className="pl-6 border-l-2 border-green-500 py-1">
          <div className="prose prose-sm max-w-none">
            <ReactMarkdown
              children={answer}
              remarkPlugins={[remarkGfm]}
              components={{
                code({ node, inline, className, children, ...props }) {
                  const match = /language-(\w+)/.exec(className || '')
                  return !inline && match ? (
                    <SyntaxHighlighter
                      children={String(children).replace(/\n$/, '')}
                      style={nord}
                      language={match[1]}
                      PreTag="div"
                      {...props}
                    />
                  ) : (
                    <code className={className} {...props}>
                      {children}
                    </code>
                  )
                }
              }}
            />
          </div>

          {sources && sources.length > 0 && (
            <div className="mt-4">
              <button
                className="text-sm font-medium text-blue-600 hover:text-blue-800 flex items-center"
                onClick={() => setShowSources(!showSources)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 mr-1 transition-transform ${showSources ? 'rotate-90' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
                {showSources ? t('kb.hideSources') : t('kb.showSources')} ({sources.length})
              </button>

              {showSources && (
                <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    {t('kb.sources')}:
                  </h4>
                  <ul className="space-y-1">
                    {sources.map((source, i) => (
                      <SourceLink key={i} source={source} />
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Card>
  )
}

const KBAskPage = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [question, setQuestion] = useState('')
  const [answerHistory, setAnswerHistory] = useState([])
  const [contextSize, setContextSize] = useState(3)
  const answerEndRef = useRef(null)

  // Ask question mutation
  const askMutation = useMutation(
    (questionText) => {
      // Get the current locale from i18n
      const currentLocale = i18n.language || 'en';
      return askQuestion(questionText, contextSize, currentLocale);
    },
    {
      onSuccess: (data) => {
        // Add the QA pair to history
        setAnswerHistory(prev => [
          {
            question,
            answer: data.answer,
            sources: data.sources,
            timestamp: new Date().toISOString()
          },
          ...prev
        ])

        // Clear the input
        setQuestion('')
      }
    }
  )

  const handleSubmit = (e) => {
    e.preventDefault()

    if (question.trim()) {
      askMutation.mutate(question)
    }
  }

  // Auto-scroll to the new answer
  useEffect(() => {
    if (answerHistory.length > 0 && !askMutation.isLoading) {
      answerEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [answerHistory, askMutation.isLoading]);

  return (
    <div>
      <div className="bg-white shadow-sm rounded-lg p-6 mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {t('kb.askAIAssistant')}
        </h1>
        <p className="text-gray-600 mb-4">
          {t('kb.askAIAssistantDescription')}
        </p>

        <form onSubmit={handleSubmit} className="mt-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-grow">
              <Input
                type="text"
                placeholder={t('kb.askPlaceholder')}
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                className="flex-grow rounded-r-none"
                noMargin={true}
              />
            </div>

            <div className="flex space-x-2">
              <div className="w-40">
                <select
                  value={contextSize}
                  onChange={(e) => setContextSize(Number(e.target.value))}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm h-10"
                >
                  <option value={1}>{t('kb.minContext')}</option>
                  <option value={3}>{t('kb.defaultContext')}</option>
                  <option value={5}>{t('kb.maxContext')}</option>
                </select>
              </div>

              <Button
                type="submit"
                className="flex items-center h-10"
                disabled={askMutation.isLoading || !question.trim()}
              >
                {askMutation.isLoading ? t('common.thinking') : t('kb.ask')}
              </Button>
            </div>
          </div>

          <div className="mt-2 text-xs text-gray-500">
            {t('kb.contextSizeExplanation')}
          </div>
        </form>
      </div>

      {askMutation.isLoading && (
        <Card className="mb-6">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-green-500"></div>
            <span>{t('kb.thinkingAboutQuestion')}</span>
          </div>
        </Card>
      )}

      {answerHistory.length > 0 && (
        <div>
          {answerHistory.map((qa, index) => (
            <QAPair
              key={index}
              question={qa.question}
              answer={qa.answer}
              sources={qa.sources}
            />
          ))}
          <div ref={answerEndRef} />
        </div>
      )}

      {!askMutation.isLoading && answerHistory.length === 0 && (
        <Card className="mb-6">
          <div className="text-center py-8">
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              {t('kb.askAnything')}
            </h2>
            <p className="text-gray-600 mb-6 max-w-lg mx-auto">
              {t('kb.askAnythingDescription')}
            </p>
            <div className="space-y-2">
              <div
                className="p-2 border rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => setQuestion(t('kb.presetQuestion1'))}
              >
                <p className="text-gray-900">{t('kb.presetQuestion1')}</p>
              </div>
              <div
                className="p-2 border rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => setQuestion(t('kb.presetQuestion2'))}
              >
                <p className="text-gray-900">{t('kb.presetQuestion2')}</p>
              </div>
              <div
                className="p-2 border rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => setQuestion(t('kb.presetQuestion3'))}
              >
                <p className="text-gray-900">{t('kb.presetQuestion3')}</p>
              </div>
            </div>
          </div>
        </Card>
      )}

      {askMutation.error && (
        <Card className="mb-6 bg-red-50">
          <div className="text-red-700 p-4">
            <h3 className="font-medium">{t('common.error')}</h3>
            <p>{askMutation.error.message || t('kb.errorProcessingQuestion')}</p>
          </div>
        </Card>
      )}

      <div className="flex justify-between mt-6">
        <Link to="/kb">
          <Button variant="outline">
            {t('kb.backToKnowledgeBase')}
          </Button>
        </Link>

        <Link to="/kb/search">
          <Button variant="outline">
            {t('kb.searchInstead')}
          </Button>
        </Link>
      </div>
    </div>
  )
}

export default KBAskPage