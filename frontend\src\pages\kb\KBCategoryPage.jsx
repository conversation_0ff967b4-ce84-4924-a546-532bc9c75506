import React, { useState } from 'react'
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'

import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import { getCategoryBySlug, getArticles } from '../../services/kbService'

const KBCategoryPage = () => {
  const { t } = useTranslation()
  const { slug } = useParams()
  const navigate = useNavigate()
  const [currentPage, setCurrentPage] = useState(1)
  const limit = 10
  
  // Get category
  const { 
    data: category,
    isLoading: categoryLoading,
    error: categoryError
  } = useQuery(
    ['kb-category', slug], 
    () => getCategoryBySlug(slug),
    { refetchOnWindowFocus: false }
  )
  
  // Get articles in this category
  const { 
    data: articlesData = { articles: [], total: 0 },
    isLoading: articlesLoading,
    error: articlesError
  } = useQuery(
    ['kb-category-articles', slug, currentPage], 
    () => getArticles({
      category_slug: slug,
      published: true,
      page: currentPage,
      limit: limit,
      sort_by: 'created_at',
      sort_order: 'desc'
    }),
    {
      enabled: !!slug,
      keepPreviousData: true,
      refetchOnWindowFocus: false
    }
  )
  
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage)
    // Scroll to top when changing pages
    window.scrollTo(0, 0)
  }
  
  if (categoryLoading || articlesLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    )
  }
  
  if (categoryError || !category) {
    return (
      <Card>
        <div className="text-center py-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('kb.categoryNotFound')}</h2>
          <p className="text-gray-600 mb-4">{t('kb.categoryNotFoundDescription')}</p>
          <Button onClick={() => navigate('/kb')} variant="outline">
            {t('kb.backToKnowledgeBase')}
          </Button>
        </div>
      </Card>
    )
  }
  
  return (
    <div>
      {/* Breadcrumb */}
      <div className="flex items-center text-sm mb-4">
        <Link to="/kb" className="text-blue-600 hover:text-blue-800">
          {t('kb.home')}
        </Link>
        <span className="mx-2 text-gray-500">/</span>
        <span className="text-gray-500 truncate">
          {category.name}
        </span>
      </div>
      
      {/* Category header */}
      <Card className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{category.name}</h1>
        {category.description && (
          <p className="text-gray-600 text-lg">{category.description}</p>
        )}
      </Card>
      
      {/* Articles list */}
      <Card title={`${t('kb.articles')} (${articlesData.total})`}>
        {articlesLoading ? (
          <p className="text-center py-4">{t('common.loading')}</p>
        ) : articlesError ? (
          <div className="text-center py-4">
            <p className="text-red-600 mb-2">{t('kb.errorLoadingArticles')}</p>
            <Button onClick={() => navigate('/kb')} variant="outline" size="sm">
              {t('kb.backToKnowledgeBase')}
            </Button>
          </div>
        ) : articlesData.articles.length === 0 ? (
          <p className="text-center py-4">{t('kb.noCategoryArticles')}</p>
        ) : (
          <>
            <div className="divide-y divide-gray-200">
              {articlesData.articles.map(article => (
                <div key={article.id} className="py-4">
                  <Link 
                    to={`/kb/articles/${article.slug}`}
                    className="block hover:bg-gray-50 -mx-4 px-4 py-2 rounded-lg transition-colors"
                  >
                    <h2 className="text-xl font-semibold text-blue-600 hover:text-blue-800 mb-1">
                      {article.title}
                    </h2>
                    {article.summary && (
                      <p className="text-gray-600 mb-2">{article.summary}</p>
                    )}
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>
                        {new Date(article.created_at).toLocaleDateString()}
                      </span>
                      <span>{article.view_count} {t('kb.views')}</span>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
            
            {/* Pagination */}
            {articlesData.pages > 1 && (
              <div className="mt-6 flex justify-between items-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  {t('common.previous')}
                </Button>
                
                <span className="text-sm text-gray-500">
                  {t('common.page')} {currentPage} {t('common.of')} {articlesData.pages}
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= articlesData.pages}
                >
                  {t('common.next')}
                </Button>
              </div>
            )}
          </>
        )}
      </Card>
      
      {/* Back button */}
      <div className="mt-6">
        <Button
          variant="outline"
          onClick={() => navigate('/kb')}
        >
          {t('kb.backToKnowledgeBase')}
        </Button>
      </div>
    </div>
  )
}

export default KBCategoryPage