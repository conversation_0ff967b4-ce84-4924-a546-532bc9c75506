import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Tab } from '@headlessui/react';
import { useKBStats } from '../../hooks/useKBStats';
import StatsCard from '../../components/ui/StatsCard';
import ArticlesTab from '../../components/kb/ArticlesTab';
import CategoriesTab from '../../components/kb/CategoriesTab';
import CreateArticleTab from '../../components/kb/CreateArticleTab';
import KBEmbeddingsTab from '../../components/kb/KBEmbeddingsTab';
import useAuthStore from '../../store/authStore';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export default function KBAdminPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();
  const { data: stats = {}, isLoading: statsLoading } = useKBStats();
  const [selectedTab, setSelectedTab] = useState(0);

  useEffect(() => {
    if (!isAuthenticated) navigate('/login');
  }, [isAuthenticated, navigate]);

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <section className="bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-6 shadow">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">{t('kb.adminTitle')}</h1>
        <p className="text-gray-600 mb-6 max-w-2xl">
          {t('kb.adminDescription')}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <StatsCard title={t('kb.totalArticles')} value={stats.total_articles} loading={statsLoading} />
          <StatsCard title={t('kb.publishedArticles')} value={stats.published_articles} loading={statsLoading} />
          <StatsCard title={t('kb.totalCategories')} value={stats.total_categories} loading={statsLoading} />
          <StatsCard title={t('kb.totalViews')} value={stats.total_views} loading={statsLoading} />
        </div>
      </section>

      {/* Tabs Section */}
      <Tab.Group selectedIndex={selectedTab} onChange={setSelectedTab}>
        <Tab.List className="flex bg-gray-100 p-1 rounded-lg mb-6 shadow">
          {[
            { key: 'articles', label: t('kb.articles') },
            { key: 'categories', label: t('kb.categories') },
            { key: 'create', label: t('kb.createArticle') },
            { key: 'embeddings', label: t('kb.embeddingsStatus') }
          ].map(tab => (
            <Tab
              key={tab.key}
              className={({ selected }) => classNames(
                'w-full py-3 px-4 text-sm font-medium rounded-lg focus:outline-none transition-all',
                selected
                  ? 'bg-white shadow text-blue-700'
                  : 'text-gray-600 hover:bg-white/[0.12] hover:text-gray-800'
              )}
            >
              {tab.label}
            </Tab>
          ))}
        </Tab.List>
        <Tab.Panels className="bg-white rounded-lg shadow p-6">
          <Tab.Panel><ArticlesTab /></Tab.Panel>
          <Tab.Panel><CategoriesTab /></Tab.Panel>
          <Tab.Panel><CreateArticleTab /></Tab.Panel>
          <Tab.Panel><KBEmbeddingsTab /></Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
}