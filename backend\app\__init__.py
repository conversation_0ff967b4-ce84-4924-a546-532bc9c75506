import os
from flask import Flask, request
from .extensions import db, jwt, cors # Assuming these are defined correctly
from .config import config # Assuming this maps config_name to config objects
import logging
from sqlalchemy import event, text
from pgvector.psycopg2 import register_vector
from .services.tasks import generate_embedding_task, process_animal_group_task, process_animal_groups_batch_task
from procrastinate import App as ProcrastinateApp
from procrastinate import PsycopgConnector
import tiktoken
import semchunk

# Initialize logger for this module
logger = logging.getLogger(__name__) # Use __name__ for module-specific logger

def create_app(config_name=None):
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')

    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # --- Logging Configuration ---
    log_formatter = logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    )
    log_level = logging.DEBUG if app.config.get('DEBUG') else logging.INFO

    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(log_formatter)

    root_logger = logging.getLogger()
    # Prevent duplicate handlers if create_app is called multiple times (e.g., tests)
    if not root_logger.handlers or not any(isinstance(h, logging.StreamHandler) for h in root_logger.handlers):
         # Clear existing handlers (like Flask's default) before adding our own
         # Be cautious if other parts of your system might add handlers expect them to persist
         root_logger.handlers.clear()
         root_logger.addHandler(stream_handler)
         root_logger.setLevel(log_level)
         # Propagate level setting to prevent handlers from blocking messages
         for handler in root_logger.handlers:
              handler.setLevel(log_level)
         root_logger.info(f'Root logger configured with level {logging.getLevelName(log_level)}.')
    else:
         root_logger.info("Root logger already configured.")
         # Ensure level is set correctly even if handlers exist
         root_logger.setLevel(log_level)
         for handler in root_logger.handlers:
              handler.setLevel(log_level)

    # Initialize Flask extensions
    db.init_app(app)
    jwt.init_app(app)
    cors.init_app(app)

    def connect(dbapi_connection, connection_record):
        logger.info("Registering pgvector type handler for connection.")
        try:
            register_vector(dbapi_connection)
        except Exception as e:
            logger.error(f"Failed to register pgvector type handler: {e}",exc_info=True)

    # Register blueprints for routes
    # Ensure these imports work based on your project structure
    from .routes import auth, feeds, herds, rations, excel_import, kb, nrc
    app.register_blueprint(auth.bp)
    app.register_blueprint(feeds.bp)
    app.register_blueprint(herds.bp)
    app.register_blueprint(rations.bp)
    app.register_blueprint(excel_import.bp)
    app.register_blueprint(kb.bp)
    app.register_blueprint(nrc.bp)

    # Register CLI commands Blueprint
    from . import cli # Import the cli module where bp is defined
    app.register_blueprint(cli.bp)
    logger.info("Registered blueprints and CLI commands.")

    # --- Initialize Procrastinate ---
    try:
        logger.info("Initializing Procrastinate...")
        # Get connection info - prefer DATABASE_URL from Flask config
        database_url = app.config.get('DATABASE_URL') or app.config.get('SQLALCHEMY_DATABASE_URI')

        # Construct from parts only if URL not found in config
        if not database_url:
             db_host = app.config.get('DB_HOST', 'localhost')
             db_port = app.config.get('DB_PORT', 5432)
             db_user = app.config.get('DB_USER', 'postgres')
             db_password = app.config.get('DB_PASSWORD', '') # Ensure password handling is secure
             db_name = app.config.get('DB_NAME', 'postgres')
             # Ensure components are valid before formatting
             if all([db_host, db_user, db_name]): # Password can be empty
                  database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
                  logger.info("Constructed Database URL from parts.")
             else:
                  logger.error("Missing DB connection parameters (DB_HOST, DB_USER, DB_NAME) for constructing URL.")
                  raise ValueError("Missing DB connection parameters.")

        # Final check for a valid PostgreSQL URL
        if not database_url or not database_url.startswith('postgresql'):
             logger.error(f"Invalid or missing PostgreSQL connection URL: {database_url}")
             raise ValueError("A valid PostgreSQL connection URL (DATABASE_URL or SQLALCHEMY_DATABASE_URI) is required for Procrastinate.")

        # Avoid logging passwords if possible
        log_db_url = database_url.split('@')[1] if '@' in database_url else database_url
        logger.info(f"Using Database URL: postgresql://<user>:<pwd>@{log_db_url}")

        # Get the target schema, default to 'public'
        schema = app.config.get('PROCRASTINATE_SCHEMA', 'public')

        # Prepare the dictionary for the 'kwargs' argument of PsycopgConnector
        # This will hold connection options like 'options' for search_path
        connector_options_kwargs = {}
        if schema != 'public':
            logger.info(f"Configuring Procrastinate kwargs for custom schema: {schema} using search_path option")
            # Set the search_path via connection options. Include 'extensions' and 'public'.
            connector_options_kwargs["options"] = f"-c search_path={schema},extensions,public"
        else:
            logger.info("Using default 'public' schema for Procrastinate but adding extensions schema")
            connector_options_kwargs["options"] = "-c search_path=public,extensions"

        # Initialize the connector:
        # Pass the database URL string directly to conninfo
        # Pass the dictionary prepared above to the kwargs argument
        connector = PsycopgConnector(
            conninfo=database_url,
            kwargs=connector_options_kwargs
        )
        logger.info("PsycopgConnector initialized.")

        # Create the Procrastinate App instance
        procrastinate = ProcrastinateApp(
            connector=connector,
            # Consider adding app_name for clarity if running multiple apps
            # app_name=app.config.get('PROCRASTINATE_APP_NAME', 'flask_app')
        )
        logger.info("Procrastinate App instance created.")

        # --- Register Tasks ---
        # Ensure the task function is imported correctly above
        procrastinate.task(queue="embeddings", name="generate_embedding_task")(generate_embedding_task)
        procrastinate.task(queue="herds", name="process_animal_group_task")(process_animal_group_task)
        procrastinate.task(queue="herds", name="process_animal_groups_batch_task")(process_animal_groups_batch_task)
        logger.info("Registered Procrastinate tasks: generate_embedding_task,process_animal_group_task,process_animal_groups_batch_task")
        # Register other tasks here if needed...

        logger.info("Procrastinate app instance stored via set_procrastinate_app.")

        # --- Schema Application (Optional - Consider CLI) ---
        # If you uncomment this, ensure it runs appropriately (e.g., only once)
        # with app.app_context():
        #    try:
        #        logger.info("Attempting to apply Procrastinate schema...")
        #        with procrastinate.open() as conn_ctx:
        #             procrastinate.schema_manager.apply_schema()
        #        logger.info("Procrastinate schema applied successfully (or already exists).")
        #    except Exception as schema_exc:
        #        logger.error(f"Failed to apply Procrastinate schema: {schema_exc}", exc_info=True)


        # Attach procrastinate app to Flask app instance if needed for direct access in routes/views
        app.procrastinate = procrastinate

        # --- Register Ration Agent ---
        from .routes import ration_agent
        app.register_blueprint(ration_agent.bp)

        logger.info("Procrastinate initialization complete.")

    except Exception as e:
        # Log the exception with traceback before raising
        logger.exception(f"FATAL: Failed to initialize Procrastinate: {str(e)}", exc_info=True)
        # Re-raise a specific error to halt app creation if Procrastinate is critical
        raise RuntimeError("Procrastinate initialization failed") from e

    # --- Initialize tokenizer and chunker---
    try:
        encoding = tiktoken.get_encoding(app.config.get('TIKTOKEN_ENCODING_NAME'))
    except Exception as e:
        logger.error(f"Could not initialize tiktoken encoding '{app.config.get('TIKTOKEN_ENCODING_NAME')}': {e}. Falling back to basic split.")
        # Fallback basic word count if tiktoken fails
        encoding = lambda text: len(text.split())

    app.text_encoding = encoding
    logger.info("Initialized tiktoken encoding.")

    # Initialize the semchunk chunker
    try:
        chunker = semchunk.chunkerify(encoding, app.config.get('TARGET_TOKEN_COUNT'))
        logger.info(f"semchunk initialized with tiktoken '{app.config.get('TIKTOKEN_ENCODING_NAME')}' and target size {app.config.get('TARGET_TOKEN_COUNT')}.")
    except Exception as e:
        logger.error(f"Could not initialize semchunk chunker: {e}. Chunking will be disabled.")
        chunker = None # Disable chunking if initialization fails

    app.text_chunker = chunker

    # --- Request/Response Hooks & Routes ---

    # Debug middleware to log requests (Consider removing/disabling in production)
    @app.before_request
    def log_request_info():
        # Limit logging noise, maybe only if DEBUG is True
        if app.config.get("DEBUG"):
             try:
                  # Log selectively, avoid logging potentially sensitive paths always
                  # Example: log only non-static requests
                  if not request.path.startswith('/static'):
                      logger.debug(f"Request: {request.method} {request.path}")
                      # Add more details if needed, be careful with sensitive data
                      # if request.is_json: logger.debug(f" JSON: {request.get_json(silent=True)}")

             except Exception as e:
                  logger.error(f"Error in log_request_info middleware: {e}", exc_info=True)


    # Debug middleware to log auth headers (Consider removing/disabling in production)
    @app.before_request
    def log_auth_header():
         if app.config.get("DEBUG"):
              try:
                   # Check specific paths if needed
                   if '/api/' in request.path and request.path != '/api/auth/login':
                        auth_header = request.headers.get('Authorization')
                        if auth_header:
                             # Avoid logging the full token in production logs
                             log_auth = auth_header[:15] + "..." if len(auth_header) > 15 else auth_header
                             logger.debug(f"Auth header found for {request.path}: {log_auth}")
                        # else:
                        #      logger.debug(f"No auth header for {request.path}") # Can be noisy
              except Exception as e:
                   logger.error(f"Error in log_auth_header middleware: {e}", exc_info=True)

    @app.route('/health')
    def health_check():
        # Basic health check, could add checks for DB, etc.
        return {'status': 'healthy'}, 200

    logger.info(f"Flask app '{app.name}' created successfully with config '{config_name}'.")
    return app