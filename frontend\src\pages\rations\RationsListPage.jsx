// src/pages/rations/RationsListPage.jsx
import { useState } from 'react'
import { Link } from 'react-router-dom'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Pagination from '../../components/ui/Pagination'
import { getRations } from '../../services/rationService'

const RationsListPage = () => {
  const { t } = useTranslation()
  const [currentPage, setCurrentPage] = useState(1)
  const ITEMS_PER_PAGE = 10

  // Use React Query with pagination
  const {
    data: rationsData = { rations: [], page: 1, totalPages: 0, totalItems: 0 },
    isLoading,
    error,
    refetch,
    isFetching
  } = useQuery(
    ['rations', currentPage, ITEMS_PER_PAGE],
    () => getRations(currentPage, ITEMS_PER_PAGE),
    { keepPreviousData: true }
  )

  // Extract data for convenience
  const { rations = [], totalPages = 0, totalItems = 0 } = rationsData || {}

  // Handle page change
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage)
    window.scrollTo(0, 0) // Scroll to top when changing pages
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('rations.title')}</h1>
        <Link to="/rations/new">
          <Button>{t('rations.createNew')}</Button>
        </Link>
      </div>

      {isLoading ? (
        <p className="text-center py-4">{t('common.loading')}</p>
      ) : error ? (
        <Card>
          <p className="text-red-500">{t('rations.errorLoading')}: {error.message}</p>
          <Button onClick={() => refetch()} className="mt-2" variant="outline">{t('common.tryAgain')}</Button>
        </Card>
      ) : rations.length === 0 ? (
        <Card>
          <div className="text-center py-4">
            <p className="text-gray-500 mb-4">{t('rations.noRationsFoundLong')}</p>
            <Link to="/rations/new">
              <Button>{t('rations.createNew')}</Button>
            </Link>
          </div>
        </Card>
      ) : (
        <>
          {/* Total rations count */}
          <p className="mb-4 text-sm text-gray-500">
            {totalItems} {t('rations.totalRations')}
          </p>
          <div className="grid grid-cols-1 gap-4">
            {rations.map(ration => (
              // --- Removed the outer Link here ---
              <Card key={ration.id} className="hover:shadow-md transition-shadow duration-200">
                <div className="flex flex-col sm:flex-row justify-between sm:items-center">
                  {/* Ration Info */}
                  <div className="mb-4 sm:mb-0">
                    <Link to={`/rations/${ration.id}`} className="block"> {/* Link for name */}
                      <h3 className="text-lg font-medium text-blue-600 hover:text-blue-800">{ration.name}</h3>
                    </Link>
                    {ration.description && (
                      <p className="text-sm text-gray-500 mt-1 line-clamp-2">{ration.description}</p>
                    )}
                     <p className="text-xs text-gray-400 mt-1">
                        {ration.last_formulated_at
                          ? `${t('rations.lastFormulated')}: ${new Date(ration.last_formulated_at).toLocaleDateString()}`
                          : t('rations.notYetFormulated')
                        }
                      </p>
                  </div>

                  {/* Status and Actions */}
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    {ration.is_formulated ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {t('rations.formulated')}
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        {t('rations.notFormulated')}
                      </span>
                    )}
                    {/* Separate Link/Button for View Details */}
                    <Link to={`/rations/${ration.id}`}>
                       <Button variant="outline" size="sm">{t('common.view')}</Button>
                    </Link>
                    {/* Separate Link/Button for Formulate */}
                    <Link to={`/rations/${ration.id}/formulate`}>
                      <Button size="sm">
                         {ration.is_formulated
                           ? t('rations.reformulate')
                           : t('rations.formulate')}
                      </Button>
                    </Link>
                  </div>
                </div>
              </Card>
              // --- End of Card ---
            ))}
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            isLoading={isFetching}
          />
        </>
      )}
    </div>
  )
}

export default RationsListPage