# Excel Template Analyzer

This is an Excel add-in that allows you to analyze Excel templates using AI and extract structured data.

## Architecture

The solution consists of two main parts:

1. **Excel Add-in**: A TypeScript/HTML application that runs inside Excel and allows users to select worksheets and send data to the server.
2. **Backend Server**: A Flask application that processes the Excel data using LLMs and provides a review interface.

## Setting Up the Excel Add-in

### Prerequisites

- Node.js 14 or later
- npm or yarn
- Office 365 (for testing in Excel)

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Build the project:
   ```
   npm run build
   ```
4. Start the dev server:
   ```
   npm run dev-server
   ```
5. Sideload the add-in in Excel:
   - In Excel, go to Insert > Add-ins > My Add-ins
   - Choose "Upload My Add-in" and select the manifest.xml file

## Setting Up the Backend Server

### Prerequisites

- Python 3.8 or later
- pip

### Installation

1. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```
   pip install flask flask-cors pandas numpy openai google-generativeai
   ```

3. Set up environment variables:
   
   Create a `.env` file with the following variables:
   ```
   LLM_PROVIDER=openai
   OPENAI_API_KEY=your_openai_api_key
   # If using Gemini
   # GEMINI_API_KEY=your_gemini_api_key
   # If using DeepSeek
   # DEEPSEEK_API_KEY=your_deepseek_api_key
   ```

4. Run the server:
   ```
   python app.py
   ```

## Directory Structure

```
excel-template-analyzer/
├── src/
│   ├── taskpane/
│   │   ├── taskpane.html
│   │   ├── taskpane.css
│   │   └── taskpane.ts
│   └── commands/
│       ├── commands.html
│       └── commands.ts
├── server/
│   ├── app.py
│   ├── config.py
│   ├── models/
│   │   └── session.py
│   ├── services/
│   │   ├── excel_service.py
│   │   └── llm_service.py
│   └── static/
│       └── index.html
├── manifest.xml
├── .eslintrc.json
├── babel.config.json
├── package.json
├── tsconfig.json
├── webpack.config.js
└── README.md
```

## Usage

1. Launch Excel and open your template file
2. Go to the Home tab and click on the "Excel Template Analyzer" add-in
3. In the add-in panel:
   - Enter the server URL (default: http://localhost:5000)
   - Select a worksheet from the dropdown
   - Click "Analyze Workbook Structure" to see information about the worksheet
   - Click "Extract & Send Data" to send the data to the server
4. Follow the link provided to review your data in the web interface
5. Use the chat interface to make corrections to the data structure

## Development

### Building the Add-in

```
npm run build
```

For development with auto-reloading:
```
npm run dev-server
```

### Running the Server in Development Mode

```
python app.py
```

## License

MIT