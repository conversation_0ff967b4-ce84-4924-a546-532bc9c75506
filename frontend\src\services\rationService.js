import api from './api'

// Get all rations with pagination
export const getRations = async (page = 1, limit = 10) => {
  const response = await api.get('/rations', {
    params: { page, limit }
  })
  return {
    rations: response.data.rations,
    page: response.data.page,
    totalPages: response.data.totalPages,
    totalItems: response.data.totalItems
  }
}

// Get ration by ID
export const getRationById = async (id) => {
  const response = await api.get(`/rations/${id}`)
  return response.data.ration
}

// Create new ration
export const createRation = async (rationData) => {
  const response = await api.post('/rations', rationData)
  return response.data
}

// Update ration
export const updateRation = async (id, rationData) => {
  const response = await api.put(`/rations/${id}`, rationData)
  return response.data
}

// Delete ration
export const deleteRation = async (id) => {
  const response = await api.delete(`/rations/${id}`)
  return response.data
}

// Formulate ration with optional optimization settings
export const formulateRation = async (feedsData, constraintsData, optimizationSettings = {}) => {
  try {
    const response = await api.post('/rations/formulate', {
      feeds: feedsData,
      constraints: constraintsData,
      formulation_settings: {
        time_limit_seconds: optimizationSettings.maxIterations ? optimizationSettings.maxIterations / 100 : 10,
        ...optimizationSettings
      }
    })
    return response.data
  } catch (error) {
    // If the error response contains result data (like infeasible solution details),
    // extract and return it so our error handler can display helpful information
    if (error.response && error.response.data && error.response.data.result) {
      return {
        message: error.response.data.message || 'Formulation failed',
        result: error.response.data.result
      }
    }

    // Otherwise, throw the error to be caught by the mutation's onError handler
    throw error
  }
}

// Save formulation result to a ration
export const saveFormulationResult = async (rationId, formulationResult) => {
  const response = await api.post(`/rations/${rationId}/save-formulation`, {
    formulation_result: formulationResult
  })
  return response.data
}