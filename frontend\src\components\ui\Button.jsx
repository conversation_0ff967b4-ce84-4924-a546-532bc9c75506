import React from 'react'

const Button = ({
  children,
  type = 'button',
  variant = 'primary',
  size = 'md',
  disabled = false,
  onClick,
  className = '',
  fullWidth = false,
  ...props
}) => {

  let variantClasses = ''

  switch (variant) {
    case 'primary':
      variantClasses = 'bg-green-600 hover:bg-green-700 text-white'
      break
    case 'secondary':
      variantClasses = 'bg-gray-200 hover:bg-gray-300 text-gray-800'
      break
    case 'outline':
      variantClasses = 'bg-transparent border border-green-600 text-green-600 hover:bg-green-50'
      break
    case 'danger':
      variantClasses = 'bg-red-600 hover:bg-red-700 text-white'
      break
    default:
      variantClasses = 'bg-green-600 hover:bg-green-700 text-white'
  }

  let sizeClasses = ''

  switch (size) {
    case 'sm':
      sizeClasses = 'py-1 px-3 text-sm h-8'
      break
    case 'md':
      sizeClasses = 'py-2 px-4 text-sm h-10'
      break
    case 'lg':
      sizeClasses = 'py-2 px-6 text-base h-12'
      break
    default:
      sizeClasses = 'py-2 px-4 text-sm h-10'
  }

  const widthClass = fullWidth ? 'w-full' : ''

  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : 'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'

  return (
    <button
      type={type}
      className={`${variantClasses} ${sizeClasses} ${widthClass} ${disabledClasses} font-medium rounded-md shadow-sm flex items-center justify-center ${className}`} // <-- Added flex, items-center, and justify-center
      disabled={disabled}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  )
}

export default Button