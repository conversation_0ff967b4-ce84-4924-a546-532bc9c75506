from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime
from ..models import Herd, AnimalGroup
from ..extensions import db
from ..services.tasks import queue_animal_group_processing
from ..services.nrc8_requirements import get_nrc8_requirements


bp = Blueprint('herds', __name__, url_prefix='/api/herds')

@bp.route('', methods=['GET'])
@jwt_required()
def get_herds():
    current_user_id = get_jwt_identity()

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)

    # Get user's herds with pagination
    query = Herd.query.filter_by(user_id=int(current_user_id)).order_by(Herd.created_at.desc())

    # Apply pagination
    pagination = query.paginate(page=page, per_page=limit, error_out=False)
    herds = pagination.items

    return jsonify({
        'herds': [herd.to_dict() for herd in herds],
        'page': page,
        'totalPages': pagination.pages,
        'totalItems': pagination.total
    }), 200

@bp.route('', methods=['POST'])
@jwt_required()
def create_herd():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    # Validate required fields
    if not data or not data.get('name'):
        return jsonify({'message': 'Name is required'}), 400

    # Parse date strings into datetime objects
    date_fields = ['entry_date', 'measure_date', 'birth_date', 'exit_date',
                  'calving_date', 'cure_date', 'abortion_date', 'insem_date', 'dry_date']

    for field in date_fields:
        if data.get(field):
            try:
                data[field] = datetime.fromisoformat(data[field].replace('Z', '+00:00'))
            except (ValueError, TypeError):
                # If date parsing fails, set to None
                data[field] = None

    # Create new herd with all fields
    herd = Herd(
        user_id=int(current_user_id),
        name=data['name'],
        description=data.get('description'),

        # Extended fields
        ear_num=data.get('ear_num'),
        gender=data.get('gender'),
        birth_weight=data.get('birth_weight'),
        breed=data.get('breed'),
        color=data.get('color'),
        father_ear_num=data.get('father_ear_num'),
        mother_ear_num=data.get('mother_ear_num'),
        entry_date=data.get('entry_date'),
        entry_type=data.get('entry_type'),
        group_name=data.get('group_name'),
        lactation_number=data.get('lactation_number'),
        month_age=data.get('month_age'),
        grow_status=data.get('grow_status'),
        fertility_status=data.get('fertility_status'),
        high=data.get('high'),
        bust=data.get('bust'),
        measure_date=data.get('measure_date'),
        birth_date=data.get('birth_date'),
        is_on_farm=data.get('is_on_farm'),
        exit_date=data.get('exit_date'),
        exit_type=data.get('exit_type'),
        exit_reason=data.get('exit_reason'),
        calving_date=data.get('calving_date'),
        cure_date=data.get('cure_date'),
        abortion_date=data.get('abortion_date'),
        insem_date=data.get('insem_date'),
        curedate=data.get('curedate'),
        pd1_date=data.get('pd1_date'),
        pd2_date=data.get('pd2_date'),
        disease_date=data.get('disease_date'),
        health_status=data.get('health_status'),
        disease_name=data.get('disease_name'),
        create_time=data.get('create_time'),
        remark=data.get('remark'),
        dry_date=data.get('dry_date')
    )

    db.session.add(herd)
    db.session.commit()

    queue_animal_group_processing(herd.id)


    return jsonify({
        'message': 'Herd created successfully',
        'herd': herd.to_dict()
    }), 201

@bp.route('/<int:herd_id>', methods=['GET'])
@jwt_required()
def get_herd(herd_id):
    current_user_id = get_jwt_identity()

    # Get herd that belongs to user
    herd = Herd.query.filter_by(id=herd_id, user_id=int(current_user_id)).first()

    if not herd:
        return jsonify({'message': 'Herd not found'}), 404

    return jsonify({
        'herd': herd.to_dict(include_groups=True)
    }), 200

@bp.route('/<int:herd_id>', methods=['PUT'])
@jwt_required()
def update_herd(herd_id):
    current_user_id = get_jwt_identity()

    # Get herd that belongs to user
    herd = Herd.query.filter_by(id=herd_id, user_id=int(current_user_id)).first()

    if not herd:
        return jsonify({'message': 'Herd not found or you do not have permission to update it'}), 404

    data = request.get_json()

    # Parse date strings into datetime objects
    date_fields = ['entry_date', 'measure_date', 'birth_date', 'exit_date',
                  'calving_date', 'cure_date', 'abortion_date', 'insem_date', 'dry_date']

    for field in date_fields:
        if data.get(field):
            try:
                data[field] = datetime.fromisoformat(data[field].replace('Z', '+00:00'))
            except (ValueError, TypeError):
                # If date parsing fails, keep the existing value
                data[field] = getattr(herd, field)
        elif field in data and data[field] is None:
            # If the field is explicitly set to null, update it
            data[field] = None

    # Update basic fields
    if data.get('name'):
        herd.name = data['name']
    if data.get('description') is not None:
        herd.description = data['description']

    # Update extended fields
    extended_fields = [
        'ear_num', 'gender', 'birth_weight', 'breed', 'color',
        'father_ear_num', 'mother_ear_num', 'entry_date', 'entry_type',
        'group_name', 'lactation_number', 'month_age', 'grow_status',
        'fertility_status', 'high', 'bust', 'measure_date', 'birth_date',
        'is_on_farm', 'exit_date', 'exit_type', 'exit_reason',
        'calving_date', 'cure_date', 'abortion_date', 'insem_date',
        'curedate', 'pd1_date', 'pd2_date', 'disease_date',
        'health_status', 'disease_name', 'create_time', 'remark', 'dry_date'
    ]

    for field in extended_fields:
        if field in data:
            setattr(herd, field, data[field])

    db.session.commit()
    queue_animal_group_processing(herd.id)


    return jsonify({
        'message': 'Herd updated successfully',
        'herd': herd.to_dict()
    }), 200

@bp.route('/<int:herd_id>', methods=['DELETE'])
@jwt_required()
def delete_herd(herd_id):
    current_user_id = get_jwt_identity()

    # Get herd that belongs to user
    herd = Herd.query.filter_by(id=herd_id, user_id=int(current_user_id)).first()

    if not herd:
        return jsonify({'message': 'Herd not found or you do not have permission to delete it'}), 404

    db.session.delete(herd)
    db.session.commit()

    return jsonify({
        'message': 'Herd deleted successfully'
    }), 200

@bp.route('/groups', methods=['GET'])
@jwt_required()
def get_all_animal_groups():
    current_user_id = get_jwt_identity()

    # Get all animal groups belonging to the user
    groups = AnimalGroup.query.filter_by(user_id=int(current_user_id)).all()

    return jsonify({
        'animal_groups': [group.to_dict() for group in groups]
    }), 200

@bp.route('/groups/<int:group_id>', methods=['GET'])
@jwt_required()
def get_animal_group(group_id):
    current_user_id = get_jwt_identity()

    # Get the specific animal group
    group = AnimalGroup.query.filter_by(id=group_id, user_id=int(current_user_id)).first()

    if not group:
        return jsonify({'message': 'Animal group not found'}), 404

    # Get all herds associated with this group using the relationship
    herds = group.herds

    return jsonify({
        'animal_group': group.to_dict(),
        'herds': [herd.to_dict() for herd in herds]
    }), 200

@bp.route('/groups', methods=['POST'])
@jwt_required()
def create_animal_group():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    # Validate required fields
    if not data or not data.get('name'):
        return jsonify({'message': 'Name is required'}), 400

    # Check if group already exists for this user
    existing_group = AnimalGroup.query.filter_by(
        name=data['name'],
        user_id=int(current_user_id)
    ).first()

    if existing_group:
        return jsonify({'message': 'Animal group with this name already exists'}), 409

    # Create new animal group
    group = AnimalGroup(
        user_id=int(current_user_id),
        name=data['name'],
        description=data.get('description'),
        weight_kg=data.get('weight_kg'),
        bcs=data.get('bcs'),
        milk_production_kg=data.get('milk_production_kg'),
        days_in_milk=data.get('days_in_milk'),
        lactation_number=data.get('lactation_number')
    )

    db.session.add(group)
    db.session.commit()

    return jsonify({
        'message': 'Animal group created successfully',
        'animal_group': group.to_dict()
    }), 201

@bp.route('/groups/<int:group_id>', methods=['PUT'])
@jwt_required()
def update_animal_group(group_id):
    current_user_id = get_jwt_identity()

    # Get animal group
    group = AnimalGroup.query.filter_by(id=group_id, user_id=int(current_user_id)).first()

    if not group:
        return jsonify({'message': 'Animal group not found'}), 404

    data = request.get_json()
    old_name = group.name

    # Update group fields
    if data.get('name'):
        # Check if the new name would conflict with existing group
        if data['name'] != old_name:
            existing_group = AnimalGroup.query.filter_by(
                name=data['name'],
                user_id=int(current_user_id)
            ).first()
            if existing_group and existing_group.id != group_id:
                return jsonify({'message': 'Another animal group with this name already exists'}), 409
        group.name = data['name']

        # Update group_name in all herds that belong to this group
        if old_name != data['name']:
            for herd in group.herds:
                herd.group_name = data['name']

    if data.get('description') is not None:
        group.description = data['description']
    if data.get('weight_kg') is not None:
        group.weight_kg = data['weight_kg']
    if data.get('bcs') is not None:
        group.bcs = data['bcs']
    if data.get('milk_production_kg') is not None:
        group.milk_production_kg = data['milk_production_kg']
    if data.get('days_in_milk') is not None:
        group.days_in_milk = data['days_in_milk']
    if data.get('lactation_number') is not None:
        group.lactation_number = data['lactation_number']

    db.session.commit()

    return jsonify({
        'message': 'Animal group updated successfully',
        'animal_group': group.to_dict()
    }), 200

@bp.route('/groups/<int:group_id>', methods=['DELETE'])
@jwt_required()
def delete_animal_group(group_id):
    current_user_id = get_jwt_identity()

    # Get animal group
    group = AnimalGroup.query.filter_by(id=group_id, user_id=int(current_user_id)).first()

    if not group:
        return jsonify({'message': 'Animal group not found'}), 404

    # Set animal_group_id to NULL for all herds in this group
    for herd in group.herds:
        herd.animal_group_id = None
        # Keep the group_name for reference

    db.session.delete(group)
    db.session.commit()

    return jsonify({
        'message': 'Animal group deleted successfully'
    }), 200

# Route to assign herds to a group
@bp.route('/groups/<int:group_id>/herds', methods=['POST'])
@jwt_required()
def assign_herds_to_group(group_id):
    current_user_id = get_jwt_identity()
    data = request.get_json()

    # Validate required fields
    if not data or not data.get('herd_ids'):
        return jsonify({'message': 'Herd IDs are required'}), 400

    # Get the animal group
    group = AnimalGroup.query.filter_by(id=group_id, user_id=int(current_user_id)).first()
    if not group:
        return jsonify({'message': 'Animal group not found'}), 404

    # Update herds to assign to this group by setting their group_name and animal_group_id
    herd_ids = data['herd_ids']
    updated_count = 0

    for herd_id in herd_ids:
        herd = Herd.query.filter_by(id=herd_id, user_id=int(current_user_id)).first()
        if herd:
            herd.group_name = group.name
            herd.animal_group_id = group.id
            updated_count += 1

    db.session.commit()

    return jsonify({
        'message': f'Successfully assigned {updated_count} herds to the group',
        'updated_count': updated_count
    }), 200

# Route to get all herds in a group
@bp.route('/groups/<int:group_id>/herds', methods=['GET'])
@jwt_required()
def get_group_herds(group_id):
    current_user_id = get_jwt_identity()

    # Get the animal group
    group = AnimalGroup.query.filter_by(id=group_id, user_id=int(current_user_id)).first()
    if not group:
        return jsonify({'message': 'Animal group not found'}), 404

    # Get herds using the relationship
    herds = group.herds

    return jsonify({
        'group': group.to_dict(),
        'herds': [herd.to_dict() for herd in herds],
        'herd_count': len(herds)
    }), 200

# Route to get NRC 8th edition requirements for an animal group
@bp.route('/groups/<int:group_id>/nrc-requirements', methods=['GET'])
@jwt_required()
def get_group_nrc_requirements(group_id):
    current_user_id = get_jwt_identity()

    # Get the animal group
    group = AnimalGroup.query.filter_by(id=group_id, user_id=int(current_user_id)).first()
    if not group:
        return jsonify({'message': 'Animal group not found'}), 404

    # Check if we have enough data to calculate NRC requirements
    if group.weight_kg is None:
        group.weight_kg=680 #assume standerd weight

    # Check if a specific model version was requested
    model_version_id = request.args.get('model_version_id', type=int)

    # Get NRC 8th edition requirements
    nrc_data = get_nrc8_requirements(group_id, model_version_id)

    return jsonify({
        'group': group.to_dict(),
        'nrc_requirements': nrc_data
    }), 200