from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, verify_jwt_in_request
from functools import wraps
from werkzeug.exceptions import NotFound

from ..services.kb_service import KnowledgeBaseService
from ..models.kb_models import KBArticle, KBArticleChunk
from ..extensions import db
import logging

bp = Blueprint('kb', __name__, url_prefix='/api/kb')
logger = logging.getLogger(__name__)

# Custom decorator to make JWT optional
def jwt_optional():
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            try:
                verify_jwt_in_request(optional=True)
            except:
                # Invalid token, but we'll proceed anyway
                pass
            return fn(*args, **kwargs)
        return decorator
    return wrapper

# ---- Category Routes ----

@bp.route('/categories', methods=['GET'])
def get_categories():
    try:
        include_count = request.args.get('include_count', 'true').lower() == 'true'
        categories = KnowledgeBaseService.get_categories(include_article_count=include_count)
        return jsonify({'categories': categories}), 200
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@bp.route('/categories/<string:slug>', methods=['GET'])
def get_category(slug):
    try:
        category = KnowledgeBaseService.get_category_by_slug(slug)
        if not category:
            return jsonify({'message': 'Category not found'}), 404
        return jsonify({'category': category}), 200
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@bp.route('/categories', methods=['POST'])
@jwt_required()
def create_category():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    if not data:
        return jsonify({'message': 'No data provided'}), 400

    try:
        category = KnowledgeBaseService.create_category(data)
        return jsonify({'message': 'Category created successfully', 'category': category}), 201
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@bp.route('/categories/<int:category_id>', methods=['PUT'])
@jwt_required()
def update_category(category_id):
    current_user_id = get_jwt_identity()
    data = request.get_json()

    if not data:
        return jsonify({'message': 'No data provided'}), 400

    try:
        category = KnowledgeBaseService.update_category(category_id, data)
        return jsonify({'message': 'Category updated successfully', 'category': category}), 200
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@bp.route('/categories/<int:category_id>', methods=['DELETE'])
@jwt_required()
def delete_category(category_id):
    current_user_id = get_jwt_identity()

    try:
        KnowledgeBaseService.delete_category(category_id)
        return jsonify({'message': 'Category deleted successfully'}), 200
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': str(e)}), 500

# ---- Article Routes ----

@bp.route('/articles', methods=['GET'])
def get_articles():
    # Pagination parameters
    page = int(request.args.get('page', 1))
    limit = min(int(request.args.get('limit', 20)), 50)  # Cap at 50
    offset = (page - 1) * limit

    # Sorting parameters
    sort_by = request.args.get('sort_by', 'created_at')
    sort_order = request.args.get('sort_order', 'desc')

    # Filter parameters
    filters = {}
    if 'category_id' in request.args:
        filters['category_id'] = int(request.args.get('category_id'))
    if 'category_slug' in request.args:
        category = KnowledgeBaseService.get_category_by_slug(request.args.get('category_slug'))
        if category:
            filters['category_id'] = category['id']
    if 'tag' in request.args:
        filters['tag'] = request.args.get('tag')
    if 'search' in request.args:
        filters['search'] = request.args.get('search')
    if 'published' in request.args:
        filters['published'] = request.args.get('published').lower() == 'true'

    try:
        # Check for JWT to include unpublished articles for admin/author
        try:
            verify_jwt_in_request(optional=True)
            current_user_id = get_jwt_identity()
            if current_user_id:
                filters['user_id'] = current_user_id
        except:
            # No valid token, only show published
            filters['published'] = True

        articles = KnowledgeBaseService.get_articles(
            filters=filters,
            limit=limit,
            offset=offset,
            sort_by=sort_by,
            sort_order=sort_order
        )

        return jsonify(articles), 200
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@bp.route('/articles/<string:slug>', methods=['GET'])
@jwt_optional()
def get_article(slug):
    # Default to reconstructing content unless client specifies otherwise
    include_content = request.args.get('include_content', 'true').lower() == 'true'
    # Increment view count by default
    increment_view = request.args.get('increment_view', 'true').lower() == 'true'

    try:
        # Fetch basic article data (includes view increment if requested)
        # This returns the SQLAlchemy object
        article_obj = KnowledgeBaseService.get_article_by_slug(slug, increment_view=increment_view)

        if not article_obj:
            raise NotFound('Article not found')

        # Check published status if needed (same logic as before)
        if not article_obj.published:
             current_user_id = get_jwt_identity()
             if not current_user_id or str(current_user_id) != str(article_obj.user_id):
                 raise NotFound('Article not found or not published')

        # Get basic article dictionary (no content)
        article_dict = article_obj.to_dict()

        # Get content if requested using our general function
        if include_content:
            logger.debug(f"Getting content for article ID: {article_obj.id}")
            # Call the general content retrieval function from the service
            full_content = KnowledgeBaseService.get_article_content(article_obj.id)

            if full_content is None:
                # Log error if content retrieval failed but article exists
                logger.error(f"Failed to get content for article {article_obj.id} (slug: {slug})")
            else:
                article_dict['content'] = full_content

        return jsonify({'article': article_dict}), 200

    except NotFound as e:
         # Log not found errors specifically
         logger.info(f"Article not found for slug '{slug}': {e}")
         return jsonify({'message': str(e)}), 404
    except Exception as e:
        # Log other specific errors
        logger.error(f"Error in get_article route for slug '{slug}': {e}", exc_info=True)
        # Ensure session is rolled back in case of general error during processing
        db.session.rollback()
        return jsonify({'message': "An internal error occurred."}), 500
    finally:
         pass


@bp.route('/articles', methods=['POST'])
@jwt_required()
def create_article():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    if not data:
        return jsonify({'message': 'No data provided'}), 400

    try:
        article = KnowledgeBaseService.create_article(data, current_user_id)
        return jsonify({'message': 'Article created successfully', 'article': article}), 201
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@bp.route('/articles/<int:article_id>', methods=['PUT'])
@jwt_required()
def update_article(article_id):
    current_user_id = get_jwt_identity()
    data = request.get_json()

    if not data:
        return jsonify({'message': 'No data provided'}), 400

    try:
        article = KnowledgeBaseService.update_article(article_id, data, current_user_id)
        return jsonify({'message': 'Article updated successfully', 'article': article}), 200
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@bp.route('/articles/<int:article_id>', methods=['DELETE'])
@jwt_required()
def delete_article(article_id):
    current_user_id = get_jwt_identity()

    try:
        KnowledgeBaseService.delete_article(article_id, current_user_id)
        return jsonify({'message': 'Article deleted successfully'}), 200
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@bp.route('/articles/<int:article_id>/feedback', methods=['POST'])
@jwt_optional()
def record_feedback(article_id):
    data = request.get_json()

    if not data:
        return jsonify({'message': 'No data provided'}), 400

    helpful = data.get('helpful')
    comment = data.get('comment')

    # Get user ID if authenticated
    current_user_id = get_jwt_identity()

    try:
        feedback = KnowledgeBaseService.record_feedback(
            article_id, current_user_id, helpful, comment
        )
        return jsonify({'message': 'Feedback recorded successfully', 'feedback': feedback}), 200
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@bp.route('/articles/<int:article_id>/related', methods=['GET'])
def get_related_articles(article_id):
    limit = min(int(request.args.get('limit', 5)), 10)  # Cap at 10

    try:
        related = KnowledgeBaseService.get_related_articles(article_id, limit=limit)
        return jsonify({'related_articles': related}), 200
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': str(e)}), 500

# ---- Search and AI Routes ----

@bp.route('/search', methods=['GET'])
def search_articles():
    query = request.args.get('q', '')
    limit = min(int(request.args.get('limit', 10)), 50)  # Cap at 50
    search_mode = request.args.get('mode', 'semantic')  # Get search mode parameter

    if not query:
        return jsonify({'message': 'Search query is required'}), 400

    try:
        # Use the appropriate search method based on mode
        if search_mode == 'semantic':
            results = KnowledgeBaseService.semantic_search(query, limit=limit)
        else:
            # Fallback to keyword search
            results = KnowledgeBaseService.keyword_search(query, limit=limit)

        return jsonify({'results': results}), 200
    except Exception as e:
        return jsonify({'message': f"Error during search: {str(e)}"}), 500

@bp.route('/ask', methods=['POST'])
def answer_question():
    data = request.get_json()

    if not data or 'question' not in data:
        return jsonify({'message': 'Question is required'}), 400

    question = data['question']
    context_window = min(int(data.get('context_window', 3)), 5)  # Cap at 5
    locale = data.get('locale', 'en')  # Default to English if not specified

    try:
        answer_data = KnowledgeBaseService.answer_question(
            question,
            context_window=context_window,
            locale=locale
        )
        return jsonify(answer_data), 200
    except Exception as e:
        return jsonify({'message': str(e)}), 500

# ---- Tag Routes ----

@bp.route('/tags', methods=['GET'])
def get_popular_tags():
    limit = min(int(request.args.get('limit', 10)), 50)  # Cap at 50

    try:
        tags = KnowledgeBaseService.get_popular_tags(limit=limit)
        return jsonify({'tags': tags}), 200
    except Exception as e:
        return jsonify({'message': str(e)}), 500

# ---- Stats Routes ----

@bp.route('/stats', methods=['GET'])
def get_kb_stats():
    try:
        stats = KnowledgeBaseService.get_article_stats()
        return jsonify({'stats': stats}), 200
    except Exception as e:
        return jsonify({'message': str(e)}), 500

# ---- Admin Routes ----

@bp.route('/admin/embeddings/status', methods=['GET'])
@jwt_required()
def get_embedding_status():
    """Get the status of embeddings for all articles."""
    try:
        # Initialize status counters
        status = {
            'pending': 0,
            'processing': 0,
            'completed': 0,
            'failed': 0,
            'no_chunks': 0,
            'error': 0
        }

        # Get all articles
        articles = KBArticle.query.all()

        # Calculate embedding status for each article
        for article in articles:
            # Get counts for each status for this article's chunks
            status_counts = dict(db.session.query(
                KBArticleChunk.embedding_status,
                db.func.count(KBArticleChunk.id)
            ).filter(KBArticleChunk.article_id == article.id)
             .group_by(KBArticleChunk.embedding_status).all())

            total_chunks = sum(status_counts.values())

            # Determine overall status using same logic as in KBArticle.to_dict()
            if total_chunks == 0:
                status["no_chunks"] += 1
            elif status_counts.get('failed', 0) > 0:
                status["failed"] += 1
            elif status_counts.get('processing', 0) > 0:
                status["processing"] += 1
            elif status_counts.get('pending', 0) > 0:
                status["pending"] += 1
            elif status_counts.get('completed', 0) == total_chunks:
                status["completed"] += 1
            else:
                status["error"] += 1

        # Add total
        status['total'] = sum(status.values())

        return jsonify({'embedding_status': status}), 200
    except Exception as e:
        logger.error(f"Error getting embedding status: {str(e)}", exc_info=True)
        return jsonify({'message': f"Error getting embedding status: {str(e)}"}), 500

@bp.route('/admin/embeddings/reprocess', methods=['POST'])
@jwt_required()
def reprocess_embeddings():
    """Reprocess articles with failed embeddings."""
    try:
        limit = int(request.args.get('limit', 50))
        count = KnowledgeBaseService.reprocess_failed_embeddings(limit=limit)

        return jsonify({
            'message': f"Queued {count} articles for reprocessing",
            'count': count
        }), 200
    except Exception as e:
        return jsonify({'message': f"Error reprocessing embeddings: {str(e)}"}), 500

@bp.route('/admin/embeddings/reprocess-completed', methods=['POST'])
@jwt_required()
def reprocess_completed_embeddings():
    """Reprocess articles with completed embeddings (for embedding service changes)."""
    try:
        limit = request.args.get('limit')
        article_id = request.args.get('article_id')
        process_all = request.args.get('process_all', 'false').lower() == 'true'

        # Convert limit to int if provided
        if limit is not None:
            limit = int(limit)
        elif not process_all:
            # Default limit if not processing all
            limit = 50

        # Convert article_id to int if provided
        if article_id:
            article_id = int(article_id)

        # Call the service method with the parameters
        count = KnowledgeBaseService.reprocess_completed_embeddings(
            limit=limit,
            article_id=article_id,
            process_all=process_all
        )

        return jsonify({
            'message': f"Queued {count} chunks for re-embedding",
            'count': count
        }), 200
    except Exception as e:
        return jsonify({'message': f"Error reprocessing completed embeddings: {str(e)}"}), 500

@bp.route('/admin/embeddings/<int:article_id>/regenerate', methods=['POST'])
@jwt_required()
def regenerate_embedding(article_id):
    """Regenerate embedding for a specific article."""
    try:
        # Find the article
        article = KBArticle.query.get(article_id)
        if not article:
            return jsonify({'message': 'Article not found'}), 404

        # Reset status and queue for processing
        article.embedding_status = 'pending'
        db.session.commit()

        # Queue embedding generation
        from ..services.tasks import queue_embedding_generation
        success = queue_embedding_generation(article.id)

        if success:
            return jsonify({'message': f"Embedding generation queued for article {article_id}"}), 200
        else:
            return jsonify({'message': f"Failed to queue embedding generation for article {article_id}"}), 500
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f"Error regenerating embedding: {str(e)}"}), 500