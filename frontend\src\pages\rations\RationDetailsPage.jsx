import { useState } from 'react'
import { useParams, useNavigate, Link } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import { getRationById, deleteRation } from '../../services/rationService'
import { formatNumber } from '../../utils/formatters'

const RationDetailsPage = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const { data: ration, isLoading, error } = useQuery(['ration', id], () => getRationById(id))

  const deleteMutation = useMutation(() => deleteRation(id), {
    onSuccess: () => {
      queryClient.invalidateQueries('rations')
      navigate('/rations')
    }
  })

  const handleDelete = () => {
    deleteMutation.mutate()
  }

  if (isLoading) {
    return <p className="text-center py-4">{t('common.loading')}</p>
  }

  if (error) {
    return (
      <Card>
        <p className="text-red-500">{t('rations.errorLoading')}: {error.message}</p>
        <div className="mt-4">
          <Link to="/rations">
            <Button variant="outline">{t('rations.backToRations')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  if (!ration) {
    return (
      <Card>
        <p className="text-gray-500">{t('rations.rationNotFound')}</p>
        <div className="mt-4">
          <Link to="/rations">
            <Button variant="outline">{t('rations.backToRations')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-gray-900">{ration.name}</h1>
            {ration.is_formulated ? (
              <span className="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {t('rations.formulated')}
              </span>
            ) : (
              <span className="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                {t('rations.notFormulated')}
              </span>
            )}
          </div>
          {ration.last_formulated_at && (
            <p className="text-sm text-gray-500">
              {t('rations.lastFormulated')}: {new Date(ration.last_formulated_at).toLocaleString()}
            </p>
          )}
        </div>
        <div className="flex space-x-2">
          <Link to={`/rations/${id}/formulate`}>
            <Button>
              {ration.is_formulated
                ? t('rations.reformulate')
                : t('rations.formulate')}
            </Button>
          </Link>
          <Link to={`/rations/${id}/edit`}>
            <Button variant="outline">{t('common.edit')}</Button>
          </Link>
          <Button variant="danger" onClick={() => setShowDeleteConfirm(true)}>{t('common.delete')}</Button>
        </div>
      </div>

      {ration.description && (
        <Card className="mb-6">
          <p>{ration.description}</p>
        </Card>
      )}

      {/* Total Cost Summary */}
      {ration.feeds && ration.feeds.some(feed => feed.cost_contribution !== null) && (
        <Card className="mb-6 bg-green-50 border-green-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">{t('rations.totalCost')}</h3>
            <div className="text-xl font-bold text-green-700">
              {t('common.currencySymbol')}
              {formatNumber(
                ration.feeds.reduce((total, feed) => total + (feed.cost_contribution || 0), 0)
              )}
              <span className="text-sm font-normal text-gray-600 ml-2">{t('rations.perDayPerAnimal')}</span>
            </div>
          </div>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card title={t('rations.feedIngredients')}>
          {ration.feeds && ration.feeds.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {ration.feeds.map(feed => (
                <div key={feed.id} className="py-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{feed.feed_name}</h4>
                      <div className="flex space-x-4 mt-1">
                        {feed.min_inclusion_percentage !== null && (
                          <span className="text-xs text-gray-500">
                            {t('rations.min')}: {formatNumber(feed.min_inclusion_percentage)}%
                          </span>
                        )}
                        {feed.max_inclusion_percentage !== null && (
                          <span className="text-xs text-gray-500">
                            {t('rations.max')}: {formatNumber(feed.max_inclusion_percentage)}%
                          </span>
                        )}
                      </div>
                    </div>
                    {feed.actual_inclusion_percentage !== null && (
                      <div className="text-right">
                        <span className="text-lg font-medium text-green-600">
                          {formatNumber(feed.actual_inclusion_percentage)}%
                        </span>
                        {feed.cost_contribution !== null && (
                          <p className="text-xs text-gray-500">
                            {t('common.currencySymbol')}{formatNumber(feed.cost_contribution)}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-center py-4 text-gray-500">{t('rations.noFeedsAdded')}</p>
          )}
        </Card>

        <Card title={t('rations.nutritionalConstraints')}>
          {ration.constraints && ration.constraints.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {ration.constraints.map(constraint => (
                <div key={constraint.id} className="py-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{constraint.nutrient_name}</h4>
                      <div className="flex space-x-4 mt-1">
                        {constraint.min_value !== null && (
                          <span className="text-xs text-gray-500">
                            {t('rations.min')}: {formatNumber(constraint.min_value)}
                          </span>
                        )}
                        {constraint.max_value !== null && (
                          <span className="text-xs text-gray-500">
                            {t('rations.max')}: {formatNumber(constraint.max_value)}
                          </span>
                        )}
                      </div>
                    </div>
                    {constraint.actual_value !== null && (
                      <div className="text-right">
                        <span className={`text-lg font-medium ${
                          (constraint.min_value !== null && constraint.actual_value < constraint.min_value) ||
                          (constraint.max_value !== null && constraint.actual_value > constraint.max_value)
                            ? 'text-red-600'
                            : 'text-green-600'
                        }`}>
                          {formatNumber(constraint.actual_value)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-center py-4 text-gray-500">{t('rations.noConstraintsDefined')}</p>
          )}
        </Card>
      </div>

      <div className="mt-6">
        <Link to="/rations">
          <Button variant="outline">{t('rations.backToRations')}</Button>
        </Link>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">{t('rations.confirmDeletion')}</h3>
            <p className="text-gray-500 mb-4">
              {t('rations.deleteConfirmationMessage', { name: ration.name })}
            </p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
                {t('common.cancel')}
              </Button>
              <Button variant="danger" onClick={handleDelete}>
                {t('common.delete')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default RationDetailsPage