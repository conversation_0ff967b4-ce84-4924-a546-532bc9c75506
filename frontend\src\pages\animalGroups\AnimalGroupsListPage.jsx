import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Input from '../../components/ui/Input'
import { getAnimalGroups } from '../../services/animalGroupService'

const AnimalGroupsListPage = () => {
  const { t } = useTranslation()
  const [searchTerm, setSearchTerm] = useState('')

  const { data: animalGroups = [], isLoading, error, refetch } = useQuery(
    'animal-groups',
    getAnimalGroups
  )

  // Filter groups based on search term
  const filteredGroups = animalGroups.filter(group => 
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('animalGroups.title')}</h1>
        <Link to="/animal-groups/new">
          <Button>{t('animalGroups.addNew')}</Button>
        </Link>
      </div>

      <div className="mb-6">
        <Input
          placeholder={t('animalGroups.searchGroups')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full md:w-64"
        />
      </div>

      {isLoading ? (
        <p className="text-center py-4">{t('common.loading')}</p>
      ) : error ? (
        <Card>
          <p className="text-red-500">{t('animalGroups.errorLoading')}: {error.message}</p>
          <Button onClick={() => refetch()} className="mt-2" variant="outline">{t('common.tryAgain')}</Button>
        </Card>
      ) : filteredGroups.length === 0 ? (
        <Card>
          {searchTerm ? (
            <div className="text-center py-4">
              <p className="text-gray-500 mb-4">{t('animalGroups.noGroupsFoundForSearch')}</p>
              <Button variant="outline" onClick={() => setSearchTerm('')}>{t('common.clearSearch')}</Button>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500 mb-4">{t('animalGroups.noGroupsFound')}</p>
              <Link to="/animal-groups/new">
                <Button>{t('animalGroups.createFirst')}</Button>
              </Link>
            </div>
          )}
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredGroups.map(group => (
            <Link to={`/animal-groups/${group.id}`} key={group.id} className="block">
              <Card className="h-full hover:shadow-md transition-shadow duration-200">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">{group.name}</h3>
                  {group.description && (
                    <p className="text-gray-600 mb-4 line-clamp-2">{group.description}</p>
                  )}
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2 mt-4">
                    <div>
                      <p className="text-xs text-gray-500">{t('animalGroups.animals')}</p>
                      <p className="text-sm font-medium">{group.herd_count || 0}</p>
                    </div>
                    {group.lactation_number !== null && (
                      <div>
                        <p className="text-xs text-gray-500">{t('animalGroups.avgLactation')}</p>
                        <p className="text-sm font-medium">{group.lactation_number.toFixed(1)}</p>
                      </div>
                    )}
                    {group.milk_production_kg !== null && (
                      <div>
                        <p className="text-xs text-gray-500">{t('animalGroups.milkProduction')}</p>
                        <p className="text-sm font-medium">{group.milk_production_kg.toFixed(1)} kg</p>
                      </div>
                    )}
                    {group.days_in_milk !== null && (
                      <div>
                        <p className="text-xs text-gray-500">{t('animalGroups.daysInMilk')}</p>
                        <p className="text-sm font-medium">{group.days_in_milk}</p>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </div>
  )
}

export default AnimalGroupsListPage