# my_project/backend/app/models/staged_import.py

from datetime import datetime
from ..extensions import db
from sqlalchemy.dialects.postgresql import JSONB # Use JSONB for efficient JSON storage in PostgreSQL

class StagedImportSession(db.Model):
    __tablename__ = 'staged_import_sessions'
    # Ensure this table uses the same schema as your other app tables if necessary
    __table_args__ = {'schema': 'ration_app'}

    session_id = db.Column(db.String(36), primary_key=True) # UUID as string
    user_id = db.Column(db.Integer, db.ForeignKey('ration_app.users.id'), nullable=False, index=True)
    status = db.Column(db.String(50), nullable=False, default='pending_review', index=True) # e.g., pending_review, committed, discarded, error
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Store the plan and the full payload as JSON
    # Using JSONB is generally recommended in PostgreSQL for performance and querying capabilities
    import_plan_json = db.Column(JSONB, nullable=False)
    excel_payload_json = db.Column(JSONB, nullable=False) # Storing full payload might be large! Consider alternatives if needed.

    # Optional: Add relationships if needed, e.g., back to User
    user = db.relationship('User')

    def __repr__(self):
        return f'<StagedImportSession {self.session_id} [{self.status}]>'

    # Optional: Helper methods to get plan/payload as dicts
    def get_plan(self):
        # The JSONB type often returns dicts directly with SQLAlchemy
        return self.import_plan_json if isinstance(self.import_plan_json, dict) else {}

    def get_payload(self):
        return self.excel_payload_json if isinstance(self.excel_payload_json, dict) else {}