from datetime import datetime
from sqlalchemy.dialects.postgresql import JSONB, ARRAY
from ..extensions import db
from sqlalchemy import text
from pgvector.sqlalchemy import Vector
from sqlalchemy import func

class KBCategory(db.Model):
    __tablename__ = 'kb_categories'
    __table_args__ = {'schema': 'ration_app'}

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    slug = db.Column(db.String(255), nullable=False, unique=True)
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('ration_app.kb_categories.id'), nullable=True)
    order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    articles = db.relationship('KBArticle', backref='category', lazy=True)
    subcategories = db.relationship('KBCategory',
                                    backref=db.backref('parent', remote_side=[id]),
                                    lazy=True)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'slug': self.slug,
            'description': self.description,
            'parent_id': self.parent_id,
            'order': self.order,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'article_count': len(self.articles) if self.articles else 0
        }


class KBArticle(db.Model):
    __tablename__ = 'kb_articles'
    __table_args__ = {'schema': 'ration_app'}

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    slug = db.Column(db.String(255), nullable=False, unique=True)
    summary = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('ration_app.kb_categories.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('ration_app.users.id'), nullable=False)
    published = db.Column(db.Boolean, default=True)
    view_count = db.Column(db.Integer, default=0)
    helpful_count = db.Column(db.Integer, default=0)
    not_helpful_count = db.Column(db.Integer, default=0)
    article_metadata = db.Column(JSONB, nullable=True)   # Any additional metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    tags = db.relationship('KBTag', secondary='ration_app.kb_article_tags', backref='articles')
    feedback = db.relationship('KBFeedback', backref='article', lazy=True)
    chunks = db.relationship(
        'KBArticleChunk',
        backref='article', # Allows chunk.article access
        lazy='dynamic',
        cascade='all, delete-orphan',
        order_by='KBArticleChunk.chunk_order'
    )

    def to_dict(self, include_embedding_status=True):
        result = {
            'id': self.id,
            'title': self.title,
            'slug': self.slug,
            'summary': self.summary,
            'category_id': self.category_id,
            'category_name': self.category.name if self.category else None,
            'user_id': self.user_id,
            'published': self.published,
            'view_count': self.view_count,
            'helpful_count': self.helpful_count,
            'not_helpful_count': self.not_helpful_count,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'tags': [tag.name for tag in self.tags] if self.tags else []
        }

        if include_embedding_status:
            overall_status = 'n/a' # Default if no chunks
            try:
                # Query chunk statuses efficiently - avoids loading full chunk objects
                # Use the relationship directly if the session is active,
                # otherwise, a query might be needed if called outside a request context.
                # Assuming session is typically active when to_dict is called from a route:

                # Get counts for each status and total count
                status_counts = dict(db.session.query(
                        KBArticleChunk.embedding_status,
                        func.count(KBArticleChunk.id)
                    ).filter(KBArticleChunk.article_id == self.id)
                     .group_by(KBArticleChunk.embedding_status).all()
                )
                total_chunks = sum(status_counts.values())

                if total_chunks == 0:
                    overall_status = 'no_chunks' # Or 'completed', or 'n/a'
                elif status_counts.get('failed', 0) > 0:
                    overall_status = 'failed'
                elif status_counts.get('processing', 0) > 0:
                    overall_status = 'processing'
                elif status_counts.get('pending', 0) > 0:
                    overall_status = 'pending'
                elif status_counts.get('completed', 0) == total_chunks:
                    overall_status = 'completed'
                else:
                    overall_status = 'inconsistent' # Should ideally not happen with above logic

                result['embedding_status'] = overall_status
                # Optional: Include detailed counts
                # result['chunk_status_counts'] = status_counts
                # result['chunk_count'] = total_chunks

            except Exception as e:
                 result['embedding_status'] = 'error'

        return result

    def has_embedding(self):
        """Check if article has a valid embedding."""
        return self.embedding_status == 'completed' and self.embedding_vector is not None

class KBArticleChunk(db.Model):
    __tablename__ = 'kb_article_chunks'
    __table_args__ = {'schema': 'ration_app'}

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('ration_app.kb_articles.id', ondelete='CASCADE'), nullable=False, index=True)
    chunk_text = db.Column(db.Text, nullable=False) # Stores the clean chunk text
    chunk_order = db.Column(db.Integer, nullable=False) # Sequence number

    embedding_vector = db.Column(Vector(768), nullable=True) # Index this column
    embedding_status = db.Column(db.String(20), default='pending', index=True) # pending, processing, completed, failed

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'article_id': self.article_id,
            'chunk_order': self.chunk_order,
            'embedding_status': self.embedding_status,
            'text_preview': self.chunk_text[:100] + '...' if self.chunk_text else '',
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class KBTag(db.Model):
    __tablename__ = 'kb_tags'
    __table_args__ = {'schema': 'ration_app'}

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'article_count': len(self.articles) if self.articles else 0
        }


# Association table for article-tag many-to-many relationship
kb_article_tags = db.Table('kb_article_tags',
    db.Column('article_id', db.Integer, db.ForeignKey('ration_app.kb_articles.id'), primary_key=True),
    db.Column('tag_id', db.Integer, db.ForeignKey('ration_app.kb_tags.id'), primary_key=True),
    schema='ration_app'
)


class KBFeedback(db.Model):
    __tablename__ = 'kb_feedback'
    __table_args__ = {'schema': 'ration_app'}

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('ration_app.kb_articles.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('ration_app.users.id'), nullable=True)  # Allow anonymous feedback
    helpful = db.Column(db.Boolean, nullable=True)  # True=helpful, False=not helpful, None=no rating
    comment = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'article_id': self.article_id,
            'user_id': self.user_id,
            'helpful': self.helpful,
            'comment': self.comment,
            'created_at': self.created_at.isoformat()
        }