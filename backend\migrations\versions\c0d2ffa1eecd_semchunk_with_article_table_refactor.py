"""semchunk with article table refactor

Revision ID: c0d2ffa1eecd
Revises: 0f834ad8ca3a
Create Date: 2025-04-16 15:36:07.236305

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import pgvector
# revision identifiers, used by Alembic.
revision = 'c0d2ffa1eecd'
down_revision = '0f834ad8ca3a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('kb_article_chunks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('article_id', sa.Integer(), nullable=False),
    sa.Column('chunk_text', sa.Text(), nullable=False),
    sa.Column('chunk_order', sa.Integer(), nullable=False),
    sa.Column('embedding_vector', pgvector.sqlalchemy.Vector(dim=1024), nullable=True),
    sa.Column('embedding_status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['article_id'], ['ration_app.kb_articles.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    schema='ration_app'
    )
    with op.batch_alter_table('kb_article_chunks', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_ration_app_kb_article_chunks_article_id'), ['article_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_ration_app_kb_article_chunks_embedding_status'), ['embedding_status'], unique=False)
        batch_op.create_index(batch_op.f('ix_ration_app_kb_article_chunks_embedding_vector'), ['embedding_vector'], unique=False)

    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint('animal_groups_herd_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'herds', ['herd_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint('constraints_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint('feed_nutrients_nutrient_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('feed_nutrients_feed_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'nutrients', ['nutrient_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app', ondelete='CASCADE')

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint('feeds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint('herds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_article_tags', schema=None) as batch_op:
        batch_op.drop_constraint('kb_article_tags_article_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('kb_article_tags_tag_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'kb_tags', ['tag_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'kb_articles', ['article_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_articles', schema=None) as batch_op:
        batch_op.drop_constraint('kb_articles_category_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('kb_articles_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'kb_categories', ['category_id'], ['id'], referent_schema='ration_app')
        batch_op.drop_column('embedding_status')
        batch_op.drop_column('content')
        batch_op.drop_column('embedding_json')
        batch_op.drop_column('embedding_vector')

    with op.batch_alter_table('kb_categories', schema=None) as batch_op:
        batch_op.drop_constraint('kb_categories_parent_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'kb_categories', ['parent_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_feedback', schema=None) as batch_op:
        batch_op.drop_constraint('kb_feedback_user_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('kb_feedback_article_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'kb_articles', ['article_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint('ration_feeds_ration_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('ration_feeds_feed_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint('rations_animal_group_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('rations_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'animal_groups', ['animal_group_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint('staged_import_sessions_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('staged_import_sessions_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('rations_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('rations_animal_group_id_fkey', 'animal_groups', ['animal_group_id'], ['id'])

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('ration_feeds_feed_id_fkey', 'feeds', ['feed_id'], ['id'])
        batch_op.create_foreign_key('ration_feeds_ration_id_fkey', 'rations', ['ration_id'], ['id'])

    with op.batch_alter_table('kb_feedback', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_feedback_article_id_fkey', 'kb_articles', ['article_id'], ['id'])
        batch_op.create_foreign_key('kb_feedback_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('kb_categories', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_categories_parent_id_fkey', 'kb_categories', ['parent_id'], ['id'])

    with op.batch_alter_table('kb_articles', schema=None) as batch_op:
        batch_op.add_column(sa.Column('embedding_vector', pgvector.sqlalchemy.vector.VECTOR(dim=1536), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('embedding_json', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False))
        batch_op.add_column(sa.Column('embedding_status', sa.VARCHAR(length=20), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_articles_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('kb_articles_category_id_fkey', 'kb_categories', ['category_id'], ['id'])

    with op.batch_alter_table('kb_article_tags', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_article_tags_tag_id_fkey', 'kb_tags', ['tag_id'], ['id'])
        batch_op.create_foreign_key('kb_article_tags_article_id_fkey', 'kb_articles', ['article_id'], ['id'])

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('herds_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feeds_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feed_nutrients_feed_id_fkey', 'feeds', ['feed_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('feed_nutrients_nutrient_id_fkey', 'nutrients', ['nutrient_id'], ['id'])

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('constraints_ration_id_fkey', 'rations', ['ration_id'], ['id'])

    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('animal_groups_herd_id_fkey', 'herds', ['herd_id'], ['id'])

    with op.batch_alter_table('kb_article_chunks', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_ration_app_kb_article_chunks_embedding_vector'))
        batch_op.drop_index(batch_op.f('ix_ration_app_kb_article_chunks_embedding_status'))
        batch_op.drop_index(batch_op.f('ix_ration_app_kb_article_chunks_article_id'))

    op.drop_table('kb_article_chunks', schema='ration_app')
    # ### end Alembic commands ###
