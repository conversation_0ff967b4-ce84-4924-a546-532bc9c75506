import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { getCategories, createCategory, updateCategory, deleteCategory } from '../../services/kbService';
import Card from '../../components/ui/Card';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';

export default function CategoriesTab() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({ name: '', slug: '', description: '', parent_id: '' });
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [errors, setErrors] = useState({});

  const { data: categories = [], isLoading } = useQuery('kb-admin-categories', () => getCategories(true));

  const createMut = useMutation(createCategory, {
    onSuccess: () => { queryClient.invalidateQueries('kb-admin-categories'); queryClient.invalidateQueries('kb-stats'); reset(); }
  });
  const updateMut = useMutation(({ id, data }) => updateCategory(id, data), {
    onSuccess: () => { queryClient.invalidateQueries('kb-admin-categories'); reset(); }
  });
  const deleteMut = useMutation(id => deleteCategory(id), {
    onSuccess: () => { queryClient.invalidateQueries('kb-admin-categories'); queryClient.invalidateQueries('kb-stats'); }
  });

  function reset() {
    setFormData({ name: '', slug: '', description: '', parent_id: '' });
    setIsEditing(false);
    setEditingId(null);
    setErrors({});
  }

  function handleChange(e) {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: name === 'parent_id' && value === '' ? '' : value }));
    if (name === 'name' && !isEditing) {
      const slug = value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
      setFormData(prev => ({ ...prev, slug }));
    }
    setErrors(prev => ({ ...prev, [name]: '' }));
  }

  function handleEdit(cat) {
    setIsEditing(true);
    setEditingId(cat.id);
    setFormData({ name: cat.name, slug: cat.slug, description: cat.description || '', parent_id: cat.parent_id || '' });
  }

  function handleSubmit(e) {
    e.preventDefault();
    const errs = {};
    if (!formData.name) errs.name = t('validation.nameRequired');
    if (!formData.slug) errs.slug = t('validation.slugRequired');
    if (Object.keys(errs).length) { setErrors(errs); return; }
    if (isEditing) updateMut.mutate({ id: editingId, data: formData });
    else createMut.mutate(formData);
  }

  function handleDelete(id) {
    if (window.confirm(t('kb.confirmDeleteCategory'))) deleteMut.mutate(id);
  }

  if (isLoading) return (
    <div className="flex justify-center items-center py-16">
      <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
    </div>
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card
        title={isEditing ? t('kb.editCategory') : t('kb.addCategory')}
        className="bg-white shadow-md hover:shadow-lg transition-shadow"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input id="name" name="name" label={t('common.name')} value={formData.name} onChange={handleChange} error={errors.name} required />
          <Input id="slug" name="slug" label={t('kb.slug')} value={formData.slug} onChange={handleChange} error={errors.slug} required />
          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">{t('common.description')}</label>
            <textarea
              id="description"
              name="description"
              rows="3"
              className="mt-1 block w-full border rounded-md p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={formData.description}
              onChange={handleChange}
            />
          </div>
          <div className="mb-4">
            <label htmlFor="parent_id" className="block text-sm font-medium text-gray-700">{t('kb.parentCategory')}</label>
            <select
              id="parent_id"
              name="parent_id"
              value={formData.parent_id}
              onChange={handleChange}
              className="mt-1 block w-full border rounded-md p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">{t('kb.noParent')}</option>
              {categories.filter(c => c.id !== editingId).map(c => (
                <option key={c.id} value={c.id}>{c.name}</option>
              ))}
            </select>
          </div>
          <div className="flex space-x-2 pt-2">
            <Button
              type="submit"
              disabled={createMut.isLoading || updateMut.isLoading}
            >
              {isEditing ? t('common.update') : t('common.create')}
            </Button>
            {isEditing &&
              <Button variant="outline" onClick={reset}>
                {t('common.cancel')}
              </Button>
            }
          </div>
        </form>
      </Card>
      <Card
        title={t('kb.categories')}
        className="bg-white shadow-md hover:shadow-lg transition-shadow"
      >
        {categories.length === 0 ? (
          <div className="py-10 text-center text-gray-500">
            <p className="mb-2">{t('kb.noCategories')}</p>
            <p className="text-sm">{t('kb.createCategoryPrompt')}</p>
          </div>
        ) : (
          <div className="divide-y">
            {categories.map(cat => (
              <div key={cat.id} className="py-4 flex justify-between items-center hover:bg-gray-50 px-2 rounded transition-colors">
                <div>
                  <h3 className="font-medium text-blue-700">{cat.name}</h3>
                  <div className="flex items-center text-sm text-gray-500 mt-1">
                    <span className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded-full text-xs">{cat.slug}</span>
                    <span className="mx-2">•</span>
                    <span className="text-xs">{cat.article_count} {t('kb.articles')}</span>
                  </div>
                </div>
                <div className="space-x-2">
                  <button
                    className="text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50 transition-colors"
                    onClick={() => handleEdit(cat)}
                  >
                    {t('common.edit')}
                  </button>
                  <button
                    className="text-red-600 hover:text-red-800 px-2 py-1 rounded hover:bg-red-50 transition-colors"
                    onClick={() => handleDelete(cat.id)}
                    disabled={cat.article_count > 0}
                  >
                    {t('common.delete')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
}