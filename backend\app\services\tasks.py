# tasks.py
import logging
from flask import current_app # Use current_app if needed inside the task

from .embedding_service import EmbeddingService
from app.models.kb_models import KBArticle, KBArticleChunk
from app.extensions import db # Assuming you have a Flask-SQLAlchemy db object
from sqlalchemy.orm import joinedload

logger = logging.getLogger(__name__)

# --- Task Definition ---
# Note: We define the function normally first.
# It will be registered with Procrastinate later.

def generate_embedding_task(chunk_id: int):
    """
    Background task to generate and store embeddings for an article chunk,
    prepending the article title for context.
    Args:
        chunk_id: ID of the KBArticleChunk to process
    """
    logger.info(f"Starting embedding generation task for chunk {chunk_id}")
    db.session.remove() # Clean session

    article_title = None
    chunk_text_content = None
    parent_article_id = None # For logging if chunk not found later

    try:
        # Add a small delay to ensure the chunk is committed to the database
        # This helps with race conditions between the worker and the main process
        import time
        time.sleep(0.5)  # 500ms delay

        # Fetch the chunk AND its parent article in one query using joinedload
        chunk = db.session.query(KBArticleChunk).options(
            joinedload(KBArticleChunk.article) # Eagerly load the related article
        ).get(chunk_id)

        if not chunk:
            logger.error(f"KBArticleChunk {chunk_id} not found")
            # Try one more time after a longer delay
            time.sleep(1.5)  # 1.5s delay
            chunk = db.session.query(KBArticleChunk).options(
                joinedload(KBArticleChunk.article)
            ).get(chunk_id)

            if not chunk:
                logger.error(f"KBArticleChunk {chunk_id} still not found after retry")
                return
        if not chunk.article:
             logger.error(f"Parent KBArticle not found for chunk {chunk_id}")
             # Mark as failed because we need the title
             raise ValueError(f"Parent article missing for chunk {chunk_id}")

        # Store required data in local variables BEFORE commit/detach
        chunk_text_content = chunk.chunk_text
        article_title = chunk.article.title
        parent_article_id = chunk.article_id # Store for logging in case chunk disappears

        logger.info(f"Chunk {chunk_id} (Article {parent_article_id}) found, updating status to 'processing'")
        chunk.embedding_status = 'processing'
        db.session.commit() # Commit detaches objects

        # Validate content before embedding
        if not chunk_text_content: # Title should exist if article was found
             logger.error(f"Chunk text content is empty for chunk {chunk_id}.")
             raise ValueError("Chunk text is empty, cannot generate embedding.")

        # --- Prepare text for embedding (Title + Chunk) ---
        text_to_embed = f"{article_title}\n\n{chunk_text_content}"
        logger.debug(f"Text prepared for embedding (chunk {chunk_id}): '{text_to_embed[:100]}...'")
        # ---

        # Generate embedding using the combined text
        logger.info(f"Generating embedding for chunk {chunk_id} (with title context)...")
        embedding = EmbeddingService.generate_embeddings(text_to_embed,type='RETRIEVAL_DOCUMENT') # Pass combined text
        logger.info(f"Embedding generation finished for chunk {chunk_id}.")

        # --- Re-fetch the chunk for update ---
        db.session.remove()
        chunk = db.session.query(KBArticleChunk).get(chunk_id)
        if not chunk:
            # Use parent_article_id for logging here as chunk.article_id is unavailable
            logger.error(f"KBArticleChunk {chunk_id} (Article {parent_article_id}) not found after embedding generation")
            return
        # ---

        if embedding:
            chunk.embedding_vector = embedding
            chunk.embedding_status = 'completed'
            logger.info(f"Successfully generated embedding for chunk {chunk_id}")
        else:
            chunk.embedding_status = 'failed'
            logger.error(f"Failed to generate embedding for chunk {chunk_id} (embedding service returned None)")

        # Commit the changes for embedding update
        db.session.commit()
        logger.info(f"Successfully updated chunk {chunk_id} with embedding status: {chunk.embedding_status}")

    except Exception as e:
        logger.error(f"Error in generate_embedding_task for chunk {chunk_id}: {str(e)}", exc_info=True)
        try:
            db.session.rollback()
            db.session.remove()
            # Fetch using ID stored earlier if needed
            chunk = db.session.query(KBArticleChunk).get(chunk_id)
            if chunk and chunk.embedding_status != 'completed':
                chunk.embedding_status = 'failed'
                db.session.commit()
                logger.info(f"Marked chunk {chunk_id} as failed after exception")
            elif not chunk:
                 logger.error(f"Could not mark chunk {chunk_id} (Article {parent_article_id}) as failed, chunk not found.")
        except Exception as inner_e:
            logger.error(f"Error updating chunk status to failed for chunk {chunk_id}: {str(inner_e)}", exc_info=True)
            db.session.rollback()
    finally:
        db.session.remove()

def queue_embedding_generation_for_chunk(chunk_id: int):
    """
    Queue a chunk for embedding generation asynchronously.

    Args:
        chunk_id: ID of the KBArticleChunk to generate embeddings for

    Returns:
        bool: True if queuing was successful, False otherwise
    """
    try:
        # Get the Procrastinate app from the Flask app context
        procrastinate_app = current_app.procrastinate
        if not procrastinate_app:
            logger.error(f"Procrastinate app not found in current_app for chunk {chunk_id}")
            return False

        logger.info(f"Attempting to queue embedding generation for chunk {chunk_id}")

        # Use the app instance to defer the task.
        # Make sure the task name matches how it was registered in app/__init__.py
        # Usually it's the function name unless specified otherwise.
        with procrastinate_app.open():
            logger.info(f"Procrastinate app opened, deferring task for chunk {chunk_id}")
            # Defer the task using the registered task object
            # The defer method returns the job ID directly (an integer), not a job object
            job_id = procrastinate_app.tasks["generate_embedding_task"].defer(chunk_id=chunk_id)

        logger.info(f"Queued embedding generation for chunk {chunk_id}, job ID: {job_id}")
        return True
    except AttributeError as ae:
         # Handle case where procrastinate might not be initialized on current_app
        logger.error(f"Procrastinate not properly configured or accessed outside app context: {str(ae)}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"Error queueing embedding generation for chunk {chunk_id}: {str(e)}", exc_info=True)
        return False

def process_animal_group_task(herd_id: int):
    """Background task to assign a herd to the appropriate animal group based on group_name."""
    logger.info(f"Starting animal group processing task for herd {herd_id}")
    db.session.remove()  # Clean session

    try:
        # Fetch the herd
        from ..models import Herd, AnimalGroup
        herd = db.session.query(Herd).get(herd_id)
        if not herd:
            logger.error(f"Herd {herd_id} not found")
            return

        # Skip if group_name is not set
        if not herd.group_name:
            logger.info(f"Herd {herd_id} has no group_name, skipping group assignment")
            return

        # Find or create animal group based on group_name
        animal_group = db.session.query(AnimalGroup).filter_by(
            name=herd.group_name,
            user_id=herd.user_id
        ).first()

        if not animal_group:
            # Create new animal group with the group name
            logger.info(f"Creating new animal group '{herd.group_name}' for herd {herd_id}")
            animal_group = AnimalGroup(
                name=herd.group_name,
                user_id=herd.user_id,
                description=f"Group: {herd.group_name}"
            )

            # Process available data from the herd
            if herd.lactation_number is not None:
                animal_group.lactation_number = herd.lactation_number

            # Calculate days in milk if possible
            if herd.calving_date:
                from datetime import datetime
                today = datetime.utcnow()
                delta = today - herd.calving_date
                animal_group.days_in_milk = delta.days if delta.days > 0 else 0

            db.session.add(animal_group)
            db.session.flush()  # Get ID before further operations

        # Update the herd with the animal_group_id
        herd.animal_group_id = animal_group.id

        db.session.commit()
        logger.info(f"Successfully processed group '{herd.group_name}' for herd {herd_id}, assigned to animal_group_id {animal_group.id}")

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error in process_animal_group_task for herd {herd_id}: {str(e)}", exc_info=True)
    finally:
        db.session.remove()

def process_animal_groups_batch_task(session_id: str, mode: str):
    """Background task to process animal groups for all herds in an import session."""
    logger.info(f"Starting batch animal group processing for import session {session_id}")
    db.session.remove()  # Clean session

    try:
        # Fetch all confirmed herds from this import session
        from ..models import Herd, AnimalGroup
        herds = db.session.query(Herd).filter_by(
            import_session_id=session_id,
            import_status='confirmed'
        ).all()

        if not herds:
            logger.info(f"No confirmed herds found for import session {session_id}")
            return

        logger.info(f"Processing animal groups for {len(herds)} herds in session {session_id}")

        # --- Start Added Code ---
        if mode == 'replace':
            user_id = herds[0].user_id
            db.session.query(AnimalGroup).filter(
                AnimalGroup.user_id == user_id
            ).delete(synchronize_session='fetch')
            db.session.flush() # Make the changes available within the transaction
        # --- End Added Code ---

        # Get all unique group names from herds
        group_names = {}  # Group name -> list of herds
        for herd in herds:
            if herd.group_name:
                if herd.group_name not in group_names:
                    group_names[herd.group_name] = []
                group_names[herd.group_name].append(herd)

        logger.info(f"Found {len(group_names)} unique group names in session {session_id}")

        # Process each unique group
        for group_name, group_herds in group_names.items():
            if not group_herds:
                continue

            # Take user_id from first herd in group
            user_id = group_herds[0].user_id

            # Find or create animal group
            animal_group = db.session.query(AnimalGroup).filter_by(
                name=group_name,
                user_id=user_id
            ).first()

            if not animal_group:
                animal_group = AnimalGroup(
                    name=group_name,
                    user_id=user_id,
                    description=f"Group: {group_name}"
                )

                # Aggregate data from herds for this group
                # Calculate average lactation number if available
                lactation_numbers = [h.lactation_number for h in group_herds if h.lactation_number is not None]
                if lactation_numbers:
                    animal_group.lactation_number = sum(lactation_numbers) / len(lactation_numbers)

                # Calculate average days in milk if available
                from datetime import datetime
                today = datetime.utcnow()
                days_in_milk = []

                for herd in group_herds:
                    if herd.calving_date:
                        # Assuming herd.calving_date is a datetime object
                        try:
                           delta = today - herd.calving_date
                           if delta.days > 0:
                               days_in_milk.append(delta.days)
                        except TypeError:
                            # Handle cases where calving_date might not be a comparable type
                            logger.warning(f"Could not calculate DIM for herd {herd.id} with calving_date {herd.calving_date}")


                if days_in_milk:
                    animal_group.days_in_milk = sum(days_in_milk) / len(days_in_milk)

                db.session.add(animal_group)
                db.session.flush()  # Get ID before further operations

            # Update all herds in this group with the animal_group_id
            for herd in group_herds:
                herd.animal_group_id = animal_group.id

        db.session.commit()
        logger.info(f"Batch processing complete: processed {len(group_names)} animal groups for session {session_id}")

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error in batch animal group processing for session {session_id}: {str(e)}", exc_info=True)
    finally:
        db.session.remove()

def queue_animal_group_processing(herd_id: int):
    """Queue a herd for animal group processing."""
    try:
        procrastinate_app = current_app.procrastinate
        if not procrastinate_app:
            logger.error(f"Procrastinate app not found in current_app for herd {herd_id}")
            return False

        logger.info(f"Attempting to queue animal group processing for herd {herd_id}")

        with procrastinate_app.open():
            job_id = procrastinate_app.tasks["process_animal_group_task"].defer(herd_id=herd_id)

        logger.info(f"Queued animal group processing for herd {herd_id}, job ID: {job_id}")
        return True
    except Exception as e:
        logger.error(f"Error queueing animal group processing for herd {herd_id}: {str(e)}", exc_info=True)
        return False

def queue_batch_animal_group_processing(session_id: str, mode: str):
    """Queue batch processing for all herds in an import session."""
    try:
        procrastinate_app = current_app.procrastinate
        if not procrastinate_app:
            logger.error(f"Procrastinate app not found in current_app for session {session_id}")
            return False

        logger.info(f"Attempting to queue batch animal group processing for session {session_id}")

        with procrastinate_app.open():
            job_id = procrastinate_app.tasks["process_animal_groups_batch_task"].defer(session_id=session_id, mode=mode)

        logger.info(f"Queued batch animal group processing for session {session_id}, job ID: {job_id}")
        return True
    except Exception as e:
        logger.error(f"Error queueing batch animal group processing for session {session_id}: {str(e)}", exc_info=True)
        return False