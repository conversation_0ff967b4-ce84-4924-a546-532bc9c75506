"""remove index on vector

Revision ID: 9fbdf002bfc8
Revises: cfcd747fdd3e
Create Date: 2025-04-16 16:40:15.529799

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9fbdf002bfc8'
down_revision = 'cfcd747fdd3e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint('animal_groups_herd_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'herds', ['herd_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint('constraints_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint('feed_nutrients_feed_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('feed_nutrients_nutrient_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'nutrients', ['nutrient_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint('feeds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint('herds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_article_chunks', schema=None) as batch_op:
        batch_op.drop_index('ix_ration_app_kb_article_chunks_embedding_vector')
        batch_op.drop_constraint('kb_article_chunks_article_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'kb_articles', ['article_id'], ['id'], referent_schema='ration_app', ondelete='CASCADE')

    with op.batch_alter_table('kb_article_tags', schema=None) as batch_op:
        batch_op.drop_constraint('kb_article_tags_tag_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('kb_article_tags_article_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'kb_tags', ['tag_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'kb_articles', ['article_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_articles', schema=None) as batch_op:
        batch_op.drop_constraint('kb_articles_category_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('kb_articles_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'kb_categories', ['category_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_categories', schema=None) as batch_op:
        batch_op.drop_constraint('kb_categories_parent_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'kb_categories', ['parent_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('kb_feedback', schema=None) as batch_op:
        batch_op.drop_constraint('kb_feedback_article_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('kb_feedback_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'kb_articles', ['article_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint('ration_feeds_feed_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('ration_feeds_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint('rations_animal_group_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('rations_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'animal_groups', ['animal_group_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint('staged_import_sessions_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('staged_import_sessions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('staged_import_sessions_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('rations_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('rations_animal_group_id_fkey', 'animal_groups', ['animal_group_id'], ['id'])

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('ration_feeds_ration_id_fkey', 'rations', ['ration_id'], ['id'])
        batch_op.create_foreign_key('ration_feeds_feed_id_fkey', 'feeds', ['feed_id'], ['id'])

    with op.batch_alter_table('kb_feedback', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_feedback_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('kb_feedback_article_id_fkey', 'kb_articles', ['article_id'], ['id'])

    with op.batch_alter_table('kb_categories', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_categories_parent_id_fkey', 'kb_categories', ['parent_id'], ['id'])

    with op.batch_alter_table('kb_articles', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_articles_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('kb_articles_category_id_fkey', 'kb_categories', ['category_id'], ['id'])

    with op.batch_alter_table('kb_article_tags', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_article_tags_article_id_fkey', 'kb_articles', ['article_id'], ['id'])
        batch_op.create_foreign_key('kb_article_tags_tag_id_fkey', 'kb_tags', ['tag_id'], ['id'])

    with op.batch_alter_table('kb_article_chunks', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('kb_article_chunks_article_id_fkey', 'kb_articles', ['article_id'], ['id'], ondelete='CASCADE')
        batch_op.create_index('ix_ration_app_kb_article_chunks_embedding_vector', ['embedding_vector'], unique=False)

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('herds_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feeds_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feed_nutrients_nutrient_id_fkey', 'nutrients', ['nutrient_id'], ['id'])
        batch_op.create_foreign_key('feed_nutrients_feed_id_fkey', 'feeds', ['feed_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('constraints_ration_id_fkey', 'rations', ['ration_id'], ['id'])

    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('animal_groups_herd_id_fkey', 'herds', ['herd_id'], ['id'])

    # ### end Alembic commands ###
