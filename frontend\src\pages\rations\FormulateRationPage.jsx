import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import { getRationById, formulateRation } from '../../services/rationService'
import { getFeeds } from '../../services/feedService'
import { getAnimalGroups } from '../../services/animalGroupService'
import { getNrcRequirements } from '../../services/nrcRequirementsService'
import { getNrcModelVersions } from '../../services/nrcCoefficientService'

// Import the new layout component
import RationFormulationLayout from '../../components/rations/RationFormulationLayout'

const FormulateRationPage = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [isFormulating, setIsFormulating] = useState(false)
  const [result, setResult] = useState(null)
  const [selectedNrcModelId, setSelectedNrcModelId] = useState('') // For NRC model selection

  // Get available NRC model versions to set the first one as default
  const { data: modelVersionsData } = useQuery('nrc-model-versions', () => getNrcModelVersions(), {
    onSuccess: (data) => {
      if (data?.versions?.length > 0 && !selectedNrcModelId) {
        setSelectedNrcModelId(data.versions[0].id.toString())
      }
    }
  })
  const [optimizationSettings, setOptimizationSettings] = useState({
    objective: 'minimize_cost',
    maxIterations: 1000,
    tolerance: 0.001
  })

  // Convert ration data to formData structure needed by the layout
  const [formData, setFormData] = useState({
    id: '',
    name: '',
    description: '',
    animal_group_id: '',
    feeds: [],
    constraints: []
  })

  // Get ration data
  const { data: ration, isLoading: isRationLoading, error: rationError } =
    useQuery(['ration', id], () => getRationById(id))

  // Get feeds and animal groups
  const { data: feedsData = { feeds: [] }, isLoading: isLoadingFeeds } =
    useQuery('feeds', () => getFeeds(1, 100))
  const feeds = feedsData.feeds || []

  const { data: animalGroups = [], isLoading: isLoadingGroups } =
    useQuery('animal-groups', getAnimalGroups)

  // Get NRC requirements
  const { data: nrcData, refetch: refetchNrcData } = useQuery(
    ['nrc-requirements', formData.animal_group_id, selectedNrcModelId],
    () => getNrcRequirements(formData.animal_group_id, selectedNrcModelId),
    {
      enabled: !!formData.animal_group_id
    }
  )

  // Update formData when ration data is loaded
  useEffect(() => {
    if (ration) {
      setFormData({
        id: ration.id,
        name: ration.name || '',
        description: ration.description || '',
        animal_group_id: ration.animal_group_id || '',
        feeds: ration.feeds?.map(feed => ({
          feed_id: feed.feed_id,
          min_inclusion_percentage: feed.min_inclusion_percentage,
          max_inclusion_percentage: feed.max_inclusion_percentage,
          actual_inclusion_percentage: feed.actual_inclusion_percentage,
          cost_contribution: feed.cost_contribution
        })) || [],
        constraints: ration.constraints?.map(constraint => ({
          nutrient_name: constraint.nutrient_name,
          min_value: constraint.min_value,
          max_value: constraint.max_value,
          actual_value: constraint.actual_value
        })) || []
      })

      // If already formulated, set the result
      if (ration.last_formulated_at) {
        setResult({
          status: 'success',
          feeds: ration.feeds.reduce((acc, feed) => {
            acc[feed.feed_id] = {
              inclusion_percentage: feed.actual_inclusion_percentage,
              cost_contribution: feed.cost_contribution
            }
            return acc
          }, {}),
          nutrients: ration.constraints.reduce((acc, constraint) => {
            acc[constraint.nutrient_name] = {
              actual_value: constraint.actual_value,
              min_value: constraint.min_value,
              max_value: constraint.max_value
            }
            return acc
          }, {}),
          total_cost: ration.feeds.reduce((sum, feed) => sum + (feed.cost_contribution || 0), 0)
        })
      }
    }
  }, [ration])

  // Effect to refetch NRC data when model changes
  useEffect(() => {
    if (formData.animal_group_id && selectedNrcModelId) {
      refetchNrcData();
    }
  }, [selectedNrcModelId, formData.animal_group_id, refetchNrcData]);

  // Formulate mutation
  const formulateMutation = useMutation(() =>
    formulateRation(id, {
      ...optimizationSettings,
      nrc_model_id: selectedNrcModelId || undefined
    }), {
    onSuccess: (data) => {
      setResult(data.result)
      queryClient.invalidateQueries(['ration', id])
      setIsFormulating(false)
    },
    onError: (error) => {
      setIsFormulating(false)
    }
  })

  // Handle formulate button click
  const handleFormulate = () => {
    setIsFormulating(true)
    setResult(null)
    formulateMutation.mutate()
  }

  // Handle optimization settings change
  const handleOptimizationSettingsChange = (settings) => {
    setOptimizationSettings(settings)
  }

  // Handle form data change
  const handleFormDataChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Handle feed change
  const handleFeedChange = (updatedFeeds) => {
    setFormData(prev => ({
      ...prev,
      feeds: updatedFeeds
    }))
  }

  // Handle constraint change
  const handleConstraintChange = (updatedConstraints) => {
    setFormData(prev => ({
      ...prev,
      constraints: updatedConstraints
    }))
  }

  // Loading state
  const isLoading = isRationLoading || isLoadingFeeds || isLoadingGroups

  if (isLoading) {
    return <p className="text-center py-4">{t('common.loading')}</p>
  }

  if (rationError) {
    return (
      <Card>
        <p className="text-red-500">{t('rations.errorLoading')}: {rationError.message}</p>
        <div className="mt-4">
          <Link to="/rations">
            <Button variant="outline">{t('rations.backToRations')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  if (!ration) {
    return (
      <Card>
        <p className="text-gray-500">{t('rations.rationNotFound')}</p>
        <div className="mt-4">
          <Link to="/rations">
            <Button variant="outline">{t('rations.backToRations')}</Button>
          </Link>
        </div>
      </Card>
    )
  }

  // Check if ration has feeds and constraints
  const hasFeeds = ration.feeds && ration.feeds.length > 0
  const canFormulate = hasFeeds

  return (
    <div className="space-y-6">
      <RationFormulationLayout
        formData={formData}
        feeds={feeds}
        animalGroups={animalGroups}
        formulationResult={result}
        errors={{}}
        isFormulating={isFormulating}
        isSaving={false}
        isSaved={true}
        nrcData={nrcData}
        selectedNrcModelId={selectedNrcModelId}
        optimizationSettings={optimizationSettings}
        onSave={() => navigate(`/rations/${id}`)}
        onFormulate={handleFormulate}
        onNrcModelChange={handleNrcModelChange}
        onFormDataChange={handleFormDataChange}
        onFeedChange={handleFeedChange}
        onConstraintChange={handleConstraintChange}
        onOptimizationSettingsChange={handleOptimizationSettingsChange}
        canFormulate={canFormulate}
      />

      <div className="flex justify-between">
        <Link to={`/rations/${id}`}>
          <Button variant="outline">{t('rations.backToRation')}</Button>
        </Link>
      </div>
    </div>
  )
}

export default FormulateRationPage