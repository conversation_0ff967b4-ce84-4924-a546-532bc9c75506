# Updated backend/app/services/llm_service.py
import json
import os
import sys
import logging
import traceback
from flask import current_app
from google import genai
from google.genai import types # Ensure 'types' is imported

# Configure logging
logger = logging.getLogger(__name__)

# Fix the import to use absolute path
# Ensure this path is correct for your project structure
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Factory for LLM providers
class LLMFactory:
    _cached_provider = None
    @staticmethod
    def get_provider(provider_name):
        logger.info(f"Creating LLM provider: {provider_name}")
        if provider_name.lower() == "openai":
            return OpenAIProvider()
        elif provider_name.lower() == "gemini":
            return GeminiProvider()
        elif provider_name.lower() == "deepseek":
            return DeepseekProvider()
        elif provider_name.lower() == 'qwen':
            return QwenProvider()
        else:
            error_msg = f"Unsupported LLM provider: {provider_name}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    @staticmethod
    def get_current_provider():
        if LLMFactory._cached_provider:
            return LLMFactory._cached_provider
        llm_provider=None
        provider_name = None
        try:
            provider_name = current_app.config.get('LLM_PROVIDER', 'gemini')
            logger.info(f"Attempting to initialize LLM provider '{provider_name}' from config.")
            llm_provider = LLMFactory.get_provider(provider_name)
            logger.info(f"LLM provider '{provider_name}' initialized successfully.")

        except Exception as e:
            logger.error(f"Failed to initialize primary LLM provider '{provider_name}': {str(e)}")
            primary_provider_failed = provider_name is None or provider_name.lower() != 'openai'
            openai_key_exists = current_app.config.get('OPENAI_API_KEY')

            if primary_provider_failed and openai_key_exists:
                logger.warning("Falling back to OpenAI provider due to error with primary provider.")
                try:
                    llm_provider = OpenAIProvider()
                    logger.info("Successfully initialized fallback OpenAI provider.")
                except Exception as fallback_error:
                    logger.error(f"Failed to initialize fallback OpenAI provider: {str(fallback_error)}")
                    raise fallback_error
            else:
                raise e

        LLMFactory._cached_provider = llm_provider
        return llm_provider


# Base LLM provider interface
class LLMProvider:
    def analyze_data(self, prompt, model=None, max_tokens=2000, temperature=1):
        raise NotImplementedError("Subclasses must implement this method")

    def call_with_functions(self, prompt, function_declarations, model=None, max_tokens=2000, temperature=0.3):
        """
        Call LLM with function declarations and return a standardized response format.

        All providers should return a response with the following structure:
        {
            'thinking_content': str,  # Optional reasoning/thinking content
            'content': str,           # Main response content
            'function_call': {        # Optional single function call (for backward compatibility)
                'name': str,          # Function name
                'arguments': dict     # Function arguments as a dictionary
            },
            'function_calls': [       # Optional multiple function calls
                {
                    'name': str,      # Function name
                    'arguments': dict # Function arguments as a dictionary
                }
            ]
        }
        """
        raise NotImplementedError("Subclasses must implement this method for function calling")

    def _standardize_response(self, provider_response, provider_type):
        """
        Convert provider-specific response to standardized format.
        This is a helper method that can be used by provider implementations.
        """
        # Default empty response structure
        standardized = {
            'thinking_content': '',
            'content': '',
            'function_call': None,
            'function_calls': []
        }

        # Each provider will need to implement their own conversion logic
        return standardized

# OpenAI implementation (assuming no changes needed for function calling here)
class OpenAIProvider(LLMProvider):
    def __init__(self):
        logger.info("Initializing OpenAI provider")
        import openai
        if not current_app.config.get('OPENAI_API_KEY'):
            logger.error("OpenAI API key not found")
            raise ValueError("OpenAI API key not set. Please set OPENAI_API_KEY environment variable.")
        self.client = openai.OpenAI(api_key=current_app.config.get('OPENAI_API_KEY'), base_url=current_app.config.get('OPENAI_API_ENDPOINT'))
        logger.info("OpenAI client initialized")

    def analyze_data(self, prompt, model="gpt-4", max_tokens=2000, temperature=0.3):
        # Placeholder implementation for non-function calling
        logger.info(f"Analyzing data with OpenAI model: {model}, max_tokens: {max_tokens}")
        messages = [
            {"role": "system", "content": "You are an Excel data analysis assistant."},
            {"role": "user", "content": prompt}
        ]

        try:
            logger.debug("Sending request to OpenAI API")
            response = self.client.chat.completions.create(
                model=current_app.config.get('OPENAI_MODEL'),
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature
            )

            result = response.choices[0].message.content.strip()

            return result
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {str(e)}")
            raise

    def call_with_functions(self, prompt, function_declarations, model=None, max_tokens=4000, temperature=0.3):
        """Calls OpenAI API with function/tool declarations."""
        if model is None:
            model = current_app.config.get('OPENAI_MODEL')

        logger.info(f"Calling OpenAI model {model} with function declarations.")

        # Ensure function_declarations are formatted correctly for OpenAI's 'tools' parameter
        # Expected format: [{"type": "function", "function": {...}}, ...]
        # where {...} includes 'name', 'description', 'parameters' (JSON schema)
        tools = [{"type": "function", "function": func} for func in function_declarations]

        messages = [
            # Optional: Add a system message guiding the model on tool usage
            # {"role": "system", "content": "You are an assistant that uses provided tools to answer questions about data."},
            {"role": "user", "content": prompt}
        ]

        try:
            logger.debug("Sending request with tools to OpenAI API")
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                tools=tools,
                tool_choice="auto",  # Let the model decide whether to use a tool or respond directly
                max_tokens=max_tokens,
                temperature=temperature,
            )

            # Use a standardized response handler similar to other providers
            return self._standardize_openai_response(response.choices[0].message)

        except Exception as e:
            logger.error(f"Error calling OpenAI API with functions: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'thinking_content': '',
                'content': f"API error: {str(e)}",
                'function_call': None,
                'function_calls': []
            }

    def _standardize_openai_response(self, response_message):
        """Convert OpenAI response to standardized format."""
        # Default empty response structure
        standardized = {
            'thinking_content': '',
            'content': '',
            'function_call': None,
            'function_calls': []
        }

        # Extract content if available
        if hasattr(response_message, 'content') and response_message.content:
            standardized['content'] = response_message.content.strip()

        # Extract function calls if available
        if hasattr(response_message, 'tool_calls') and response_message.tool_calls:
            function_calls = []
            for tool_call in response_message.tool_calls:
                if tool_call.type == "function":
                    try:
                        function_name = tool_call.function.name
                        function_args = json.loads(tool_call.function.arguments)
                        logger.info(f"Extracted function call from OpenAI response: {function_name}")

                        function_call_obj = {
                            'name': function_name,
                            'arguments': function_args
                        }
                        function_calls.append(function_call_obj)

                        # Set the first function call for backward compatibility
                        if standardized['function_call'] is None:
                            standardized['function_call'] = function_call_obj

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse function arguments from OpenAI: {e}")

            standardized['function_calls'] = function_calls

        return standardized


# Google Gemini implementation
class GeminiProvider(LLMProvider):
    def __init__(self):
        logger.info("Initializing Gemini provider")
        try:
            if not current_app.config.get('GEMINI_API_KEY'):
                logger.error("Gemini API key not found")
                raise ValueError("Gemini API key not set. Please set GEMINI_API_KEY environment variable.")

            logger.info(f"Gemini API key length: {len(current_app.config.get('GEMINI_API_KEY'))}")
            # Use the Client API as shown in the user manual
            self.client = genai.Client(api_key=current_app.config.get('GEMINI_API_KEY'))
            logger.info("Gemini client initialized successfully")
        except ImportError:
            error_msg = "Failed to import google.genai. Please install with: pip install google-generativeai"
            logger.error(error_msg)
            raise ImportError(error_msg)
        except Exception as e:
            error_msg = f"Failed to initialize Gemini API: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            raise ValueError(error_msg)

    def analyze_data(self, prompt, model=None, max_tokens=2000, temperature=0.3):
        # This method handles simple text generation without function calling
        # Use current_app.configured model if none provided
        if model is None:
            model = current_app.config.get('GEMINI_ANALYZE_MODEL')

        logger.info(f"Analyzing data (text only) with Gemini model: {model}, temperature: {temperature}")

        try:
            # Basic text generation request
            response = self.client.models.generate_content(
                model=model,
                contents=prompt,
                config=types.GenerateContentConfig(
                    temperature=temperature,
                    max_output_tokens=max_tokens
                )
            )

            # Extract text content
            result = response.candidates[0].content.parts[0].text.strip() if response.candidates else ""

            logger.debug(f"Received text-only response from Gemini API: {result[:100]}...")
            return result

        except Exception as e:
            logger.error(f"Error in Gemini text-only analysis: {str(e)}")
            logger.error(traceback.format_exc())
            return f"Error analyzing data: {str(e)}" # Return error message

    def call_with_functions(self, prompt, function_declarations, model=None, max_tokens=8000, temperature=0.3):
        """Function call handling for Gemini with simple retry for 500 errors."""
        if model is None:
            model = current_app.config.get('GEMINI_MODEL')

        logger.debug(f"Calling Gemini model {model} with function declarations.")

        # Configure tools for function calling
        tools = types.Tool(function_declarations=function_declarations)

        # Set function calling mode to AUTO (default)
        tool_config = types.ToolConfig(
            function_calling_config=types.FunctionCallingConfig(mode="AUTO")
        )

        config = types.GenerateContentConfig(
            tools=[tools],
            tool_config=tool_config,
            temperature=temperature,
            max_output_tokens=max_tokens,
        )

        #logger.debug(f"sending prompt:{prompt}")

        # Simple retry mechanism - just try once more for 500 errors
        import time
        try:
            # First attempt
            raw_response = self.client.models.generate_content(
                model=model,
                contents=prompt,  # Assuming prompt is correctly formatted Content or list[Content]
                config=config,
            )
            if raw_response:
                # Convert Gemini response to standardized format
                return self._standardize_gemini_response(raw_response)
            else:
                logger.warning("Empty response from Gemini API")
                return {
                    'thinking_content': '',
                    'content': 'Empty response from Gemini API',
                    'function_call': None,
                    'function_calls': []
                }

        except Exception as e:
            # Check if this is a 500 error that we should retry
            error_str = str(e).lower()
            is_server_error = "500" in error_str or "internal" in error_str

            if is_server_error:
                logger.warning(f"Gemini API 500 error: {e}. Waiting 2 seconds and retrying once...")
                try:
                    # Wait 2 seconds before retry
                    time.sleep(2)

                    # Second attempt
                    raw_response = self.client.models.generate_content(
                        model=model,
                        contents=prompt,
                        config=config,
                    )
                    if raw_response:
                        logger.info("Successfully completed request after retry")
                        return self._standardize_gemini_response(raw_response)
                    else:
                        logger.warning("Empty response from Gemini API on retry")
                        return {
                            'thinking_content': '',
                            'content': 'Empty response from Gemini API after retry',
                            'function_call': None,
                            'function_calls': []
                        }
                except Exception as retry_e:
                    logger.error(f"Gemini API error on retry: {retry_e}")
                    return {
                        'thinking_content': '',
                        'content': f"API error after retry: {str(retry_e)}",
                        'function_call': None,
                        'function_calls': []
                    }
            else:
                logger.error(f"Non-retryable Gemini API error: {e}")

            # If we get here, the retry failed or it wasn't a retryable error
            logger.exception(f"Error in Gemini function calling: {e}")
            return {
                'thinking_content': '',
                'content': f"API error: {str(e)}",
                'function_call': None,
                'function_calls': []
            }

    def _standardize_gemini_response(self, response):
        """Convert Gemini response to standardized format."""
        # Default empty response structure
        standardized = {
            'thinking_content': '',
            'content': '',
            'function_call': None,
            'function_calls': []
        }

        if not response or not hasattr(response, 'candidates') or not response.candidates:
            return standardized

        # Extract text and function calls from parts
        all_parts = response.candidates[0].content.parts
        collected_text_parts = []
        function_call_parts = []

        for part in all_parts:
            if hasattr(part, 'function_call') and part.function_call:
                function_call_parts.append(part.function_call)
            elif hasattr(part, 'text') and part.text:
                collected_text_parts.append(part.text.strip())

        # Set content from collected text parts
        standardized['content'] = " ".join(collected_text_parts).strip()

        # Set function calls if present
        if function_call_parts:
            function_calls = []
            for function_call_part in function_call_parts:
                try:
                    function_args = {key: value for key, value in function_call_part.args.items()}
                    function_call_obj = {
                        'name': function_call_part.name,
                        'arguments': function_args
                    }
                    function_calls.append(function_call_obj)

                    # Set the first function call for backward compatibility
                    if standardized['function_call'] is None:
                        standardized['function_call'] = function_call_obj

                except Exception as e:
                    logger.error(f"Error processing Gemini function arguments: {e}")

            standardized['function_calls'] = function_calls

        return standardized

# DeepSeek implementation (assuming no changes needed for function calling here)
class DeepseekProvider(LLMProvider):
    def __init__(self):
        logger.info("Initializing DeepSeek provider")
        from openai import OpenAI
        if not current_app.config.get('DEEPSEEK_API_KEY'):
            logger.error("DeepSeek API key not found")
            raise ValueError("DeepSeek API key not set. Please set DEEPSEEK_API_KEY environment variable.")
        self.client = OpenAI(api_key=current_app.config.get('DEEPSEEK_API_KEY'), base_url=current_app.config.get('DEEPSEEK_API_BASE'))
        logger.info("DeepSeek client initialized using OpenAI compatibility")

    def analyze_data(self, prompt, model="deepseek-chat", max_tokens=2000, temperature=0.2):
         # Placeholder implementation for non-function calling
        logger.info(f"Analyzing data with DeepSeek model: {model}, max_tokens: {max_tokens}")
        try:
            logger.debug("Sending request to DeepSeek API")
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )

            result = response.choices[0].message.content.strip()
            logger.debug(f"Received response from DeepSeek API: {result}...")
            return result
        except Exception as e:
            logger.error(f"Error calling DeepSeek API: {str(e)}")
            raise

    def call_with_functions(self, prompt, function_declarations, model="deepseek-chat", max_tokens=2000, temperature=0.3):
        # Placeholder: Deepseek function calling via OpenAI API would be implemented here
        logger.warning("Deepseek function calling not fully implemented in this provider.")
        # Fall back to simple analysis but use standardized format
        return {
            'thinking_content': '',
            'content': self.analyze_data(prompt, model, max_tokens, temperature),
            'function_call': None,
            'function_calls': []
        }


class QwenProvider(LLMProvider):
    def __init__(self):
        logger.info("Initializing Qwen provider")
        import openai
        if not current_app.config.get('QWEN_API_KEY'):
            logger.error("QWEN API key not found")
            raise ValueError("QWEN API key not set. Please set QWEN_API_KEY environment variable.")
        self.client = openai.OpenAI(api_key=current_app.config.get('QWEN_API_KEY'), base_url="https://openrouter.ai/api/v1")
        logger.info("OpenAI client initialized")

    def analyze_data(self, prompt, model="qwen/qwen3-30b-a3b:free", max_tokens=2000, temperature=0.3):
        # Placeholder implementation for non-function calling
        logger.info(f"Analyzing data with qwen model: {model}, max_tokens: {max_tokens}")
        messages = [
            {"role": "system", "content": "You are an Excel data analysis assistant."},
            {"role": "user", "content": prompt}
        ]

        try:
            logger.debug("Sending request to OpenAI API")
            response = self.client.chat.completions.create(
                model=current_app.config.get('QWEN_MODEL'),
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature
            )

            result = response.choices[0].message.content.strip()
            logger.debug(f"Received response from OpenAI API: {result[:100]}...")
            return result
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {str(e)}")
            raise

    def call_with_functions(self, prompt, function_declarations, model=None, max_tokens=4000, temperature=0.3):
        """Calls QWEN API with function/tool declarations."""
        from qwen_agent.llm import get_chat_model

        if model is None:
            model = current_app.config.get('QWEN_MODEL')

        logger.info(f"Calling QWEN model {model} with function declarations.")

        try:
            llm = get_chat_model({
                "model": model,
                "model_server": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "api_key": current_app.config.get('QWEN_API_KEY'),
            })

            # Ensure function_declarations are formatted correctly for OpenAI's 'tools' parameter
            # Expected format: [{"type": "function", "function": {...}}, ...]
            # where {...} includes 'name', 'description', 'parameters' (JSON schema)
            tools = [{"type": "function", "function": func} for func in function_declarations]

            messages = [
                # Optional: Add a system message guiding the model on tool usage
                # {"role": "system", "content": "You are an assistant that uses provided tools to answer questions about data."},
                {"role": "user", "content": prompt}
            ]

            '''
    example response:[{'role': 'assistant', 'content': '', 'reasoning_content': '好的，用户需要帮助创建一个动物饲料配方，目前还没有添加任何饲 料，但已经有了一些可用的饲料选项和营养约束。首先，我需要确保满足所有给定的约束条件，特别是DMI（干物质摄入量）必须存在，用户提到已经有一个DMI的约束是23.14到23.14公斤，所以这一步可能已经完成。\n\n接下来，我需要选择合适的饲料来组合，使得总重量等于DMI的值，并且所有营养指标都在允许范围内。当前可用的饲料中，比如花生秧、全株青贮、黄贮等，它们的营养成分各不相同。例如，豆粕 的粗蛋白含量很高，而一些青贮饲料可能在NDF和ADF方面表现较好。\n\n首先，我应该检查现有的饲料是否能够满足营养约束。比如，ADF的最小值是20%，需要确保所选饲料的ADF总和不低于这个值。同时，钙 、磷、粗蛋白、NDF、NEL等也需要符合要求。由于用户提到可以使用add_nutrients_to_feed来添加缺失的营养成分，但可能需要根据一般知识来补充。\n\n现在，我需要考虑如何组合这些饲料。假设用户没有 指定特定的动物或特殊需求，所以按照常规的营养需求来平衡。可能需要先确定主要的能量来源，比如玉米或高粱，然后添加蛋白质来源如豆粕，再调整纤维成分如青贮饲料。同时，注意成本因素，比如全株青贮成本较低，可能作为基础饲料。\n\n但当前可用饲料中，像全株青贮（ID 127）的ADF是0.255，NDF是0.43，而黄贮（ID 128）的NDF较高，可能有助于达到NDF的30-35%的要求。豆粕（ID 135）的粗蛋白很高 ，可以用来满足蛋白质需求，但可能需要搭配其他饲料来平衡其他指标。\n\n另外，需要确保总重量等于DMI的23.14公斤。例如，如果选择全株青贮和豆粕，计算它们的比例，使得总重量为23.14公斤，并检查 各营养指标是否达标。可能需要多次尝试不同的组合，并使用check_nutrition函数来验证。\n\n不过，用户可能希望我直接进行计算，而不是依赖优化算法。因此，我需要手动调整各饲料的比例，逐步逼近目 标。例如，先加入一定量的全株青贮作为基础，再添加豆粕提高蛋白质，同时调整其他饲料以满足ADF、NDF等要求。\n\n最后，确保所有约束都被满足，并调用make_ration函数提交最终的配方。如果发现某些 营养指标无法满足，可能需要添加额外的饲料或调整比例，甚至考虑添加补充剂。'}, {'role': 'assistant', 'content': '', 'reasoning_content': '', 'function_call': {'name': 'add_feed_to_ration', 'arguments': '{"feeds": [{"feed_id": 127, "min_inclusion_percentage": 20, "max_inclusion_percentage": 30}, {"feed_id": 135, "min_inclusion_percentage": 10, "max_inclusion_percentage": 20}, {"feed_id": 128, "min_inclusion_percentage": 10, "max_inclusion_percentage": 20}]}'}}]
    '''

            logger.debug("Sending request with tools to qwen API")
            responses = []
            for response_chunk in llm.chat(
                messages=messages,
                functions=tools,
                extra_generate_cfg=dict(max_tokens=max_tokens),
                stream=True,
            ):
                responses = response_chunk  # Keep the last chunk which contains the full response
                messages.extend(response_chunk)

            logger.info(f"Received response from qwen API: {responses}")

            # Convert Qwen response to standardized format
            return self._standardize_qwen_response(responses)

        except Exception as e:
            logger.error(f"Error calling Qwen API with functions: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'thinking_content': '',
                'content': f"API error: {str(e)}",
                'function_call': None,
                'function_calls': []
            }

    def _standardize_qwen_response(self, responses):
        """Convert Qwen response to standardized format."""
        # Default empty response structure
        standardized = {
            'thinking_content': '',
            'content': '',
            'function_call': None,
            'function_calls': []
        }

        if not responses:
            return standardized

        function_calls = []

        # Process each response message
        for msg in responses:
            if msg.get('role') == 'assistant':
                # Extract content
                if msg.get('content'):
                    standardized['content'] = msg.get('content', '')

                # Extract thinking content
                if msg.get('reasoning_content'):
                    standardized['thinking_content'] = msg.get('reasoning_content', '')

                # Extract function call
                if msg.get('function_call'):
                    func_call = msg.get('function_call')
                    func_name = func_call.get('name')

                    # Parse arguments from string to dict if needed
                    func_args = func_call.get('arguments', '{}')
                    if isinstance(func_args, str):
                        try:
                            func_args = json.loads(func_args)
                        except json.JSONDecodeError as e:
                            logger.error(f"Error parsing Qwen function arguments: {e}")
                            func_args = {}

                    function_call_obj = {
                        'name': func_name,
                        'arguments': func_args
                    }
                    function_calls.append(function_call_obj)

                    # Set the first function call for backward compatibility
                    if standardized['function_call'] is None:
                        standardized['function_call'] = function_call_obj

        standardized['function_calls'] = function_calls
        return standardized