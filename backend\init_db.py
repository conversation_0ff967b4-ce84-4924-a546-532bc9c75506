from dotenv import load_dotenv
import os
from sqlalchemy import text
from datetime import datetime

# Load environment variables from .env file
load_dotenv()

from app import create_app
from app.extensions import db
from app.models import User, Feed, Nutrient, FeedNutrient, Herd, AnimalGroup, Ration, RationFeed, Constraint
from app.models.nrc_coefficients import NrcModelVersion, NrcCoefficientGroup, NrcCoefficient

# Create a Flask application context
app = create_app(os.getenv('FLASK_CONFIG', 'default'))

with app.app_context():
    # Create the schema if it doesn't exist
    db.session.execute(text('CREATE SCHEMA IF NOT EXISTS ration_app'))
    db.session.commit()

    # Create all tables
    db.create_all()

    print("Database initialized successfully!")

    # Add initial data if needed
    if not User.query.first():
        admin = User(
            email='<EMAIL>',
            password='admin123',
            name='Admin User'
        )
        db.session.add(admin)
        db.session.commit()
        print("Created default admin user: <EMAIL> / admin123")

    # Add some basic nutrients if they don't exist
    if not Nutrient.query.first():
        nutrients = [
            Nutrient(name_en='Dry Matter', name_zh='干物质', unit='%', description='Total dry matter content'),
            Nutrient(name_en='Crude Protein', name_zh='粗蛋白', unit='%', description='Protein content'),
            Nutrient(name_en='NDF', name_zh='中性洗涤纤维', unit='%', description='Neutral Detergent Fiber'),
            Nutrient(name_en='ADF', name_zh='酸性洗涤纤维', unit='%', description='Acid Detergent Fiber'),
            Nutrient(name_en='Fat', name_zh='脂肪', unit='%', description='Fat content'),
            Nutrient(name_en='Calcium', name_zh='钙', unit='%', description='Calcium content'),
            Nutrient(name_en='Phosphorus', name_zh='磷', unit='%', description='Phosphorus content'),
            Nutrient(name_en='NEL', name_zh='奶牛净能', unit='Mcal/kg', description='Net Energy for Lactation')
        ]

        for nutrient in nutrients:
            db.session.add(nutrient)

        db.session.commit()
        print("Added basic nutrient definitions")

    # Knowledge base has been removed

    # Add NRC 8th edition model if it doesn't exist
    if not NrcModelVersion.query.first():
        print("Adding NRC 8th edition model...")
        nrc8_model = NrcModelVersion(
            name="NRC 8th Edition (NASEM 2021)",
            description="Nutrient Requirements of Dairy Cattle, 8th Revised Edition (NASEM, 2021)",
            publication_date=datetime(2021, 1, 1),  # Approximate date
            is_active=True
        )
        db.session.add(nrc8_model)
        db.session.commit()

        # Add coefficient groups
        print("Adding NRC coefficient groups...")
        groups = [
            {
                "name": "Dry Matter Intake",
                "description": "Coefficients for DMI prediction equations"
            },
            {
                "name": "Energy Requirements",
                "description": "Coefficients for energy requirement calculations"
            },
            {
                "name": "Protein Requirements",
                "description": "Coefficients for protein requirement calculations"
            },
            {
                "name": "Mineral Requirements",
                "description": "Coefficients for mineral requirement calculations"
            },
            {
                "name": "Fiber Requirements",
                "description": "Coefficients for fiber requirement calculations"
            }
        ]

        group_objects = {}
        for group in groups:
            group_obj = NrcCoefficientGroup(
                model_version_id=nrc8_model.id,
                name=group["name"],
                description=group["description"]
            )
            db.session.add(group_obj)
            db.session.flush()  # Get the group ID
            group_objects[group["name"]] = group_obj

        # Add coefficients for DMI prediction
        print("Adding NRC coefficients for DMI prediction...")
        dmi_coefficients = [
            {
                "group": "Dry Matter Intake",
                "name": "Multiparous Base Coefficient",
                "code": "dmi_multi_base",
                "value": 0.0968,
                "unit": "kg/kg^0.75",
                "description": "Base coefficient for multiparous cow DMI equation",
                "animal_type": "lactating"
            },
            {
                "group": "Dry Matter Intake",
                "name": "Milk Production Coefficient",
                "code": "dmi_milk_coef",
                "value": 0.372,
                "unit": "kg/kg milk",
                "description": "Coefficient for milk production in DMI equation",
                "animal_type": "lactating"
            },
            {
                "group": "Dry Matter Intake",
                "name": "Primiparous Factor",
                "code": "dmi_prim_factor",
                "value": 0.82,
                "unit": "dimensionless",
                "description": "Adjustment factor for primiparous cows",
                "animal_type": "lactating"
            },
            {
                "group": "Dry Matter Intake",
                "name": "Early Lactation Base",
                "code": "dmi_early_base",
                "value": 0.3,
                "unit": "dimensionless",
                "description": "Base coefficient for early lactation adjustment",
                "animal_type": "lactating"
            },
            {
                "group": "Dry Matter Intake",
                "name": "Early Lactation Exponent",
                "code": "dmi_early_exp",
                "value": -0.02,
                "unit": "1/day",
                "description": "Exponent for early lactation adjustment",
                "animal_type": "lactating"
            },
            {
                "group": "Dry Matter Intake",
                "name": "Heifer Weight Coefficient",
                "code": "dmi_heifer_weight_coef",
                "value": 0.0185,
                "unit": "kg/kg",
                "description": "Coefficient for body weight in heifer DMI equation",
                "animal_type": "heifer"
            },
            {
                "group": "Dry Matter Intake",
                "name": "Heifer Metabolic Weight Coefficient",
                "code": "dmi_heifer_mw_coef",
                "value": 0.2,
                "unit": "kg/kg^0.75",
                "description": "Coefficient for metabolic weight in heifer DMI equation",
                "animal_type": "heifer"
            }
        ]

        # Add coefficients for energy requirements
        print("Adding NRC coefficients for energy requirements...")
        energy_coefficients = [
            {
                "group": "Energy Requirements",
                "name": "Maintenance Coefficient",
                "code": "nel_maint_coef",
                "value": 0.086,
                "unit": "Mcal/kg^0.75",
                "description": "Coefficient for maintenance energy requirement",
                "animal_type": "lactating"
            },
            {
                "group": "Energy Requirements",
                "name": "Milk Fat Energy Coefficient",
                "code": "nel_milk_fat_coef",
                "value": 0.0929,
                "unit": "Mcal/kg",
                "description": "Energy value of milk fat",
                "animal_type": "lactating"
            },
            {
                "group": "Energy Requirements",
                "name": "Milk Protein Energy Coefficient",
                "code": "nel_milk_protein_coef",
                "value": 0.0563,
                "unit": "Mcal/kg",
                "description": "Energy value of milk protein",
                "animal_type": "lactating"
            },
            {
                "group": "Energy Requirements",
                "name": "Milk Lactose Energy Coefficient",
                "code": "nel_milk_lactose_coef",
                "value": 0.0395,
                "unit": "Mcal/kg",
                "description": "Energy value of milk lactose",
                "animal_type": "lactating"
            },
            {
                "group": "Energy Requirements",
                "name": "Pregnancy Coefficient",
                "code": "nel_preg_coef",
                "value": 0.00318,
                "unit": "Mcal/kg",
                "description": "Base coefficient for pregnancy energy requirement",
                "animal_type": "pregnant"
            },
            {
                "group": "Energy Requirements",
                "name": "Pregnancy Exponent",
                "code": "nel_preg_exp",
                "value": 0.0352,
                "unit": "1/day",
                "description": "Exponent for pregnancy energy requirement",
                "animal_type": "pregnant"
            }
        ]

        # Add coefficients for protein requirements
        print("Adding NRC coefficients for protein requirements...")
        protein_coefficients = [
            {
                "group": "Protein Requirements",
                "name": "Maintenance Protein Coefficient",
                "code": "mp_maint_coef",
                "value": 4.1,
                "unit": "g/kg^0.75",
                "description": "Coefficient for maintenance protein requirement",
                "animal_type": "lactating"
            },
            {
                "group": "Protein Requirements",
                "name": "Milk Protein Efficiency",
                "code": "mp_milk_efficiency",
                "value": 0.67,
                "unit": "dimensionless",
                "description": "Efficiency of MP use for milk protein synthesis",
                "animal_type": "lactating"
            },
            {
                "group": "Protein Requirements",
                "name": "Pregnancy Protein Base",
                "code": "mp_preg_base",
                "value": 0.69,
                "unit": "g/day",
                "description": "Base coefficient for pregnancy protein requirement",
                "animal_type": "pregnant"
            },
            {
                "group": "Protein Requirements",
                "name": "Pregnancy Protein Exponent",
                "code": "mp_preg_exp",
                "value": 0.0332,
                "unit": "1/day",
                "description": "Exponent for pregnancy protein requirement",
                "animal_type": "pregnant"
            }
        ]

        # Add coefficients for mineral requirements
        print("Adding NRC coefficients for mineral requirements...")
        mineral_coefficients = [
            {
                "group": "Mineral Requirements",
                "name": "Calcium Maintenance",
                "code": "ca_maint",
                "value": 0.031,
                "unit": "g/kg BW",
                "description": "Maintenance calcium requirement per kg body weight",
                "animal_type": "lactating"
            },
            {
                "group": "Mineral Requirements",
                "name": "Calcium Milk",
                "code": "ca_milk",
                "value": 1.22,
                "unit": "g/kg milk",
                "description": "Calcium requirement per kg milk",
                "animal_type": "lactating"
            },
            {
                "group": "Mineral Requirements",
                "name": "Phosphorus Maintenance",
                "code": "p_maint",
                "value": 0.016,
                "unit": "g/kg BW",
                "description": "Maintenance phosphorus requirement per kg body weight",
                "animal_type": "lactating"
            },
            {
                "group": "Mineral Requirements",
                "name": "Phosphorus Milk",
                "code": "p_milk",
                "value": 0.9,
                "unit": "g/kg milk",
                "description": "Phosphorus requirement per kg milk",
                "animal_type": "lactating"
            }
        ]

        # Add coefficients for fiber requirements
        print("Adding NRC coefficients for fiber requirements...")
        fiber_coefficients = [
            {
                "group": "Fiber Requirements",
                "name": "Minimum NDF",
                "code": "min_ndf",
                "value": 25.0,
                "unit": "% of DM",
                "description": "Minimum NDF requirement as percent of diet DM",
                "animal_type": "lactating"
            },
            {
                "group": "Fiber Requirements",
                "name": "Minimum forage NDF",
                "code": "min_forage_ndf",
                "value": 19.0,
                "unit": "% of DM",
                "description": "Minimum forage NDF as percent of diet DM",
                "animal_type": "lactating"
            },
            {
                "group": "Fiber Requirements",
                "name": "Maximum NFC",
                "code": "max_nfc",
                "value": 44.0,
                "unit": "% of DM",
                "description": "Maximum non-fiber carbohydrate as percent of diet DM",
                "animal_type": "lactating"
            }
        ]

        # Combine all coefficients
        all_coefficients = dmi_coefficients + energy_coefficients + protein_coefficients + mineral_coefficients + fiber_coefficients

        # Add all coefficients to the database
        for coef in all_coefficients:
            group_obj = group_objects[coef["group"]]
            coefficient = NrcCoefficient(
                group_id=group_obj.id,
                name=coef["name"],
                code=coef["code"],
                value=coef["value"],
                unit=coef["unit"],
                description=coef["description"],
                animal_type=coef["animal_type"]
            )
            db.session.add(coefficient)

        db.session.commit()
        print("NRC 8th edition model and coefficients added successfully!")

    print("Database setup complete!")