import React from 'react';
import { useTranslation } from 'react-i18next';

const LanguageSwitcher = () => {
  const { i18n, t } = useTranslation();

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    // Update HTML lang attribute
    document.documentElement.setAttribute('lang', lng);
    // Update document title
    document.getElementById('app-title').textContent = t('app.title');
  };

  return (
    <div className="flex items-center space-x-2">
      <button
        className={`px-2 py-1 text-sm rounded ${i18n.language === 'en' ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
        onClick={() => changeLanguage('en')}
      >
        {t('language.en')}
      </button>
      <button
        className={`px-2 py-1 text-sm rounded ${i18n.language === 'zh' ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
        onClick={() => changeLanguage('zh')}
      >
        {t('language.zh')}
      </button>
    </div>
  );
};

export default LanguageSwitcher;
