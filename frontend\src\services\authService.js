import api from './api'

// Register a new user
export const register = async (userData) => {
  const response = await api.post('/auth/register', userData)
  return response.data
}

// Login user
export const login = async (credentials) => {
  console.log('Attempting login with:', credentials.email);
  try {
    const response = await api.post('/auth/login', credentials);

    // Log the response for debugging
    console.log('Login response:', response.status, response.statusText);
    console.log('Response data contains tokens:',
      'access_token' in response.data,
      'refresh_token' in response.data);

    // Store tokens and user data
    const { access_token, refresh_token, user } = response.data;

    // Make sure the tokens are valid before storing
    if (!access_token || typeof access_token !== 'string') {
      console.error('Invalid access token received:', access_token);
      throw new Error('Invalid token received from server');
    }

    console.log('Login successful, storing tokens and user data');
    console.log('Token length:', access_token.length);

    // Store tokens clearly
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');

    localStorage.setItem('token', access_token);
    localStorage.setItem('refresh_token', refresh_token);
    localStorage.setItem('user', JSON.stringify(user));

    console.log('Tokens stored. Verifying:');
    console.log('Token in storage:', !!localStorage.getItem('token'));

    return response.data;
  } catch (error) {
    console.error('Login request failed:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.status, error.response.data);
    }
    throw error;
  }
}

// Logout user
export const logout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('refresh_token')
  localStorage.removeItem('user')
}

// Get current user
export const getCurrentUser = async () => {
  try {
    const response = await api.get('/auth/me')
    return response.data.user
  } catch (error) {
    return null
  }
}

// Change password
export const changePassword = async (passwordData) => {
  try {
    const response = await api.post('/auth/change-password', passwordData)
    return response.data
  } catch (error) {
    throw error
  }
}

// Refresh access token
export const refreshToken = async () => {
  const refreshToken = localStorage.getItem('refresh_token')

  if (!refreshToken) {
    throw new Error('No refresh token available')
  }

  try {
    const response = await api.post('/auth/refresh', {}, {
      headers: {
        Authorization: `Bearer ${refreshToken}`
      }
    })

    // Store new access token
    localStorage.setItem('token', response.data.access_token)

    return response.data.access_token
  } catch (error) {
    // Clear all data if refresh fails
    logout()
    throw error
  }
}

export const checkTokenExpiration = () => {
  const token = localStorage.getItem('token');
  if (!token) return false;

  try {
    // JWT tokens are in format: header.payload.signature
    const payload = token.split('.')[1];
    const decodedPayload = JSON.parse(atob(payload));

    // Check if token is about to expire (within next 5 minutes)
    const expiresIn = decodedPayload.exp * 1000 - Date.now();
    console.log('Token expires in:', Math.floor(expiresIn / 1000 / 60), 'minutes');

    return expiresIn > 0;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return false;
  }
}