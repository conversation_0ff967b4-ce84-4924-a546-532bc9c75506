import React, { useState, useMemo, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Input from '../../components/ui/Input'
import Pagination from '../../components/ui/Pagination'
import AnimalCard from '../../components/herds/AnimalCard'
import useDebounce from '../../hooks/useDebounce'
import { getHerds } from '../../services/herdService'

const HerdsListPage = () => {
  const { t } = useTranslation()
  const [searchInput, setSearchInput] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const ITEMS_PER_PAGE = 10
  const debouncedSearchQuery = useDebounce(searchInput, 300) // 300ms debounce

  // Use React Query with pagination
  const {
    data: herdsData = { herds: [], page: 1, totalPages: 0, totalItems: 0 },
    isLoading,
    error,
    refetch,
    isFetching
  } = useQuery(
    ['herds', currentPage, ITEMS_PER_PAGE],
    () => getHerds(currentPage, ITEMS_PER_PAGE),
    { keepPreviousData: true }
  )

  // Extract data for convenience
  const { herds = [], totalPages = 0, totalItems = 0 } = herdsData || {}

  // Set searching state when input changes but debounced value hasn't updated yet
  // This provides visual feedback during the debounce delay
  useEffect(() => {
    if (searchInput !== debouncedSearchQuery) {
      setIsSearching(true)
    } else {
      setIsSearching(false)
    }
  }, [searchInput, debouncedSearchQuery])

  // Filter herds based on debounced search query
  const filteredHerds = useMemo(() => {
    if (!debouncedSearchQuery.trim()) return herds

    const query = debouncedSearchQuery.toLowerCase().trim()

    // Create a single function for field checking to avoid repeated toLowerCase() calls
    const fieldIncludes = (field) => {
      return field && field.toLowerCase().includes(query)
    }

    return herds.filter(herd => {
      // Prioritize exact ear_num matches first (most common search)
      if (herd.ear_num === query) return true

      // Then check for partial matches in important fields
      return fieldIncludes(herd.ear_num) ||
             fieldIncludes(herd.name) ||
             fieldIncludes(herd.breed) ||
             fieldIncludes(herd.group_name)
    })
  }, [herds, debouncedSearchQuery])

  // Handle page change
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage)
    window.scrollTo(0, 0) // Scroll to top when changing pages

    // Reset search when changing pages
    if (searchInput) {
      setSearchInput('')
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('herds.title')}</h1>
        <Link to="/herds/new">
          <Button>{t('herds.addNew')}</Button>
        </Link>
      </div>

      <div className="mb-6 relative">
        <Input
          placeholder={t('herds.searchByEarNumber')}
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          className="w-full md:w-64 pr-8"
        />
        {searchInput && !isSearching && (
          <button
            onClick={() => setSearchInput('')}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            aria-label="Clear search"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
        {isSearching && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin h-4 w-4 border-2 border-green-500 border-t-transparent rounded-full" />
          </div>
        )}
      </div>

      {isLoading ? (
        <p className="text-center py-4">{t('common.loading')}</p>
      ) : error ? (
        <Card>
          <p className="text-red-500">{t('herds.errorLoading')}: {error.message}</p>
          <Button onClick={() => refetch()} className="mt-2" variant="outline">{t('common.tryAgain')}</Button>
        </Card>
      ) : herds.length === 0 ? (
        <Card>
          <div className="text-center py-4">
            <p className="text-gray-500 mb-4">{t('herds.noHerdsFoundLong')}</p>
            <Link to="/herds/new">
              <Button>{t('herds.addNew')}</Button>
            </Link>
          </div>
        </Card>
      ) : (
        <>
          {/* Results count - show filtered count when searching, total count otherwise */}
          <p className="mb-4 text-sm text-gray-500">
            {debouncedSearchQuery
              ? `${filteredHerds.length} ${t('herds.animalsFound')}`
              : `${totalItems} ${t('herds.animalsFound')}`
            }
          </p>

          {debouncedSearchQuery && filteredHerds.length === 0 ? (
            <Card className="text-center py-6">
              <p className="text-gray-500">{t('herds.noSearchResults')}</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => setSearchInput('')}
              >
                {t('common.reset')}
              </Button>
            </Card>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filteredHerds.map(herd => (
                  <AnimalCard key={herd.id} animal={herd} />
                ))}
              </div>

              {/* Only show pagination when not searching */}
              {!debouncedSearchQuery && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  onPageChange={handlePageChange}
                  isLoading={isFetching}
                />
              )}
            </>
          )}
        </>
      )}
    </div>
  )
}

export default HerdsListPage