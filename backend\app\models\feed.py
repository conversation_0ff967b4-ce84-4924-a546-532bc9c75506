from datetime import datetime
from ..extensions import db

class Nutrient(db.Model):
    __tablename__ = 'nutrients'
    __table_args__ = {'schema': 'ration_app'}
    
    id = db.Column(db.Integer, primary_key=True)
    name_en = db.Column(db.String(255), nullable=True)
    name_zh = db.Column(db.String(255))
    unit = db.Column(db.String(50), nullable=True)
    description = db.Column(db.Text)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name_en': self.name_en,
            'name_zh': self.name_zh,
            'unit': self.unit,
            'description': self.description
        }

class Feed(db.Model):
    __tablename__ = 'feeds'
    __table_args__ = {'schema': 'ration_app'}
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>Key('ration_app.users.id'))
    name = db.Column(db.String(255), nullable=True)
    description = db.Column(db.Text)
    dry_matter_percentage = db.Column(db.Float, nullable=True)
    cost_per_kg = db.Column(db.Float, nullable=True)
    is_public = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    import_status = db.Column(db.String(50), nullable=True, default='confirmed', index=True) 
    import_session_id = db.Column(db.String(36), index=True)
    
    # Relationships
    nutrients = db.relationship('FeedNutrient', backref='feed', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self, include_nutrients=False):
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'name': self.name,
            'description': self.description,
            'dry_matter_percentage': self.dry_matter_percentage,
            'cost_per_kg': self.cost_per_kg,
            'is_public': self.is_public,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'import_status': self.import_status,
            'import_session_id': self.import_session_id
        }
        
        if include_nutrients:
            data['nutrients'] = [n.to_dict() for n in self.nutrients]
            
        return data

class FeedNutrient(db.Model):
    __tablename__ = 'feed_nutrients'
    
    id = db.Column(db.Integer, primary_key=True)
    feed_id = db.Column(db.Integer, db.ForeignKey('ration_app.feeds.id', ondelete='CASCADE'), nullable=True)
    nutrient_id = db.Column(db.Integer, db.ForeignKey('ration_app.nutrients.id'), nullable=True)
    value = db.Column(db.Float, nullable=True)
    
    # Relationship with nutrient
    nutrient = db.relationship('Nutrient')
    
    # Unique constraint for feed_id and nutrient_id
    __table_args__ = (
        db.UniqueConstraint('feed_id', 'nutrient_id'),
        {'schema': 'ration_app'}
    )
    
    def to_dict(self):
        return {
            'id': self.id,
            'feed_id': self.feed_id,
            'nutrient_id': self.nutrient_id,
            'nutrient_name': self.nutrient.name_en if self.nutrient else None,
            'nutrient_unit': self.nutrient.unit if self.nutrient else None,
            'value': self.value
        }