/* taskpane.css - Styling for the Advanced Excel Template Analyzer */

/* General styles */
body {
    padding: 20px;
    max-width: 100%;
    overflow-x: hidden;
}

#container {
    max-width: 100%;
}

.section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eaeaea;
}

h2 {
    margin-bottom: 15px;
}

/* Input field styles */
.ms-TextField, .ms-SelectWrapper {
    margin-bottom: 15px;
    width: 100%;
}

.ms-TextField-field {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #d6d6d6;
}

.ms-SelectWrapper-select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #d6d6d6;
    margin-bottom: 10px;
}

.ms-TextField--multiline .ms-TextField-field {
    min-height: 80px;
}

.code-block {
    font-family: monospace;
    white-space: pre-wrap;
    background-color: #f8f8f8;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    max-height: 200px;
    overflow-y: auto;
}

/* Checkbox and radio styles */
.ms-CheckBox, .ms-RadioButton {
    margin-bottom: 10px;
    display: block;
}

.ms-CheckBox-field, .ms-RadioButton-field {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.ms-CheckBox-input, .ms-RadioButton-input {
    margin-right: 8px;
}

/* Button styles */
.ms-Button {
    margin-right: 10px;
    margin-bottom: 15px;
}

.ms-Button--primary {
    background-color: #0078d4;
    border-color: #0078d4;
    color: white;
}

.ms-Button--primary:hover {
    background-color: #106ebe;
}

.ms-Button--primary:disabled {
    background-color: #f3f2f1;
    border-color: #f3f2f1;
    color: #a19f9d;
}

/* Status messages */
.status-message {
    color: #107c10;
    font-weight: 600;
    min-height: 20px;
}

.error-message {
    color: #a4262c;
    font-weight: 600;
    min-height: 20px;
}

/* Template options section */
#template-options {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-bottom: 15px;
}

/* Preview styles */
.preview-container {
    margin-top: 15px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #eaeaea;
    padding: 10px;
    background-color: #fcfcfc;
    border-radius: 4px;
}

.data-cluster {
    margin-bottom: 20px;
}

.data-cluster h3 {
    font-size: 14px;
    margin-bottom: 10px;
    color: #333;
    font-weight: 600;
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    font-size: 12px;
}

.preview-table th {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    padding: 5px 8px;
    text-align: left;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 1;
}

.preview-table td {
    border: 1px solid #ddd;
    padding: 4px 8px;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.preview-table td.has-formula {
    background-color: #f0f7ff;
    font-style: italic;
}

.preview-table tr:hover td {
    background-color: #f5f9fc;
}

.preview-table tr:nth-child(even) {
    background-color: #fafafa;
}

.more-rows {
    background-color: #f5f5f5 !important;
    text-align: center;
    font-style: italic;
    color: #666;
    padding: 4px;
}

/* Previous analyses section */
.ms-List {
    margin-top: 10px;
}

.ms-ListItem {
    padding: 10px;
    border-bottom: 1px solid #eaeaea;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ms-ListItem-primaryText {
    font-weight: 600;
    margin-right: 10px;
}

.ms-ListItem-secondaryText {
    color: #666;
    font-size: 12px;
}

.ms-ListItem-actions {
    margin-left: auto;
}

/* Add these styles to your taskpane.css file */
.preview-controls {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.preview-controls button {
    margin-right: 16px;
}

.code-block {
    background-color: #f8f8f8;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    font-family: 'Courier New', monospace;
    overflow: auto;
    max-height: 400px;
    white-space: pre-wrap;
    word-break: break-all;
}

#raw-json-content {
    margin: 0;
    font-size: 12px;
    line-height: 1.4;
}

.ms-Toggle {
    margin-top: 8px;
}

.json-key {
    color: #881391;
}

.json-string {
    color: #1a8a14;
}

.json-number {
    color: #0000ff;
}

.json-boolean {
    color: #0000ff;
}

.json-null {
    color: #808080;
}

/* Review link section */
#review-link-section {
    background-color: #f0f7ff;
    border-radius: 6px;
    padding: 15px;
    border-left: 4px solid #0078d4;
    margin-top: 20px;
    margin-bottom: 20px;
}

.review-link-container {
    margin-top: 15px;
    text-align: center;
}

#review-page-link {
    display: inline-block;
    text-decoration: none;
    padding: 8px 16px;
    font-weight: 600;
    transition: all 0.2s ease;
}

#review-page-link:hover {
    background-color: #106ebe;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 480px) {
    body {
        padding: 10px;
    }

    .ms-Button {
        width: 100%;
        margin-right: 0;
    }
}