import React from 'react';
import { useTranslation } from 'react-i18next';
import Input from './Input';
import Select from './Select';
import Button from './Button';

export default function FilterBar({ search, published, onSearch, onPublished, onRefresh, loading }) {
  const { t } = useTranslation();

  return (
    <div className="mb-4 flex flex-wrap gap-4">
      <div className="w-full md:w-64">
        <Input
          placeholder={t('kb.searchArticles')}
          value={search}
          onChange={(e) => onSearch(e.target.value)}
        />
      </div>
      <Select
        value={published}
        onChange={(e) => onPublished(e.target.value)}
        options={[
          { value: 'all', label: t('kb.allArticles') },
          { value: 'published', label: t('kb.publishedOnly') },
          { value: 'draft', label: t('kb.draftsOnly') }
        ]}
      />
      <Button variant="outline" onClick={onRefresh} disabled={loading}>
        {t('common.refresh')}
      </Button>
    </div>
  );
}