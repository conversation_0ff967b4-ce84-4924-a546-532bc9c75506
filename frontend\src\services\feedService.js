import api from './api'

// Get all feeds with pagination
export const getFeeds = async (page = 1, limit = 10) => {
  try {
    const response = await api.get('/feeds', {
      params: { page, limit }
    })

    return {
      feeds: response.data.feeds || [],
      page: response.data.page || page,
      totalPages: response.data.totalPages || 1,
      totalItems: response.data.totalItems || 0
    }
  } catch (error) {
    console.error('Error fetching feeds:', error)
    throw error
  }
}

// Get feed by ID
export const getFeedById = async (id) => {
  const response = await api.get(`/feeds/${id}`)
  return response.data.feed
}

// Create new feed
export const createFeed = async (feedData) => {
  const response = await api.post('/feeds', feedData)
  return response.data
}

// Update feed
export const updateFeed = async (id, feedData) => {
  const response = await api.put(`/feeds/${id}`, feedData)
  return response.data
}

// Delete feed
export const deleteFeed = async (id) => {
  const response = await api.delete(`/feeds/${id}`)
  return response.data
}