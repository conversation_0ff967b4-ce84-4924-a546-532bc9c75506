import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../ui/Button';

const AIMenuButton = ({ onOpenAssistant, enabled = false, formData = {} }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [showInstructionModal, setShowInstructionModal] = useState(false);
  const [showValidationPopup, setShowValidationPopup] = useState(false);
  const buttonRef = useRef(null);
  const [instruction, setInstruction] = useState('');

  const validateBeforeFormulation = () => {
    // Only validate animal group selection
    if (!formData.animal_group_id) {
      // Show the validation popup instead of toast
      setShowValidationPopup(true);

      // Hide the popup after 3 seconds
      setTimeout(() => {
        setShowValidationPopup(false);
      }, 3000);

      return false;
    }

    return true;
  };

  const handleButtonClick = () => {
    // Validate before showing the instruction modal
    if (!validateBeforeFormulation()) {
      return;
    }

    // Show the instruction modal
    setShowInstructionModal(true);
  };

  const handleStartFormulation = () => {
    setShowInstructionModal(false);
    setIsLoading(true);

    // Use the provided instruction or a default message
    const message = instruction.trim()
      ? `Please formulate this ration with the following instructions: ${instruction}`
      : 'Please formulate this ration for me.';

    // Open the assistant with the formulation message
    onOpenAssistant(message);

    // Reset the instruction field for next time
    setInstruction('');
    setIsLoading(false);
  };

  const handleCancel = () => {
    setShowInstructionModal(false);
    setInstruction('');
  };

  // Only render the button if AI is enabled
  if (!enabled) {
    return null;
  }

  return (
    <div className="relative">
      <Button
        ref={buttonRef}
        variant="outline"
        size="sm"
        className="flex items-center space-x-1"
        onClick={handleButtonClick}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
        <span>{t('rations.startFormulation', 'Start Formulation')}</span>
      </Button>

      {/* Validation Popup */}
      {showValidationPopup && (
        <div className="absolute bottom-full left-0 mb-2 w-64 bg-orange-100 border border-orange-400 text-orange-700 px-4 py-3 rounded shadow-lg z-50">
          <div className="flex">
            <div className="py-1">
              <svg className="h-6 w-6 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div>
              <p className="font-bold">{t('validation.required', 'Required')}</p>
              <p className="text-sm">{t('rations.selectAnimalGroup', 'Please select an animal group first')}</p>
            </div>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-md">
          <div className="w-5 h-5 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* Instruction Modal */}
      {showInstructionModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
          onClick={() => handleCancel()} // Close when clicking outside
        >
          <div
            className="bg-white rounded-lg shadow-xl p-6 w-11/12 max-w-md"
            onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside
          >
            <h3 className="text-lg font-medium mb-4">
              {t('rations.additionalInstructions', 'Additional Instructions (Optional)')}
            </h3>

            <textarea
              className="w-full border border-gray-300 rounded-md p-2 mb-4 h-32 focus:border-green-500 focus:ring-green-500"
              placeholder={t('rations.instructionsPlaceholder', 'Enter any specific requirements or constraints for this formulation...')}
              value={instruction}
              onChange={(e) => setInstruction(e.target.value)}
              onKeyDown={(e) => {
                // Handle Enter key (with Shift for new line)
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleStartFormulation();
                }
                // Handle Escape key
                if (e.key === 'Escape') {
                  handleCancel();
                }
              }}
              autoFocus
            />

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
              >
                {t('common.cancel', 'Cancel')}
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={handleStartFormulation}
              >
                {t('rations.proceedToFormulation', 'Proceed to Formulation')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIMenuButton;