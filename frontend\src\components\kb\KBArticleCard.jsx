import React, { useState, useRef, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

import Card from '../../components/ui/Card'
import KBArticleContent from './KBArticleContent'

/**
 * KBArticleCard: click-to-expand preview card for KB articles.
 * - Click anywhere on the card content (except links) to toggle expansion.
 * - On toggle, scroll the card into center of viewport.
 * - Renders Markdown tables with styled HTML tables.
 */
const KBArticleCard = ({
  article,
  showScore = false,
  maxLines = 3,
  maxHeightRem = 48,
  expandable = true
}) => {
  const { t } = useTranslation()
  const [expanded, setExpanded] = useState(false)
  const cardRef = useRef(null)

  // Compute score badge
  const score =
    typeof showScore === 'number'
      ? showScore
      : article.score && Math.round(article.score * 100)

  const badgeColor = (s) => {
    if (!s) return 'bg-gray-100 text-gray-800'
    if (s >= 90) return 'bg-green-100 text-green-800'
    if (s >= 70) return 'bg-blue-100 text-blue-800'
    if (s >= 50) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  // Style for summary
  const lineHeightRem = 1.5
  const collapsedRem = maxLines * lineHeightRem + 0.5
  const contentStyle = expanded || !expandable
    ? { maxHeight: `${maxHeightRem}rem`, overflowY: 'auto' }
    : { maxHeight: `${collapsedRem}rem`, overflow: 'hidden' }

  // Scroll into view when expanded state changes
  useEffect(() => {
    if (cardRef.current) {
      cardRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }, [expanded])

  // Toggle expansion on inner click
  const handleInnerClick = (e) => {
    if (!expandable) return
    if (e.target.closest('a')) return
    setExpanded((p) => !p)
  }

  return (
    <Card className={`border hover:border-blue-400 transition-colors ${expandable ? 'cursor-pointer' : ''}`}
    >
      <div ref={cardRef} onClick={handleInnerClick}>
        {/* Header */}
        <div className="flex justify-between items-start gap-2">
          <Link
            to={`/kb/articles/${article.slug}`}
            onClick={(e) => e.stopPropagation()}
            className="text-lg md:text-xl font-semibold text-blue-600 hover:text-blue-800"
          >
            {article.title}
          </Link>
          {score && (
            <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${badgeColor(score)}`}>
              {score}% {t('kb.relevance')}
            </span>
          )}
        </div>

        {/* Summary/preview */}
        {article.summary && (
          <KBArticleContent
            content={article.summary}
            style={contentStyle}
            className="mt-2 mb-3"
            showFade={!expanded && expandable}
            isPreview={true}
          />
        )}

        {/* Meta */}
        <div className="flex flex-wrap items-center gap-2 text-xs text-gray-500 mt-2">
          {article.category_name && (
            <span className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded-full">
              {article.category_name}
            </span>
          )}
          {article.tags?.slice(0, 3).map((tag, i) => (
            <span key={i} className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">{tag}</span>
          ))}
          <span className="ml-auto">
            {new Date(article.created_at).toLocaleDateString()}
          </span>
        </div>
      </div>
    </Card>
  )
}

export default KBArticleCard