import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-hot-toast';
import Input from '../ui/Input';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { changePassword } from '../../services/authService';

const ChangePasswordModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal is opened/closed
  React.useEffect(() => {
    if (isOpen) {
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: ''
      });
      setFormErrors({});
    }
  }, [isOpen]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear field error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validate = () => {
    const errors = {};
    
    if (!formData.currentPassword) {
      errors.currentPassword = t('validation.passwordRequired');
    }
    
    if (!formData.newPassword) {
      errors.newPassword = t('validation.passwordRequired');
    } else if (formData.newPassword.length < 6) {
      errors.newPassword = t('validation.passwordLength');
    }
    
    if (!formData.confirmNewPassword) {
      errors.confirmNewPassword = t('validation.confirmPasswordRequired');
    } else if (formData.newPassword !== formData.confirmNewPassword) {
      errors.confirmNewPassword = t('validation.passwordsDoNotMatch');
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validate()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await changePassword({
        current_password: formData.currentPassword,
        new_password: formData.newPassword
      });
      
      toast.success(t('auth.passwordChanged'));
      onClose();
    } catch (error) {
      console.error('Error changing password:', error);
      
      // Handle specific error cases
      if (error.response && error.response.status === 401) {
        setFormErrors(prev => ({ 
          ...prev, 
          currentPassword: t('auth.currentPasswordIncorrect') 
        }));
      } else {
        toast.error(error.response?.data?.message || t('common.errorOccurred'));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="w-full max-w-md p-4">
        <Card title={t('auth.changePassword')}>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              id="currentPassword"
              name="currentPassword"
              type="password"
              label={t('auth.currentPassword')}
              value={formData.currentPassword}
              onChange={handleChange}
              error={formErrors.currentPassword}
              required
              autoComplete="current-password"
            />
            
            <Input
              id="newPassword"
              name="newPassword"
              type="password"
              label={t('auth.newPassword')}
              value={formData.newPassword}
              onChange={handleChange}
              error={formErrors.newPassword}
              required
              autoComplete="new-password"
            />
            
            <Input
              id="confirmNewPassword"
              name="confirmNewPassword"
              type="password"
              label={t('auth.confirmNewPassword')}
              value={formData.confirmNewPassword}
              onChange={handleChange}
              error={formErrors.confirmNewPassword}
              required
              autoComplete="new-password"
            />
            
            <div className="flex justify-end space-x-3 pt-2">
              <Button 
                variant="outline" 
                onClick={onClose}
                type="button"
              >
                {t('common.cancel')}
              </Button>
              <Button 
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? t('common.saving') : t('common.save')}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default ChangePasswordModal;
