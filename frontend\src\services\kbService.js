// src/services/kbService.js
import api from './api'

// Category endpoints
export const getCategories = async (includeCount = true) => {
  const response = await api.get(`/kb/categories?include_count=${includeCount}`)
  return response.data.categories
}

export const getCategoryBySlug = async (slug) => {
  const response = await api.get(`/kb/categories/${slug}`)
  return response.data.category
}

export const createCategory = async (categoryData) => {
  const response = await api.post('/kb/categories', categoryData)
  return response.data
}

export const updateCategory = async (categoryId, categoryData) => {
  const response = await api.put(`/kb/categories/${categoryId}`, categoryData)
  return response.data
}

export const deleteCategory = async (categoryId) => {
  const response = await api.delete(`/kb/categories/${categoryId}`)
  return response.data
}

// Article endpoints
export const getArticles = async (params = {}) => {
  const response = await api.get('/kb/articles', { params })
  return response.data
}

export const getArticleBySlug = async (slug, incrementView = false) => {
  const response = await api.get(`/kb/articles/${slug}?increment_view=${incrementView}`)
  return response.data.article
}

export const createArticle = async (articleData) => {
  const response = await api.post('/kb/articles', articleData)
  return response.data
}

export const updateArticle = async (articleId, articleData) => {
  const response = await api.put(`/kb/articles/${articleId}`, articleData)
  return response.data
}

export const deleteArticle = async (articleId) => {
  const response = await api.delete(`/kb/articles/${articleId}`)
  return response.data
}

export const getRelatedArticles = async (articleId, limit = 5) => {
  const response = await api.get(`/kb/articles/${articleId}/related?limit=${limit}`)
  return response.data.related_articles
}

export const recordFeedback = async (articleId, feedbackData) => {
  const response = await api.post(`/kb/articles/${articleId}/feedback`, feedbackData)
  return response.data
}

// Search and AI endpoints
export const searchArticles = async (query, limit = 10, mode = 'semantic') => {
  // Add search mode to the params
  const params = new URLSearchParams({
    q: query,
    limit: limit,
    mode: mode
  })

  const response = await api.get(`/kb/search?${params.toString()}`)
  return response.data.results
}

export const askQuestion = async (question, contextWindow = 3, locale = 'en') => {
  const response = await api.post('/kb/ask', {
    question,
    context_window: contextWindow,
    locale
  })
  return response.data
}

// Tag endpoints
export const getPopularTags = async (limit = 10) => {
  const response = await api.get(`/kb/tags?limit=${limit}`)
  return response.data.tags
}

// Stats endpoint
export const getKBStats = async () => {
  const response = await api.get('/kb/stats')
  return response.data.stats
}

// Embedding related endpoints
export const getEmbeddingStatus = async () => {
  const response = await api.get('/kb/admin/embeddings/status')
  return response.data.embedding_status
}

export const reprocessEmbeddings = async (limit = 50) => {
  const response = await api.post(`/kb/admin/embeddings/reprocess?limit=${limit}`)
  return response.data
}

export const reprocessCompletedEmbeddings = async (limit = 50, articleId = null, processAll = false) => {
  let url = `/kb/admin/embeddings/reprocess-completed?process_all=${processAll}`

  if (!processAll && limit) {
    url += `&limit=${limit}`
  }

  if (articleId) {
    url += `&article_id=${articleId}`
  }

  const response = await api.post(url)
  return response.data
}

export const regenerateEmbedding = async (articleId) => {
  const response = await api.post(`/kb/admin/embeddings/${articleId}/regenerate`)
  return response.data
}