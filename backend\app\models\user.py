from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from ..extensions import db

class User(db.Model):
    __tablename__ = 'users'
    __table_args__ = {'schema': 'ration_app'}
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    name = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    herds = db.relationship('Herd', backref='user', lazy=True)
    feeds = db.relationship('Feed', backref='user', lazy=True)
    rations = db.relationship('Ration', backref='user', lazy=True)
    
    def __init__(self, email, password, name=None):
        self.email = email
        self.password_hash = generate_password_hash(password)
        self.name = name
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'name': self.name,
            'created_at': self.created_at.isoformat()
        }