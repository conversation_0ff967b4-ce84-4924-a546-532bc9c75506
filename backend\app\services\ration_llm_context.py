# backend/app/services/ration_llm_context.py

from datetime import datetime, timezone
import logging
import json
from typing import Dict, List, Any, Optional

# Models are not strictly needed here unless used for type hinting
# or structuring data within methods like get_feeds_for_formulation.
# Database interaction is handled elsewhere.

logger = logging.getLogger(__name__)

class RationLLMContext:
    """
    Holds the state for an LLM-assisted ration formulation session FOR A SINGLE INTERACTION.
    In the stateless approach, this object is instantiated and populated
    by the service layer (e.g., RationAgentService) based on data received
    from the frontend with each request.
    It allows tools to modify this in-memory state during the request lifecycle.
    The final state is serialized and sent back to the frontend.
    """

    def __init__(self, user_id: int, ration_id: Optional[int] = None, locale: str = 'en'):
        """Initializes the context with user information."""
        self.user_id = user_id
        self.ration_id = ration_id
        self.locale = locale
        self.created_at = datetime.now(timezone.utc)
        self.last_updated = self.created_at

        # State attributes
        self.message_history = []
        self.current_formulation_state = None
        self.herd_info = None
        self.feed_data = []
        self.constraints = []
        self.formulation_result = None
        self.formulation_attempts = 0
        self.formulation_info =''

        # Track available feeds for suggestions (not just selected ones)
        self.available_feeds = []

        # Track iteration state
        self.is_formulation_complete = False
        self.completion_reason = None
        self.iterations = 0
        self.max_iterations = 20  # Prevent infinite loops
        self.auto_continue = False  # Flag for agentic mode

        logger.debug(f"RationLLMContext initialized for user {user_id}, ration_id: {ration_id}")

    def load_data_from_dict(self, data: Dict[str, Any]):
         """
         Populates the context object from a dictionary (context_data from request).
         Called by RationAgentService after __init__.
         """
         logger.debug(f"Populating context from dict. Received keys: {list(data.keys())}")
         # Assign attributes directly, ensuring type safety or defaults
         self.current_formulation_state = data.get('current_formulation_state')
         self.herd_info = data.get('herd_info')
         self.feed_data = data.get('feed_data', [])
         self.constraints = data.get('constraints', [])
         self.message_history = data.get('message_history', [])
         self.formulation_result = data.get('formulation_result')
         self.formulation_attempts = data.get('formulation_attempts', 0)
         self.locale = data.get('locale', self.locale)

         # Ensure feed_data and constraints are lists and potentially sanitize structure if needed
         if not isinstance(self.feed_data, list): self.feed_data = []
         if not isinstance(self.constraints, list): self.constraints = []
         if not isinstance(self.message_history, list): self.message_history = []

         # Load auto_continue flag for agentic mode
         self.auto_continue = data.get('auto_continue', False)
         logger.info(f"Auto-continue flag loaded from context data: {self.auto_continue}")

         # Update ration_id if it was passed in context_data
         # This allows the context to know its ID if the frontend knows it
         # (e.g., after creation or when editing an existing one)
         if 'ration_id' in data and data['ration_id'] is not None:
             self.ration_id = data['ration_id']

         self.last_updated = datetime.now(timezone.utc)
         logger.debug(f"Context populated from dict. Feeds: {len(self.feed_data)}, Constraints: {len(self.constraints)}, History: {len(self.message_history)}, Ration ID: {self.ration_id}")


    # --- Methods to Modify Context State (Called by Agent Service Tools) ---

    def add_message(self, role: str, content: str, function_call: Optional[Dict] = None, function_result: Optional[Dict] = None, is_thinking: bool = False):
        """Add a message to the conversation history."""
        message = {
            'role': role,
            'content': content,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'is_thinking': is_thinking
        }
        if function_call: message['function_call'] = function_call
        if function_result: message['function_result'] = function_result
        self.message_history.append(message)
        self.message_history = self.message_history[-20:] # Keep history trimmed
        self.last_updated = datetime.now(timezone.utc)
        logger.debug(f"Added message: role={role}, is_thinking={is_thinking}, history len={len(self.message_history)}")
        return self

    def load_available_feeds(self):
        """Load available feeds for the user to choose from."""
        try:
            from ..models import Feed
            from ..extensions import db

            # Get public feeds and user-specific feeds
            feeds = Feed.query.filter(
                (Feed.is_public == True) | (Feed.user_id == self.user_id)
            ).all()

            self.available_feeds = [feed.to_dict(include_nutrients=True) for feed in feeds]
            logger.info(f"Loaded {len(self.available_feeds)} available feeds for context")
            return True
        except Exception as e:
            logger.error(f"Error loading available feeds: {e}")
            return False

    def add_feed(self, feed_id: int, feed_detail: Dict, min_inclusion: float = 0, max_inclusion: float = 100, inclusion_kg: float = None) -> bool:
        """Adds a feed to the context."""
        if not isinstance(self.feed_data, list):
            self.feed_data = []

        # Check if feed already exists
        id_key = 'id' if any('id' in feed for feed in self.feed_data) else 'feed_id'
        for index, feed in enumerate(self.feed_data):
            if feed.get(id_key) == feed_id:
                new_feed_entry = {
                'id': feed_id,
                'feed_id': feed_id,
                'name': feed_detail.get('name', f"Feed_{feed_id}"),
                'min_inclusion_percentage': min_inclusion,
                'max_inclusion_percentage': max_inclusion,
                'inclusion_kg' : inclusion_kg,
                'actual_inclusion_percentage': None,
                'cost_contribution': None,
                'dry_matter_percentage': feed_detail.get('dry_matter_percentage', 0.9),
                'cost_per_kg': feed_detail.get('cost_per_kg', 0),
                'nutrients': feed_detail.get('nutrients', {})
                }

                self.feed_data[index] = new_feed_entry
                self.last_updated = datetime.now(timezone.utc)

                # Reset formulation state when feeds change
                self.is_formulation_complete = False
                self.formulation_result = None
                return True

        # Create feed entry with necessary fields for formulation
        new_feed_entry = {
            'id': feed_id,
            'feed_id': feed_id,
            'name': feed_detail.get('name', f"Feed_{feed_id}"),
            'min_inclusion_percentage': min_inclusion,
            'max_inclusion_percentage': max_inclusion,
            'inclusion_kg' : inclusion_kg,
            'actual_inclusion_percentage': None,
            'cost_contribution': None,
            'dry_matter_percentage': feed_detail.get('dry_matter_percentage', 0.9),
            'cost_per_kg': feed_detail.get('cost_per_kg', 0),
            'nutrients': feed_detail.get('nutrients', {})
        }

        self.feed_data.append(new_feed_entry)
        self.last_updated = datetime.now(timezone.utc)

        # Reset formulation state when feeds change
        self.is_formulation_complete = False
        self.formulation_result = None

        return True

    def remove_feed(self, feed_id: int) -> bool:
        """Remove a feed from the in-memory feed_data list."""
        if not isinstance(self.feed_data, list): return False
        original_count = len(self.feed_data)
        # Check both 'id' and 'feed_id' for compatibility
        self.feed_data = [feed for feed in self.feed_data if feed.get('id') != feed_id and feed.get('feed_id') != feed_id]
        new_count = len(self.feed_data)
        if new_count < original_count:
            if self.current_formulation_state: self.current_formulation_state['feeds_count'] = new_count
            self.last_updated = datetime.now(timezone.utc); logger.info(f"Removed feed ID {feed_id} from context feed_data."); return True
        else: logger.warning(f"Feed ID {feed_id} not found in context feed_data."); return False

    def update_constraint(self, constraint_data: Dict) -> bool:
        """Update or add a nutrient constraint in the in-memory list."""
        # (Implementation remains the same - operates on self.constraints)
        if not isinstance(self.constraints, list): self.constraints = []
        nutrient_name = constraint_data.get('nutrient_name')
        if not nutrient_name: logger.error("update_constraint requires nutrient_name."); return False
        try: min_value = float(constraint_data['min_value']) if constraint_data.get('min_value') is not None else None; max_value = float(constraint_data['max_value']) if constraint_data.get('max_value') is not None else None
        except: return {'status': 'error', 'message': "Min/max values must be numbers or null"}
        if min_value is not None and max_value is not None and min_value > max_value: return {'status': 'error', 'message': "Min > Max"}
        found = False
        for i, constraint in enumerate(self.constraints):
            if constraint.get('nutrient_name') == nutrient_name:
                self.constraints[i]['min_value'] = min_value; self.constraints[i]['max_value'] = max_value; self.constraints[i]['actual_value'] = None; found = True; logger.info(f"Updated constraint '{nutrient_name}' in context."); break
        if not found:
            self.constraints.append({ 'nutrient_name': nutrient_name, 'min_value': min_value, 'max_value': max_value, 'actual_value': None }); logger.info(f"Added new constraint '{nutrient_name}' to context.");
            if self.current_formulation_state: self.current_formulation_state['constraints_count'] = len(self.constraints)
        self.last_updated = datetime.now(timezone.utc); return True

    def update_formulation_result(self, result: Dict):
        """Update the formulation result stored in the context and update feed data with actual inclusion percentages."""
        self.formulation_result = result
        self.formulation_info = result
        self.formulation_attempts += 1
        self.last_updated = datetime.now(timezone.utc)
        logger.info(f"Updated formulation result in context. Status: {result.get('status')}")

        # Update feed_data with actual inclusion percentages and cost contributions from the result
        if result.get('status') == 'success' and result.get('feeds'):
            for feed in self.feed_data:
                feed_id = feed.get('id') or feed.get('feed_id')
                if feed_id and feed_id in result['feeds']:
                    # Update actual inclusion percentage from the formulation result
                    feed['actual_inclusion_percentage'] = result['feeds'][feed_id].get('inclusion_percentage')
                    # Update cost contribution from the formulation result
                    feed['cost_contribution'] = result['feeds'][feed_id].get('cost_contribution')
                    logger.info(f"Updated feed {feed_id} with actual inclusion: {feed['actual_inclusion_percentage']}%, cost: {feed['cost_contribution']}")

        # Update constraints with actual values from the result
        if result.get('status') == 'success' and result.get('nutrients'):
            for constraint in self.constraints:
                nutrient_name = constraint.get('nutrient_name')
                if nutrient_name and nutrient_name in result['nutrients']:
                    # Update actual value from the formulation result
                    constraint['actual_value'] = result['nutrients'][nutrient_name].get('actual_value')
                    logger.info(f"Updated constraint {nutrient_name} with actual value: {constraint['actual_value']}")

        # Add fail message
        if result.get('status') != 'success' and result.get('message'):
            self.formulation_info = result.get('message')

        # Log the formulation attempt in function_calls if it doesn't exist
        if not hasattr(self, 'function_calls'):
            self.function_calls = []

        # Add a function call entry for this formulation
        self.function_calls.append({
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'function': 'run_formulation',
            'args': {'optimization_objective': result.get('optimization_objective', 'minimize_cost')},
            'status': result.get('status', 'unknown'),
            'message': result.get('message', '')
        })

        logger.info(f"Added formulation result to function_calls. Total calls: {len(self.function_calls)}")

        return self

    # --- Methods to Retrieve Context Data ---

    def get_context_for_llm(self) -> Dict:
        """Build a rich context representation for the LLM prompt."""
        # Create feed summaries with name and key properties (not just IDs)
        feed_summary = []
        for feed in self.feed_data:
            feed_entry = {
                'name': feed.get('name', f"Feed_{feed.get('id')}"),
                'min_inclusion': feed.get('min_inclusion_percentage', 0),
                'max_inclusion': feed.get('max_inclusion_percentage', 100),
                'cost_per_kg': feed.get('cost_per_kg', 0)
            }

            nutrients = feed.get('nutrients', {})
            feed_entry['nutrients'] = nutrients

            feed_summary.append(feed_entry)

        # Format constraints with names (not just IDs)
        constraints_summary = []
        for constraint in self.constraints:
            constraints_summary.append({
                'nutrient': constraint.get('nutrient_name', 'Unknown'),
                'min': constraint.get('min_value'),
                'max': constraint.get('max_value')
            })

        # Simplified formulation result
        result_summary = None
        if self.formulation_result:
            result_summary = {
                'status': self.formulation_result.get('status'),
                'message': self.formulation_result.get('message'),
                'total_cost': self.formulation_result.get('actual_total_cost'),
            }
            if self.formulation_result.get('status') == 'failed':
                # Check both possible locations for conflicts
                conflicts = self.formulation_result.get('conflicts', []) or self.formulation_result.get('result', {}).get('conflicting_constraints', [])
                if conflicts:
                    result_summary['conflicts'] = conflicts

            # Add nutrients that didn't meet requirements
            if 'nutrients' in self.formulation_result:
                problem_nutrients = []
                for name, values in self.formulation_result['nutrients'].items():
                    min_val = values.get('min_value')
                    max_val = values.get('max_value')
                    actual = values.get('actual_value')

                    if ((min_val is not None and actual is not None and actual < min_val) or
                        (max_val is not None and actual is not None and actual > max_val)):
                        problem_nutrients.append({
                            'name': name,
                            'min': min_val,
                            'max': max_val,
                            'actual': actual
                        })

                if problem_nutrients:
                    result_summary['problem_nutrients'] = problem_nutrients



        # Add function call history summary
        function_call_summary = None
        if hasattr(self, 'function_calls') and self.function_calls:
            logger.info(f"Found {len(self.function_calls)} function calls in context")
            function_call_summary = [
                {
                    'function': call.get('function', ''),
                    'status': call.get('status', ''),
                    'timestamp': call.get('timestamp', ''),
                    'args': call.get('args', {})  # Include args for better context
                }
                for call in self.function_calls[-10:]  # Include last 10 function calls for better context
            ]

        # Build final context dictionary
        context_dict = {
            'ration_id': self.ration_id,
            'locale': self.locale,
            'is_new_ration': self.ration_id is None,
            'current_formulation_state': self.current_formulation_state,
            'herd_info': self.herd_info,
            'feeds': feed_summary,
            'constraints': constraints_summary,
            'formulation_result': result_summary,
            'formulation_attempts': self.formulation_attempts,
            'function_calls': function_call_summary,
            'message_history': self.message_history,
            'is_complete': self.is_formulation_complete,
            'completion_reason': self.completion_reason,
            'iterations': self.iterations,
            'available_feeds': self.available_feeds
        }

        return context_dict

    def _get_formulation_result_summary(self) -> Optional[Dict]:
        """Create a concise summary of formulation results."""
        # (Implementation remains the same)
        if not self.formulation_result: return None
        return { 'status': self.formulation_result.get('status'), 'message': self.formulation_result.get('message'), 'solver_status': self.formulation_result.get('solver_status'), 'total_cost': self.formulation_result.get('actual_total_cost'), 'feeds_count': len(self.formulation_result.get('feeds', {})), 'nutrients_count': len(self.formulation_result.get('nutrients', {})) }

    def get_feeds_for_formulation(self) -> List[Dict]:
        """ Returns feed data formatted for the formulation service from context data """
        feeds_for_service = []
        for feed_info in self.feed_data:
             # Ensure keys match what formulation service expects
             # The nutrients dict might need restructuring if it's not already {name: value}
             nutrients_dict = {}
             # Attempt to handle both list-of-dicts and dict-of-values structures
             raw_nutrients = feed_info.get('nutrients', {})
             if isinstance(raw_nutrients, list):
                 for nut in raw_nutrients:
                     if nut and isinstance(nut, dict):
                         name = nut.get('nutrient_name') or nut.get('name_en')
                         value = nut.get('value')
                         if name and value is not None:
                             nutrients_dict[name] = value
             elif isinstance(raw_nutrients, dict):
                 nutrients_dict = raw_nutrients # Assume it's already correct

             feeds_for_service.append({
                 'id': feed_info.get('id') or feed_info.get('feed_id'), # Use 'id' primarily
                 'name': feed_info.get('name'),
                 'min_incl_perc': feed_info.get('min_inclusion_percentage', 0),
                 'max_incl_perc': feed_info.get('max_inclusion_percentage', 100),
                 'dry_matter_percentage': feed_info.get('dry_matter_percentage', 0),
                 'cost_per_kg': feed_info.get('cost_per_kg', 0),
                 'nutrients': nutrients_dict
             })
        return feeds_for_service


    def get_feed_name(self, feed_id: int) -> Optional[str]:
         """ Utility to get a feed name from the current context data """
         for feed in self.feed_data:
             if feed.get('id') == feed_id or feed.get('feed_id') == feed_id:
                 return feed.get('name')
         return None

    # --- Serialization ---

    def add_function_call_log(self, function_name, args, result):
        """Add a log entry for a function call."""
        if not hasattr(self, 'function_calls'):
            self.function_calls = []
            logger.info(f"Initialized function_calls list for {function_name}")

        # Create the log entry
        log_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'function': function_name,
            'args': args,
            'status': result.get('status'),
            'message': result.get('message')
        }

        # Add to the list
        self.function_calls.append(log_entry)

        logger.info(f"Added function call log for {function_name}, status: {result.get('status')}, total calls: {len(self.function_calls)}")

        return self

    def to_dict(self) -> Dict:
        """Convert context object to a dictionary for API responses."""
        # Ensure all state needed by the frontend is included
        return {
            'user_id': self.user_id,
            'ration_id': self.ration_id,
            'locale': self.locale,
            'created_at': self.created_at.isoformat(),
            'last_updated': self.last_updated.isoformat(),
            'message_history': self.message_history,
            'current_formulation_state': self.current_formulation_state,
            'herd_info': self.herd_info,
            'feed_data': self.feed_data, # Include full feed data state
            'constraints': self.constraints, # Include full constraints state
            'formulation_result': self.formulation_result, # Include full result if present
            'formulation_attempts': self.formulation_attempts,
            'iterations': getattr(self, 'iterations', 0),
            'is_formulation_complete': getattr(self, 'is_formulation_complete', False),
            'completion_reason': getattr(self, 'completion_reason', None),
            'function_calls': getattr(self, 'function_calls', []),
            'auto_continue': getattr(self, 'auto_continue', False)
            # Add any other state attributes the frontend might need to restore session
        }

    def record_adjustment(self, adjustment_type, details):
        """Record an adjustment that was tried"""
        if not hasattr(self, 'adjustments_tried'):
            self.adjustments_tried = []

        self.adjustments_tried.append({
            'type': adjustment_type,
            'details': details,
            'timestamp': datetime.now(timezone.utc).isoformat()
        })