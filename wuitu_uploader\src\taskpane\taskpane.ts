
interface WorksheetAnalysisMetadata {
  sheetName: string;
  visibility: string;
  usedRange: string;
  rowCount: number;
  columnCount: number;
  formulaCount: number;
  clusters: Array<{
    id: number;
    range: string;
    width: number;
    height: number;
  }>;
}
interface CellData { r: number; c: number; rl: string; cl: string; v: any; f?: string; t?: string; }
interface DataCluster { id: number; range: string; data: CellData[][]; }
interface RawSheetPayload {
  sourceSheetName: string;
  usedRangeAddress: string;
  clusters: DataCluster[];
  worksheetAnalysis?: WorksheetAnalysisMetadata;
  // values?: any[][]; // Removed, no longer needed with cluster approach
}
interface LoginResponse { user: { id: number; email: string; name?: string; }; access_token: string; refresh_token?: string; }

// Updated AnalyzeResponse for the new endpoint
interface AnalyzeAndStageResponse {
  success: boolean;
  sessionId: string;
  recordType?: string; // The type of record identified (e.g., 'Feed', 'Herd')
  message?: string;
  reviewUrl?: string; // Relative URL like /excel-import-review/... or null
  error?: string; // Explicit error field
}

// --- Global Variables ---
let currentWorksheetAnalysis: WorksheetAnalysisMetadata | null = null;
let currentExtractedData: RawSheetPayload | null = null; // Store extracted data before sending
let authToken: string | null = null;
let loggedInUserEmail: string | null = null;

// --- Office Ready ---
Office.onReady((info) => {
  if (info.host === Office.HostType.Excel) {
    // Get UI elements
    const loginButton = document.getElementById("login-button") as HTMLButtonElement;
    const logoutButton = document.getElementById("logout-button") as HTMLButtonElement;
    const analyzeButton = document.getElementById("analyze-workbook") as HTMLButtonElement;
    const refreshButton = document.getElementById("get-sheet-names") as HTMLButtonElement;
    const hiddenCheckbox = document.getElementById("show-hidden-sheets") as HTMLInputElement;
    const sendDataButton = document.getElementById("send-data") as HTMLButtonElement; // Renamed button ID for clarity
    const sheetSelector = document.getElementById("sheet-selector") as HTMLSelectElement;
    const previewButton = document.getElementById("preview-data") as HTMLButtonElement;
    const rawJsonToggle = document.getElementById("raw-json-toggle") as HTMLInputElement;
    if (rawJsonToggle) {
      rawJsonToggle.onchange = () => {
        if (currentExtractedData) {
          previewData(); // Refresh the preview with the current toggle state
        }
      };
    }
    // Add event listeners
    if (loginButton) loginButton.onclick = handleLogin;
    if (logoutButton) logoutButton.onclick = handleLogout;
    if (analyzeButton) analyzeButton.onclick = analyzeWorkbook;
    if (refreshButton) refreshButton.onclick = populateSheetDropdown;
    if (hiddenCheckbox) hiddenCheckbox.onchange = populateSheetDropdown;
    if (sendDataButton) sendDataButton.onclick = analyzeAndStageData; // Changed action
    if (previewButton) previewButton.onclick = previewData; // Preview still uses local data first
    if (sheetSelector) sheetSelector.onchange = () => { // Reset state on sheet change
      if (analyzeButton) analyzeButton.disabled = !authToken || !sheetSelector.value;
      currentWorksheetAnalysis = null;
      currentExtractedData = null;
      const previewContainer = document.getElementById("data-preview-container");
      const workbookInfo = document.getElementById("workbook-info");
      if (previewContainer) previewContainer.style.display = "none";
      if (workbookInfo) workbookInfo.textContent = ""; // Clear analysis info
      const sendBtn = document.getElementById("send-data") as HTMLButtonElement;
      const previewBtn = document.getElementById("preview-data") as HTMLButtonElement;
      if (sendBtn) sendBtn.disabled = true;
      if (previewBtn) previewBtn.disabled = true;
      clearStatus();
    };

    // Initial UI setup
    updateLoginStateUI();
    populateSheetDropdown();
    showStatus("准备就绪。请选择一个工作表并点击“分析工作簿结构”。", false);
  }
});

// --- Authentication Functions (handleLogin, handleLogout, updateLoginStateUI - Remain the same) ---
async function handleLogin(): Promise<void> {
  clearStatus();
  const emailInput = document.getElementById("login-email") as HTMLInputElement;
  const passwordInput = document.getElementById("login-password") as HTMLInputElement;
  const serverUrlInput = document.getElementById("server-url") as HTMLInputElement;
  const email = emailInput?.value;
  const password = passwordInput?.value;
  const serverUrl = serverUrlInput?.value;

  if (!serverUrl || !email || !password) {
    showError("请输入服务器 URL、邮箱和密码。"); return;
  }
  showStatus("正在登录...", false);
  try {
    const response = await fetch(`${serverUrl}/api/auth/login`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    const data: LoginResponse | { message: string } = await response.json();
    if (!response.ok) throw new Error((data as { message: string }).message || `服务器错误: ${response.status}`);
    if ('access_token' in data) {
      authToken = data.access_token;
      loggedInUserEmail = data.user?.email || email;
      showStatus(`登录成功: ${loggedInUserEmail}`, false);
      updateLoginStateUI();
    } else throw new Error("登录响应中未找到 Token。");
  } catch (error) {
    console.error("登录失败:", error);
    showError(`登录失败: ${error instanceof Error ? error.message : String(error)}`);
    authToken = null; loggedInUserEmail = null; updateLoginStateUI();
  }
}

function handleLogout(): void {
  authToken = null; loggedInUserEmail = null;

  // Hide the review link section when logging out
  const reviewLinkSection = document.getElementById("review-link-section");
  if (reviewLinkSection) {
    reviewLinkSection.style.display = "none";
  }

  showStatus("已注销。", false); updateLoginStateUI();
}

function updateLoginStateUI(): void {
  const loginSection = document.getElementById("login-section");
  const logoutButton = document.getElementById("logout-button") as HTMLButtonElement;
  const loginStatus = document.getElementById("login-status");
  const analyzeButton = document.getElementById("analyze-workbook") as HTMLButtonElement;
  const sheetSelector = document.getElementById("sheet-selector") as HTMLSelectElement;
  const previewButton = document.getElementById("preview-data") as HTMLButtonElement;
  const sendDataButton = document.getElementById("send-data") as HTMLButtonElement;

  if (authToken && loginStatus) {
    loginStatus.textContent = `状态: 已登录为 ${loggedInUserEmail}`;
    if (loginSection) loginSection.style.display = 'none';
    if (logoutButton) logoutButton.style.display = 'inline-block';
    if (analyzeButton) analyzeButton.disabled = !sheetSelector?.value; // Enable Analyze if sheet selected
    // Buttons dependent on analysis completion
    if (previewButton) previewButton.disabled = !currentWorksheetAnalysis;
    if (sendDataButton) sendDataButton.disabled = !currentWorksheetAnalysis;
  } else if (loginStatus) {
    loginStatus.textContent = "状态: 未登录。";
    if (loginSection) loginSection.style.display = 'block';
    if (logoutButton) logoutButton.style.display = 'none';
    if (analyzeButton) analyzeButton.disabled = true;
    if (previewButton) previewButton.disabled = true;
    if (sendDataButton) sendDataButton.disabled = true;
  }
}

// --- Worksheet Functions (populateSheetDropdown, analyzeWorkbook - Remain the same) ---
async function populateSheetDropdown(): Promise<void> {
  try {
    await Excel.run(async (context) => {
      const sheets = context.workbook.worksheets;
      sheets.load("items/name, items/visibility");
      await context.sync();
      const dropdown = document.getElementById("sheet-selector") as HTMLSelectElement;
      const showHiddenCheckbox = document.getElementById("show-hidden-sheets") as HTMLInputElement;
      const showHidden = showHiddenCheckbox?.checked ?? false;
      if (!dropdown) return;
      const currentSelection = dropdown.value;
      dropdown.options.length = 0; // Clear existing options
      const placeholder = document.createElement("option");
      placeholder.value = ""; placeholder.text = "选择一个工作表"; placeholder.disabled = true; placeholder.selected = true;
      dropdown.add(placeholder);
      sheets.items.forEach(sheet => {
        const isVisible = sheet.visibility === Excel.SheetVisibility.visible;
        const isHidden = sheet.visibility === Excel.SheetVisibility.hidden;
        if (isVisible || (showHidden && isHidden)) {
          const option = document.createElement("option");
          option.value = sheet.name;
          option.text = sheet.name + (isHidden ? " (隐藏)" : "");
          dropdown.add(option);
        }
      });
      dropdown.value = currentSelection && dropdown.querySelector(`option[value="${currentSelection}"]`) ? currentSelection : "";
      const analyzeButton = document.getElementById("analyze-workbook") as HTMLButtonElement;
      if (analyzeButton) analyzeButton.disabled = !authToken || !dropdown.value; // Re-check auth token too
    });
    // showStatus("工作表已加载。请选择一个工作表。", false); // Avoid overriding later messages
  } catch (error) {
    showError(`加载工作表时出错: ${error instanceof Error ? error.message : String(error)}`);
  }
}

async function analyzeWorkbook(): Promise<void> {
  if (!authToken) { showError("请先登录。"); return; }
  const dropdown = document.getElementById("sheet-selector") as HTMLSelectElement;
  const selectedSheet = dropdown?.value;
  if (!selectedSheet) { showError("请先选择一个工作表。"); return; }

  showStatus(`正在分析工作表 "${selectedSheet}" 结构...`);
  currentExtractedData = null; // Reset extracted data on re-analysis
  const previewContainer = document.getElementById("data-preview-container");
  if (previewContainer) previewContainer.style.display = "none";

  try {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem(selectedSheet);
      const usedRange = sheet.getUsedRange(true);
      usedRange.load("address, rowCount, columnCount, values, formulas, rowIndex, columnIndex"); // Load formulas for count
      sheet.load("visibility, name");
      await context.sync();

      const usedRangeTopRow = usedRange.rowIndex;
      const usedRangeLeftCol = usedRange.columnIndex;
      const workbookInfo = document.getElementById("workbook-info") as HTMLDivElement;

      // Detect clusters based on values and merge overlapping ones
      const clusters = detectDataClusters(usedRange.values);
      const formattedClusters: Array<{ id: number; range: string; width: number; height: number; }> = [];
      let clusterInfo = "";

      if (clusters.length > 0) {
          clusterInfo = "\n检测到的表格区域:\n";
          clusters.forEach((cluster, index) => {
              const absoluteStartRow = cluster.startRow + usedRangeTopRow;
              const absoluteStartCol = cluster.startCol + usedRangeLeftCol;
              const absoluteEndRow = cluster.endRow + usedRangeTopRow;
              const absoluteEndCol = cluster.endCol + usedRangeLeftCol;
              const rangeAddress = `${columnIndexToLetter(absoluteStartCol + 1)}${absoluteStartRow + 1}:${columnIndexToLetter(absoluteEndCol + 1)}${absoluteEndRow + 1}`;
              const width = cluster.endCol - cluster.startCol + 1;
              const height = cluster.endRow - cluster.startRow + 1;
              clusterInfo += `  表格区域 ${index + 1}: ${rangeAddress} (${width} 列 × ${height} 行)\n`;
              formattedClusters.push({ id: index + 1, range: rangeAddress, width, height });
          });
      } else clusterInfo = "\n未检测到不同的表格区域。\n";

      // Add a note about merged clusters if applicable
      if (clusters.length > 0) {
          clusterInfo += "\n注意: 重叠的表格区域已合并为单个区域。\n";
      }


      // Count formulas efficiently
      let formulaCount = 0;
      if (usedRange.formulas) { // Check if formulas were loaded
          usedRange.formulas.forEach(row => {
              row.forEach(cellFormula => {
                  if (typeof cellFormula === 'string' && cellFormula.startsWith('=')) {
                      formulaCount++;
                  }
              });
          });
      }
      const formulaInfo = formulaCount > 0 ? `\n公式: ${formulaCount} 个单元格包含公式` : "\n公式: 未检测到";


      currentWorksheetAnalysis = {
        sheetName: sheet.name,
        visibility: sheet.visibility === Excel.SheetVisibility.visible ? "可见" : "隐藏",
        usedRange: usedRange.address,
        rowCount: usedRange.rowCount, columnCount: usedRange.columnCount,
        formulaCount: formulaCount, clusters: formattedClusters
      };

      if (workbookInfo) {
        workbookInfo.textContent = `工作表: ${sheet.name}\n可见性: ${currentWorksheetAnalysis.visibility}\n使用范围: ${usedRange.address}\n行数: ${usedRange.rowCount}\n列数: ${usedRange.columnCount}${formulaInfo}${clusterInfo}`;
      }

      // Enable next step buttons
      const sendDataButton = document.getElementById("send-data") as HTMLButtonElement;
      const previewButton = document.getElementById("preview-data") as HTMLButtonElement;
      if (sendDataButton) sendDataButton.disabled = false;
      if (previewButton) previewButton.disabled = false;

      showStatus("分析完成。点击“预览数据”或“分析并暂存数据”。", false);
    });
  } catch (error) {
    showError(`分析工作簿时出错: ${error instanceof Error ? error.message : String(error)}`);
    currentWorksheetAnalysis = null; // Reset on error
    const sendDataButton = document.getElementById("send-data") as HTMLButtonElement;
    const previewButton = document.getElementById("preview-data") as HTMLButtonElement;
    if (sendDataButton) sendDataButton.disabled = true;
    if (previewButton) previewButton.disabled = true;
  }
}


// --- Data Extraction and Processing Functions (extractRawSheetData, previewData - Remain mostly the same) ---
async function extractRawSheetData(sheetName: string): Promise<RawSheetPayload | null> {
    try {
        const payload = await Excel.run(async (context) => {
            const sheet = context.workbook.worksheets.getItem(sheetName);
            // Load necessary properties including valueTypes
            const usedRange = sheet.getUsedRange(true);
            usedRange.load("address, values, columnCount, rowCount, formulas, rowIndex, columnIndex, valueTypes");
            await context.sync();

            const usedRangeTopRow = usedRange.rowIndex;
            const usedRangeLeftCol = usedRange.columnIndex;

            // Detect clusters or use whole range
            // This will automatically merge overlapping clusters
            let clusterRegions = detectDataClusters(usedRange.values);
            if (clusterRegions.length === 0 && usedRange.rowCount > 0 && usedRange.columnCount > 0) {
                clusterRegions.push({ startRow: 0, startCol: 0, endRow: usedRange.rowCount - 1, endCol: usedRange.columnCount - 1 });
            }

            const dataClusterArray: DataCluster[] = [];

            for (let i = 0; i < clusterRegions.length; i++) {
                const cluster = clusterRegions[i];
                const startRow = cluster.startRow + usedRangeTopRow;
                const startCol = cluster.startCol + usedRangeLeftCol;
                const endRow = cluster.endRow + usedRangeTopRow;
                const endCol = cluster.endCol + usedRangeLeftCol;
                const rangeAddress = `${columnIndexToLetter(startCol + 1)}${startRow + 1}:${columnIndexToLetter(endCol + 1)}${endRow + 1}`;
                const clusterData: CellData[][] = [];

                for (let r = startRow; r <= endRow; r++) {
                    const rowData: CellData[] = [];
                    const rel_r = r - usedRangeTopRow; // Relative row index for accessing arrays

                    if (rel_r < 0 || rel_r >= usedRange.rowCount) continue; // Skip if relative row is out of bounds

                    for (let c = startCol; c <= endCol; c++) {
                        const rel_c = c - usedRangeLeftCol; // Relative col index

                        if (rel_c < 0 || rel_c >= usedRange.columnCount) continue; // Skip if relative col is out of bounds

                        // Safe access to arrays using relative indices
                        const value = usedRange.values?.[rel_r]?.[rel_c];
                        const formula = usedRange.formulas?.[rel_r]?.[rel_c];
                        const valueTypeExcel = usedRange.valueTypes?.[rel_r]?.[rel_c];

                        // Determine simplified type
                        let simpleType: string;
                        switch (valueTypeExcel) {
                            case Excel.RangeValueType.boolean: simpleType = "boolean"; break;
                            case Excel.RangeValueType.double: simpleType = "number"; break;
                            case Excel.RangeValueType.empty: simpleType = "empty"; break;
                            case Excel.RangeValueType.error: simpleType = "error"; break;
                            case Excel.RangeValueType.integer: simpleType = "number"; break; // Treat integer as number
                            case Excel.RangeValueType.string: case Excel.RangeValueType.richValue: simpleType = "string"; break;
                            default: simpleType = "unknown";
                        }

                        const cellData: CellData = {
                            r: r, c: c, rl: (r + 1).toString(), cl: columnIndexToLetter(c + 1),
                            v: value, t: simpleType
                        };
                        if (typeof formula === 'string' && formula.startsWith('=')) {
                            cellData.f = formula;
                        }
                        rowData.push(cellData);
                    }
                    if (rowData.length > 0) clusterData.push(rowData);
                }
                if (clusterData.length > 0) {
                    dataClusterArray.push({ id: i + 1, range: rangeAddress, data: clusterData });
                }
            }

            const basePayload: RawSheetPayload = {
                sourceSheetName: sheetName,
                usedRangeAddress: usedRange.address,
                clusters: dataClusterArray
            };
            // Add analysis metadata if it was generated
            if (currentWorksheetAnalysis && currentWorksheetAnalysis.sheetName === sheetName) {
                 // Update cluster dimensions based on actual extracted data
                 currentWorksheetAnalysis.clusters = dataClusterArray.map(c => ({
                    id: c.id, range: c.range,
                    width: c.data[0]?.length ?? 0, height: c.data.length
                 }));
                basePayload.worksheetAnalysis = currentWorksheetAnalysis;
            }
            return basePayload;
        });
        return payload;
    } catch (error) {
        console.error(`从工作表 "${sheetName}" 提取数据时出错:`, error);
        if (error instanceof OfficeExtension.Error) console.error("调试信息: " + JSON.stringify(error.debugInfo));
        showError(`提取数据时出错: ${error.message || String(error)}`); // Show error to user
        return null; // Return null on extraction error
    }
}

async function previewData(): Promise<void> {
  const dropdown = document.getElementById("sheet-selector") as HTMLSelectElement;
  const selectedSheet = dropdown?.value;
  if (!selectedSheet) { showError("请先选择一个工作表。"); return; }

  showStatus(`正在为工作表 "${selectedSheet}" 创建预览...`);

  try {
    // Extract data if not already done or if sheet changed
    if (!currentExtractedData || currentExtractedData.sourceSheetName !== selectedSheet) {
      currentExtractedData = await extractRawSheetData(selectedSheet);
      if (!currentExtractedData) return; // Error handled in extractRawSheetData
    }

    const previewContainer = document.getElementById("data-preview-container");
    const rawJsonContainer = document.getElementById("raw-json-container");
    const rawJsonContent = document.getElementById("raw-json-content");
    const rawJsonToggle = document.getElementById("raw-json-toggle") as HTMLInputElement;

    if (!previewContainer || !rawJsonContainer || !rawJsonContent) {
      throw new Error("在 DOM 中未找到预览容器。");
    }

    // Check which view mode is selected
    const showRawJson = rawJsonToggle?.checked || false;

    // Update display based on toggle state
    previewContainer.style.display = showRawJson ? "none" : "block";
    rawJsonContainer.style.display = showRawJson ? "block" : "none";

    // If showing raw JSON, format and display it
    if (showRawJson) {
      const formattedJson = formatJsonWithHighlighting(JSON.stringify(currentExtractedData, null, 2));
      rawJsonContent.innerHTML = formattedJson;
    } else {
      // Clear previous preview for the visual mode
      previewContainer.innerHTML = "";

      // Create preview tables for each cluster (same as before)
      if (currentExtractedData.clusters && currentExtractedData.clusters.length > 0) {
        currentExtractedData.clusters.forEach((cluster, index) => {
          try {
            const clusterDiv = document.createElement("div");
            clusterDiv.className = "data-cluster";
            clusterDiv.innerHTML = `<h3>表格区域 ${cluster.id} (${cluster.range})</h3>`;

            const table = document.createElement("table");
            table.className = "preview-table";
            const thead = document.createElement("thead");
            const headerRow = document.createElement("tr");

            // Create headers based on first row of cluster data
            cluster.data[0]?.forEach(cell => {
              const th = document.createElement("th");
              th.textContent = cell.cl;
              headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
            table.appendChild(thead);

            const tbody = document.createElement("tbody");
            const maxPreviewRows = Math.min(cluster.data.length, 10); // Limit preview rows
            for (let i = 0; i < maxPreviewRows; i++) {
              const row = document.createElement("tr");
              cluster.data[i]?.forEach(cell => {
                const td = document.createElement("td");
                td.textContent = cell.v !== null && cell.v !== undefined ? String(cell.v) : '';
                let tooltip = `${cell.cl}${cell.rl}`;
                if (cell.f) {
                  tooltip += ` = ${cell.f}`;
                  td.classList.add("has-formula");
                }
                if (cell.t) {
                  tooltip += ` [${cell.t}]`;
                }
                td.title = tooltip;
                row.appendChild(td);
              });
              tbody.appendChild(row);
            }

            // Add 'more rows' indicator if needed
            if (cluster.data.length > maxPreviewRows) {
              const moreRow = document.createElement("tr");
              const moreCell = document.createElement("td");
              moreCell.colSpan = cluster.data[0]?.length || 1;
              moreCell.className = "more-rows";
              moreCell.textContent = `... 还有 ${cluster.data.length - maxPreviewRows} 行`;
              moreRow.appendChild(moreCell);
              tbody.appendChild(moreRow);
            }

            table.appendChild(tbody);
            clusterDiv.appendChild(table);
            previewContainer.appendChild(clusterDiv);
          } catch (clusterError) {
            console.error(`渲染表格区域 ${index} 时出错:`, clusterError);
          }
        });
      } else {
        previewContainer.innerHTML = "<p>在此工作表中未找到数据区域。</p>";
      }
    }

    // Display both containers
    previewContainer.style.display = showRawJson ? "none" : "block";
    rawJsonContainer.style.display = showRawJson ? "block" : "none";

    showStatus(`已为工作表 "${selectedSheet}" 创建预览。点击"分析并暂存数据"发送到服务器。`, false);
  } catch (error) {
    console.error("预览数据时出错:", error);
    showError(`创建预览时出错: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// --- *** NEW: Analyze and Stage Data Function *** ---
async function analyzeAndStageData(): Promise<void> {
  if (!authToken) { showError("请先登录。"); return; }
  if (!currentWorksheetAnalysis) { showError("请先分析工作表结构。"); return; }

  const dropdown = document.getElementById("sheet-selector") as HTMLSelectElement;
  const selectedSheet = dropdown?.value;
  if (!selectedSheet) { showError("请先选择工作表。"); return; }

  const serverUrlInput = document.getElementById("server-url") as HTMLInputElement;
  const serverUrl = serverUrlInput?.value;
  if (!serverUrl) { showError("请输入服务器 URL。"); return; }

  // Define the base URL for your frontend application
  const frontendBaseUrl = "http://localhost:5173"; // CHANGE THIS if your React app runs elsewhere

  // Hide the review link section when starting a new analysis
  const reviewLinkSection = document.getElementById("review-link-section");
  if (reviewLinkSection) {
    reviewLinkSection.style.display = "none";
  }

  showStatus(`正在从工作表 "${selectedSheet}" 提取数据...`, false);

  try {
    // 1. Ensure data is extracted (might have been done by preview)
    if (!currentExtractedData || currentExtractedData.sourceSheetName !== selectedSheet) {
      currentExtractedData = await extractRawSheetData(selectedSheet);
      if (!currentExtractedData) {
         // Error already shown by extractRawSheetData
         return;
      }
    }

    showStatus("正在发送数据进行分析和暂存...", false);

    // 2. Prepare headers for the API call
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}` // Add the auth token
    };

    // 3. Call the NEW backend endpoint
    const response = await fetch(`${serverUrl}/api/analyze_and_stage`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(currentExtractedData) // Send the full extracted data payload
    });

    // 4. Process the response
    const responseData: AnalyzeAndStageResponse = await response.json();

    if (!response.ok || !responseData.success) {
       // Handle backend errors (e.g., staging failure, validation error)
       throw new Error(responseData.message || `服务器错误: ${response.status}`);
    }

    // --- Success ---
    console.log("分析和暂存成功:", responseData);

    if (responseData.reviewUrl) {
      // Construct the full frontend URL
      const fullReviewUrl = frontendBaseUrl + responseData.reviewUrl; // Assumes reviewUrl is relative like /path/:id?recordType=Type

      // Update the review link section
      const reviewLinkSection = document.getElementById("review-link-section");
      const reviewPageLink = document.getElementById("review-page-link") as HTMLAnchorElement;

      if (reviewLinkSection && reviewPageLink) {
        // Set the link URL and show the section
        reviewPageLink.href = fullReviewUrl;
        reviewLinkSection.style.display = "block";
      }

      // Open in new tab automatically (keep existing behavior)
      window.open(fullReviewUrl, '_blank');

      showStatus(`数据已暂存。请检查新浏览器选项卡进行审核或使用下方链接。消息: ${responseData.message || ''}`, false);
    } else {
       // Handle case where staging was successful but no review URL (e.g., no data found)
       // Hide the review link section if it was previously shown
       const reviewLinkSection = document.getElementById("review-link-section");
       if (reviewLinkSection) {
         reviewLinkSection.style.display = "none";
       }

       showStatus(`数据处理完成。消息: ${responseData.message || 'No records staged.'}`, false);
    }

  } catch (error) {
    console.error("分析或暂存数据时出错:", error);

    // Hide the review link section if there's an error
    const reviewLinkSection = document.getElementById("review-link-section");
    if (reviewLinkSection) {
      reviewLinkSection.style.display = "none";
    }

    showError(`错误: ${error instanceof Error ? error.message : String(error)}`);
  }
}


// --- Helper Functions (columnIndexToLetter, detectDataClusters, expandCluster - Remain the same) ---
function columnIndexToLetter(column: number): string { /* ... same code ... */
    let temp: number; let letter = '';
    while (column > 0) { temp = (column - 1) % 26; letter = String.fromCharCode(temp + 65) + letter; column = Math.floor((column - temp - 1) / 26); }
    return letter;
}
function detectDataClusters(data: any[][]): Array<{startRow: number, startCol: number, endRow: number, endCol: number}> {
    if (!data || data.length === 0 || !Array.isArray(data[0])) return [];

    const rows = data.length;
    const cols = data[0].length;
    const densityMap: boolean[][] = [];

    // Create density map of non-empty cells
    for (let r = 0; r < rows; r++) {
        densityMap[r] = [];
        for (let c = 0; c < cols; c++) {
            densityMap[r][c] = !!data[r]?.[c] && data[r][c].toString().trim() !== '';
        }
    }

    // Initialize visited cells tracking
    const visited: boolean[][] = Array(rows).fill(false).map(() => Array(cols).fill(false));

    // Initial clusters detection
    const initialClusters: Array<{startRow: number, startCol: number, endRow: number, endCol: number}> = [];

    // Find all initial clusters
    for (let r = 0; r < rows; r++) {
        for (let c = 0; c < cols; c++) {
            if (densityMap[r][c] && !visited[r][c]) {
                const cluster = expandCluster(r, c, densityMap, visited);
                const width = cluster.endCol - cluster.startCol + 1;
                const height = cluster.endRow - cluster.startRow + 1;

                // Only add clusters that meet minimum size criteria
                if ((width >= 2 && height >= 2) || (width * height >= 5)) {
                    initialClusters.push(cluster);
                }
            }
        }
    }

    // If no clusters or only one cluster, return as is
    if (initialClusters.length <= 1) {
        return initialClusters;
    }

    // Merge overlapping clusters
    return mergeClusters(initialClusters);
}
function expandCluster(startRow: number, startCol: number, densityMap: boolean[][], visited: boolean[][]): {startRow: number, startCol: number, endRow: number, endCol: number} {
    const rows = densityMap.length;
    const cols = densityMap[0].length;
    let minRow = startRow, minCol = startCol, maxRow = startRow, maxCol = startCol;

    // Use a stack for flood-fill algorithm
    const stack: Array<[number, number]> = [[startRow, startCol]];
    visited[startRow][startCol] = true; // Mark start as visited

    while (stack.length > 0) {
        const [r, c] = stack.pop()!;
        minRow = Math.min(minRow, r);
        minCol = Math.min(minCol, c);
        maxRow = Math.max(maxRow, r);
        maxCol = Math.max(maxCol, c);

        // Check 4 neighbors (up, down, left, right)
        const neighbors: Array<[number, number]> = [[r+1, c], [r-1, c], [r, c+1], [r, c-1]];
        for (const [nr, nc] of neighbors) {
            if (nr >= 0 && nr < rows && nc >= 0 && nc < cols && densityMap[nr][nc] && !visited[nr][nc]) {
                visited[nr][nc] = true;
                stack.push([nr, nc]);
            }
        }
    }

    return { startRow: minRow, startCol: minCol, endRow: maxRow, endCol: maxCol };
}

/**
 * Checks if two clusters overlap or if one contains the other
 */
function clustersOverlap(cluster1: {startRow: number, startCol: number, endRow: number, endCol: number},
                         cluster2: {startRow: number, startCol: number, endRow: number, endCol: number}): boolean {
    // Check if there's no overlap (one is completely to the right/left/above/below the other)
    if (cluster1.endRow < cluster2.startRow || cluster1.startRow > cluster2.endRow ||
        cluster1.endCol < cluster2.startCol || cluster1.startCol > cluster2.endCol) {
        return false;
    }

    // If we get here, there is some overlap
    return true;
}

/**
 * Checks if cluster1 contains cluster2
 */
function clusterContains(cluster1: {startRow: number, startCol: number, endRow: number, endCol: number},
                        cluster2: {startRow: number, startCol: number, endRow: number, endCol: number}): boolean {
    return cluster1.startRow <= cluster2.startRow && cluster1.endRow >= cluster2.endRow &&
           cluster1.startCol <= cluster2.startCol && cluster1.endCol >= cluster2.endCol;
}

/**
 * Merges two clusters into one that encompasses both
 */
function mergeClusterPair(cluster1: {startRow: number, startCol: number, endRow: number, endCol: number},
                         cluster2: {startRow: number, startCol: number, endRow: number, endCol: number}): {startRow: number, startCol: number, endRow: number, endCol: number} {
    return {
        startRow: Math.min(cluster1.startRow, cluster2.startRow),
        startCol: Math.min(cluster1.startCol, cluster2.startCol),
        endRow: Math.max(cluster1.endRow, cluster2.endRow),
        endCol: Math.max(cluster1.endCol, cluster2.endCol)
    };
}

/**
 * Merges overlapping clusters into larger clusters
 */
function mergeClusters(clusters: Array<{startRow: number, startCol: number, endRow: number, endCol: number}>): Array<{startRow: number, startCol: number, endRow: number, endCol: number}> {
    if (clusters.length <= 1) {
        return clusters;
    }

    // Sort clusters by size (area) in descending order
    // This helps prioritize larger clusters when merging
    clusters.sort((a, b) => {
        const areaA = (a.endRow - a.startRow + 1) * (a.endCol - a.startCol + 1);
        const areaB = (b.endRow - b.startRow + 1) * (b.endCol - b.startCol + 1);
        return areaB - areaA; // Descending order
    });

    // Keep track of which clusters have been merged
    const merged = new Array(clusters.length).fill(false);
    const result: Array<{startRow: number, startCol: number, endRow: number, endCol: number}> = [];

    // For each cluster, check if it overlaps with or contains any other cluster
    for (let i = 0; i < clusters.length; i++) {
        if (merged[i]) continue; // Skip if already merged

        let currentCluster = {...clusters[i]};
        merged[i] = true;

        let madeChanges = true;
        while (madeChanges) {
            madeChanges = false;

            for (let j = 0; j < clusters.length; j++) {
                if (merged[j]) continue; // Skip if already merged

                // Check if the current merged cluster overlaps with or contains this cluster
                if (clustersOverlap(currentCluster, clusters[j]) ||
                    clusterContains(currentCluster, clusters[j]) ||
                    clusterContains(clusters[j], currentCluster)) {

                    // Merge the clusters
                    currentCluster = mergeClusterPair(currentCluster, clusters[j]);
                    merged[j] = true;
                    madeChanges = true;
                }
            }
        }

        result.push(currentCluster);
    }

    return result;
}

// --- UI Update Functions (showStatus, showError, clearStatus - Remain the same) ---
function showStatus(message: string, isError: boolean = false): void {
    const statusElement = document.getElementById("status-info") as HTMLParagraphElement;
    const errorElement = document.getElementById("error-info") as HTMLParagraphElement;
    if (!isError && statusElement) statusElement.textContent = message;
    if (isError && errorElement) errorElement.textContent = message;
    if (isError && statusElement) statusElement.textContent = ""; // Clear status on error
    if (!isError && errorElement) errorElement.textContent = ""; // Clear error on success/status
    if (isError) console.error("Add-in Error:", message); else console.log("Add-in Status:", message);
}
function showError(message: string): void { showStatus(message, true); }
function clearStatus(): void { showStatus("", false); showStatus("", true); }

function formatJsonWithHighlighting(json: string): string {
  // Escape HTML to prevent XSS
  const escaped = json.replace(/&/g, '&amp;')
                     .replace(/</g, '&lt;')
                     .replace(/>/g, '&gt;');

  // Add syntax highlighting with regex
  return escaped
      .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
          function(match) {
              let cls = 'json-number';
              if (/^"/.test(match)) {
                  if (/:$/.test(match)) {
                      cls = 'json-key';
                      // Remove the colon from the match for better highlighting
                      match = match.replace(/:$/, '') + ':';
                  } else {
                      cls = 'json-string';
                  }
              } else if (/true|false/.test(match)) {
                  cls = 'json-boolean';
              } else if (/null/.test(match)) {
                  cls = 'json-null';
              }
              return '<span class="' + cls + '">' + match + '</span>';
          });
}

// Export empty object to satisfy TypeScript module requirement if no other exports exist
export {};