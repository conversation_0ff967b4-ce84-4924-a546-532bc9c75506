import api from './api'

/**
 * Get all NRC model versions
 * @param {number} [page=1] - Page number for pagination
 * @param {number} [limit=10] - Number of items per page
 * @returns {Promise<Object>} - The model versions data with pagination info
 */
export const getNrcModelVersions = async (page = 1, limit = 10) => {
  const response = await api.get(`/nrc/versions?page=${page}&limit=${limit}`)
  return response.data
}

/**
 * Get a specific NRC model version
 * @param {number} versionId - The ID of the model version
 * @returns {Promise<Object>} - The model version data
 */
export const getNrcModelVersion = async (versionId) => {
  const response = await api.get(`/nrc/versions/${versionId}`)
  return response.data
}

/**
 * Get all coefficient groups for a model version
 * @param {number} versionId - The ID of the model version
 * @returns {Promise<Object>} - The coefficient groups data
 */
export const getCoefficientGroups = async (versionId) => {
  const response = await api.get(`/nrc/versions/${versionId}/groups`)
  return response.data
}

/**
 * Get all coefficients for a group
 * @param {number} groupId - The ID of the coefficient group
 * @returns {Promise<Object>} - The coefficients data
 */
export const getCoefficients = async (groupId) => {
  const response = await api.get(`/nrc/groups/${groupId}/coefficients`)
  return response.data
}

/**
 * Set the active NRC model version
 * @param {number} versionId - The ID of the model version to set as active
 * @returns {Promise<Object>} - The response message
 */
export const setActiveModelVersion = async (versionId) => {
  const response = await api.post(`/nrc/versions/${versionId}/activate`)
  return response.data
}

/**
 * Get the currently active NRC model version
 * @returns {Promise<Object>} - The active model version data
 */
export const getActiveModelVersion = async () => {
  const response = await api.get('/nrc/versions/active')
  return response.data
}

/**
 * Create a new model comparison to evaluate different NRC models
 * @param {Object} comparisonData - The comparison settings
 * @returns {Promise<Object>} - The comparison results
 */
export const createModelComparison = async (comparisonData) => {
  const response = await api.post('/nrc/compare', comparisonData)
  return response.data
}