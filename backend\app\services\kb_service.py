import re
import logging
from sqlalchemy import func, desc, or_, text
from slugify import slugify
from flask import current_app
from .tasks import queue_embedding_generation_for_chunk
from sqlalchemy.orm import joinedload
from app.models.kb_models import KBArticle, KBCategory, KBTag, KBFeedback, KBArticleChunk
from app.extensions import db
from .embedding_service import EmbeddingService
from .llm_service import LLMFactory

logger = logging.getLogger(__name__)

def _chunk_content(original_content: str) -> list[str]:
    """Chunks content using the globally initialized chunker from app context."""
    # Access the chunker initialized in create_app
    chunker = getattr(current_app, 'text_chunker', None)
    # Remove empty lines after Markdown headings.
    original_content = re.sub(r'(^#+[^\n]+\n)\n', r'\1', original_content, flags = re.MULTILINE)

    if not chunker:
        logger.error("Chunker not available on current_app. Cannot chunk content.")
        return [original_content] if original_content else []
    if not original_content:
        return []

    try:
        logger.debug(f"Chunking content of length {len(original_content)} characters using app.text_chunker...")
        chunks = chunker(original_content)
        processed_chunks = [chunk for chunk in chunks if chunk and not chunk.isspace()]
        logger.info(f"Content split into {len(processed_chunks)} non-empty chunks.")
        return processed_chunks
    except Exception as e:
        logger.error(f"Error during chunking: {e}", exc_info=True)
        return [original_content] # Fallback

# --- Reconstruction Function (no change needed) ---
def reconstruct_article_content(article_id: int) -> str | None:
    """Reconstructs the full article content by concatenating chunks."""
    # ... (Implementation remains the same as previous version) ...
    db.session.remove()
    try:
        chunks_query = db.session.query(KBArticleChunk.chunk_text).filter(
            KBArticleChunk.article_id == article_id
        ).order_by(KBArticleChunk.chunk_order)
        chunks_texts = [item[0] for item in chunks_query.all()]

        if not chunks_texts:
            article_exists = db.session.query(KBArticle.id).filter(KBArticle.id == article_id).count() > 0
            return "" if article_exists else None

        full_content = "\n\n".join(chunks_texts)
        return full_content
    except Exception as e:
        logger.error(f"Error reconstructing content for article {article_id}: {e}", exc_info=True)
        db.session.rollback()
        return None
    finally:
        db.session.remove()

class KnowledgeBaseService:
    @staticmethod
    def get_categories(include_article_count=True):
        """Get all categories with optional article counts."""
        try:
            categories = KBCategory.query.order_by(KBCategory.order, KBCategory.name).all()

            if include_article_count:
                # Enhance with article counts
                result = []
                for category in categories:
                    cat_dict = category.to_dict()
                    cat_dict['article_count'] = KBArticle.query.filter_by(
                        category_id=category.id,
                        published=True
                    ).count()
                    result.append(cat_dict)
                return result

            return [category.to_dict() for category in categories]
        except Exception as e:
            logger.error(f"Error getting KB categories: {str(e)}")
            raise

    @staticmethod
    def get_category_by_slug(slug):
        """Get a category by its slug."""
        try:
            category = KBCategory.query.filter_by(slug=slug).first()
            if not category:
                return None
            return category.to_dict()
        except Exception as e:
            logger.error(f"Error getting KB category by slug: {str(e)}")
            raise

    @staticmethod
    def create_category(data):
        """Create a new category."""
        try:
            if 'slug' not in data or not data['slug']:
                data['slug'] = slugify(data['name'])

            # Check for existing slug
            existing = KBCategory.query.filter_by(slug=data['slug']).first()
            if existing:
                raise ValueError(f"Category with slug '{data['slug']}' already exists")

            # Handle parent_id - convert empty string to None (SQL NULL)
            parent_id = data.get('parent_id')
            if parent_id == '' or parent_id == 0:
                parent_id = None

            category = KBCategory(
                name=data['name'],
                slug=data['slug'],
                description=data.get('description'),
                parent_id=parent_id,
                order=data.get('order', 0)
            )

            db.session.add(category)
            db.session.commit()

            return category.to_dict()
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error creating KB category: {str(e)}")
            raise

    @staticmethod
    def update_category(category_id, data):
        """Update an existing category."""
        try:
            category = KBCategory.query.get(category_id)
            if not category:
                raise ValueError(f"Category with ID {category_id} not found")

            if 'name' in data:
                category.name = data['name']

            if 'slug' in data:
                # Check if new slug already exists for another category
                existing = KBCategory.query.filter(
                    KBCategory.slug == data['slug'],
                    KBCategory.id != category_id
                ).first()

                if existing:
                    raise ValueError(f"Category with slug '{data['slug']}' already exists")

                category.slug = data['slug']

            if 'description' in data:
                category.description = data['description']

            if 'parent_id' in data:
                # Prevent circular references
                if data['parent_id'] == category_id:
                    raise ValueError("Category cannot be its own parent")

                # Handle empty string or 0 as NULL
                if data['parent_id'] == '' or data['parent_id'] == 0:
                    category.parent_id = None
                else:
                    category.parent_id = data['parent_id']

            if 'order' in data:
                category.order = data['order']

            db.session.commit()

            return category.to_dict()
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating KB category: {str(e)}")
            raise

    @staticmethod
    def delete_category(category_id):
        """Delete a category and optionally reassign its articles."""
        try:
            category = KBCategory.query.get(category_id)
            if not category:
                raise ValueError(f"Category with ID {category_id} not found")

            # Check if category has articles
            if KBArticle.query.filter_by(category_id=category_id).count() > 0:
                raise ValueError("Cannot delete category with articles. Reassign articles first.")

            db.session.delete(category)
            db.session.commit()

            return True
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting KB category: {str(e)}")
            raise

    @staticmethod
    def get_articles(filters=None, limit=20, offset=0, sort_by='created_at', sort_order='desc'):
        """Get articles with pagination and filtering."""
        try:
            query = KBArticle.query

            # Apply filters
            if filters:
                if 'category_id' in filters:
                    query = query.filter_by(category_id=filters['category_id'])

                if 'published' in filters:
                    query = query.filter_by(published=filters['published'])

                #if 'user_id' in filters:
                #    query = query.filter_by(user_id=filters['user_id'])

                if 'tag' in filters:
                    tag = KBTag.query.filter_by(name=filters['tag']).first()
                    if tag:
                        query = query.filter(KBArticle.tags.contains(tag))

                if 'search' in filters and filters['search']:
                    search_term = f"%{filters['search']}%"
                    query = query.filter(
                        or_(
                            KBArticle.title.ilike(search_term),
                            KBArticle.content.ilike(search_term),
                            KBArticle.summary.ilike(search_term)
                        )
                    )

            # Apply sorting
            if sort_order.lower() == 'asc':
                query = query.order_by(getattr(KBArticle, sort_by))
            else:
                query = query.order_by(desc(getattr(KBArticle, sort_by)))

            # Get total count for pagination
            total = query.count()

            # Apply pagination
            articles = query.limit(limit).offset(offset).all()

            return {
                'articles': [article.to_dict() for article in articles],
                'total': total,
                'page': (offset // limit) + 1,
                'pages': (total + limit - 1) // limit  # Ceiling division
            }
        except Exception as e:
            logger.error(f"Error getting KB articles: {str(e)}")
            raise

    @staticmethod
    def get_article_by_slug(slug, increment_view=False):
        """Get an article by its slug.

        Returns the SQLAlchemy article object (not a dict) for more flexibility in the route.
        """
        try:
            article = KBArticle.query.filter_by(slug=slug).first()
            if not article:
                return None

            if increment_view:
                article.view_count += 1
                db.session.commit()

            return article
        except Exception as e:
            if increment_view:
                db.session.rollback()
            logger.error(f"Error getting KB article by slug: {str(e)}")
            raise

    @staticmethod
    def create_article(article_data, user_id):
        """Create article, chunk content, save chunks, queue embedding tasks."""
        db.session.remove()
        try:
            from slugify import slugify
            def generate_slug(title): return slugify(title)

            article = KBArticle(
                title=article_data['title'],
                slug=article_data.get('slug') or generate_slug(article_data['title']),
                summary=article_data.get('summary'),
                category_id=int(article_data['category_id']),
                user_id=user_id,
                published=article_data.get('published', True)
            )
            db.session.add(article)
            db.session.flush()
            article_id = article.id

            original_content = article_data.get('content', '')
            chunk_objects = []
            # --- USE HELPER FUNCTION ---
            chunks = _chunk_content(original_content)
            # ---
            logger.info(f"Creating {len(chunks)} chunks for new article {article_id}")
            for i, chunk_text in enumerate(chunks):
                chunk = KBArticleChunk(
                    article_id=article_id,
                    chunk_text=chunk_text,
                    chunk_order=i,
                    embedding_status='pending'
                )
                chunk_objects.append(chunk)
            if chunk_objects:
                db.session.add_all(chunk_objects)

            # Handle tags
            if 'tags' in article_data and article_data['tags']:
                 tags_to_assign = []
                 for tag_name in article_data['tags']:
                     tag = db.session.query(KBTag).filter_by(name=tag_name).first()
                     if not tag:
                         tag = KBTag(name=tag_name)
                         db.session.add(tag)
                     tags_to_assign.append(tag)
                 article.tags = tags_to_assign

            db.session.commit()
            logger.info(f"Article {article_id} and {len(chunk_objects)} chunks committed.")

            # Queue tasks for chunks
            if chunk_objects:
                new_chunk_ids = [c.id for c in chunk_objects if c.id is not None] # Ensure IDs are populated
                queued_count = 0
                for chunk_id in new_chunk_ids:
                    success = queue_embedding_generation_for_chunk(chunk_id)
                    if success: queued_count += 1
                logger.info(f"Queued {queued_count}/{len(new_chunk_ids)} chunks for embedding for article {article_id}.")

            return article.to_dict()

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error creating KB article: {str(e)}", exc_info=True)
            raise
        finally:
            db.session.remove()

    @staticmethod
    def update_article(article_id, data, user_id=None):
        """Update article, re-chunking and re-queueing if content changes."""
        db.session.remove()
        try:
            article = db.session.query(KBArticle).options(joinedload(KBArticle.tags)).get(article_id)
            if not article: raise ValueError(f"Article {article_id} not found")

            content_changed = 'content' in data
            new_content = data.get('content')
            title_changed = 'title' in data and data['title'] != article.title

            # Update basic fields
            if title_changed: article.title = data['title']
            if 'slug' in data: article.slug = data['slug']
            if 'summary' in data: article.summary = data['summary']
            if 'category_id' in data: article.category_id = int(data['category_id'])
            if 'published' in data: article.published = data['published']
            if 'metadata' in data: article.article_metadata = data['metadata']
            if 'tags' in data:
                 # Handle tags update
                 tags_to_assign = []
                 for tag_name in data['tags']:
                     tag = db.session.query(KBTag).filter_by(name=tag_name).first()
                     if not tag:
                         tag = KBTag(name=tag_name)
                         db.session.add(tag)
                     tags_to_assign.append(tag)
                 article.tags = tags_to_assign

            chunk_ids_to_queue = []
            existing_chunk_ids_to_requeue = []

            if content_changed:
                logger.info(f"Content changed for article {article_id}. Re-chunking...")
                deleted_count = db.session.query(KBArticleChunk).filter_by(article_id=article_id).delete(synchronize_session='fetch')
                logger.info(f"Deleted {deleted_count} old chunks.")
                db.session.flush()

                # --- USE HELPER FUNCTION ---
                chunks = _chunk_content(new_content or '')
                # ---
                logger.info(f"Creating {len(chunks)} new chunks.")
                new_chunk_objects = []
                for i, chunk_text in enumerate(chunks):
                    chunk = KBArticleChunk(article_id=article_id, chunk_text=chunk_text, chunk_order=i, embedding_status='pending')
                    new_chunk_objects.append(chunk)
                if new_chunk_objects:
                    db.session.add_all(new_chunk_objects)
                    db.session.flush()
                    chunk_ids_to_queue = [chunk.id for chunk in new_chunk_objects if chunk.id is not None]

            elif title_changed:
                 # Mark existing chunks for re-embedding if only title changed
                 logger.info(f"Title changed for article {article_id}. Marking existing chunks for re-embedding.")
                 # ... (logic to update status and get IDs as before) ...
                 update_count = db.session.query(KBArticleChunk).filter(
                     KBArticleChunk.article_id == article_id,
                     KBArticleChunk.embedding_status == 'completed'
                 ).update({'embedding_status': 'pending'}, synchronize_session=False)
                 logger.info(f"Marked {update_count} existing chunks as pending.")
                 db.session.flush()
                 existing_chunk_ids_to_requeue = [
                     row[0] for row in db.session.query(KBArticleChunk.id).filter(
                         KBArticleChunk.article_id == article_id,
                         KBArticleChunk.embedding_status == 'pending' # Select pending ones
                     ).all()
                 ]


            db.session.commit()
            logger.info(f"Article {article_id} updated.")

            # Queue tasks
            all_chunk_ids_to_queue = chunk_ids_to_queue + existing_chunk_ids_to_requeue
            if all_chunk_ids_to_queue:
                queued_count = 0
                for chunk_id in all_chunk_ids_to_queue:
                    success = queue_embedding_generation_for_chunk(chunk_id)
                    if success: queued_count += 1
                logger.info(f"Queued {queued_count}/{len(all_chunk_ids_to_queue)} chunks for embedding update for article {article_id}.")

            return article.to_dict()

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating KB article {article_id}: {str(e)}", exc_info=True)
            raise
        finally:
            db.session.remove()

    @staticmethod
    def delete_article(article_id, user_id=None):
        """Delete an article."""
        try:
            article = KBArticle.query.get(article_id)
            if not article:
                raise ValueError(f"Article with ID {article_id} not found")

            # Permission check removed to allow any authenticated user to delete articles

            db.session.delete(article)
            db.session.commit()

            return True
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting KB article: {str(e)}")
            raise

    @staticmethod
    def record_feedback(article_id, user_id, helpful, comment=None):
        """Record user feedback for an article."""
        try:
            article = KBArticle.query.get(article_id)
            if not article:
                raise ValueError(f"Article with ID {article_id} not found")

            # Check if user already provided feedback
            existing = KBFeedback.query.filter_by(
                article_id=article_id,
                user_id=user_id
            ).first()

            if existing:
                # Update existing feedback
                if helpful is not None:
                    # Update article counts if the helpful status changed
                    if existing.helpful is True and helpful is False:
                        article.helpful_count = max(0, article.helpful_count - 1)
                        article.not_helpful_count += 1
                    elif existing.helpful is False and helpful is True:
                        article.not_helpful_count = max(0, article.not_helpful_count - 1)
                        article.helpful_count += 1
                    elif existing.helpful is None and helpful is True:
                        article.helpful_count += 1
                    elif existing.helpful is None and helpful is False:
                        article.not_helpful_count += 1

                    existing.helpful = helpful

                if comment is not None:
                    existing.comment = comment

                db.session.commit()
                return existing.to_dict()
            else:
                # Create new feedback
                feedback = KBFeedback(
                    article_id=article_id,
                    user_id=user_id,
                    helpful=helpful,
                    comment=comment
                )

                db.session.add(feedback)

                # Update article counts
                if helpful is True:
                    article.helpful_count += 1
                elif helpful is False:
                    article.not_helpful_count += 1

                db.session.commit()
                return feedback.to_dict()
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error recording KB article feedback: {str(e)}")
            raise

    @staticmethod
    def get_popular_tags(limit=10):
        """Get the most popular tags with article counts."""
        try:
            # This query gets tags sorted by article count
            tags = db.session.query(
                KBTag,
                func.count(KBArticle.id).label('article_count')
            ).join(
                KBTag.articles
            ).group_by(
                KBTag.id
            ).order_by(
                desc('article_count')
            ).limit(limit).all()

            result = []
            for tag, count in tags:
                tag_dict = tag.to_dict()
                tag_dict['article_count'] = count
                result.append(tag_dict)

            return result
        except Exception as e:
            logger.error(f"Error getting popular KB tags: {str(e)}")
            raise

    @staticmethod
    def get_related_articles(article_id, limit=5):
        """Get related articles based on tags and category."""
        try:
            article = KBArticle.query.get(article_id)
            if not article:
                raise ValueError(f"Article with ID {article_id} not found")

            # Get articles with the same tags
            tag_ids = [tag.id for tag in article.tags]

            if tag_ids:
                # Find articles that share tags with this article
                related_by_tag = db.session.query(
                    KBArticle,
                    func.count(KBTag.id).label('shared_tags')
                ).join(
                    KBArticle.tags
                ).filter(
                    KBArticle.id != article_id,
                    KBArticle.published == True,
                    KBTag.id.in_(tag_ids)
                ).group_by(
                    KBArticle.id
                ).order_by(
                    desc('shared_tags'),
                    desc(KBArticle.view_count)
                ).limit(limit).all()

                related_articles = [a[0].to_dict() for a in related_by_tag]
            else:
                related_articles = []

            # If we don't have enough related by tag, add some from the same category
            if len(related_articles) < limit:
                remaining = limit - len(related_articles)
                related_ids = [a['id'] for a in related_articles]

                related_by_category = KBArticle.query.filter(
                    KBArticle.id != article_id,
                    KBArticle.id.not_in(related_ids) if related_ids else True,
                    KBArticle.category_id == article.category_id,
                    KBArticle.published == True
                ).order_by(
                    desc(KBArticle.view_count)
                ).limit(remaining).all()

                related_articles.extend([a.to_dict() for a in related_by_category])

            return related_articles
        except Exception as e:
            logger.error(f"Error getting related KB articles: {str(e)}")
            raise

    @staticmethod
    def semantic_search(query, limit=10, min_score=0.3):
        """Search articles based on semantic similarity using chunked embeddings."""
        import time
        start_time = time.time()
        logger.debug(f"Starting semantic search with query: '{query[:50]}{'...' if len(query) > 50 else ''}', limit: {limit}, min_score: {min_score}")

        try:
            # Generate embedding for the query
            embedding_start = time.time()
            query_embedding = KnowledgeBaseService._generate_query_embedding(query, type='SEMANTIC_SIMILARITY')
            embedding_time = time.time() - embedding_start
            logger.debug(f"Query embedding generation took {embedding_time:.3f}s")

            if not query_embedding:
                # Fall back to keyword search if embedding fails
                logger.warning("Falling back to keyword search due to embedding generation failure")
                return KnowledgeBaseService._keyword_search(query, limit)

            # Configure search parameters
            ef_search = current_app.config.get('PGVECTOR_HNSW_EF_SEARCH', 40)
            logger.debug(f"Using HNSW ef_search parameter: {ef_search}")

            # Set HNSW parameters if supported
            try:
                db.session.execute(text(f"SET LOCAL hnsw.ef_search = {ef_search}"))
                logger.debug(f"Successfully set hnsw.ef_search to {ef_search}")
            except Exception as e:
                logger.debug(f"Setting hnsw.ef_search not supported or failed: {str(e)}")

            # Find articles with the best matching chunks
            sql_query = text("""
            WITH chunk_search AS (
                SELECT
                    c.article_id,
                    c.chunk_text as chunk_content,
                    1 - (c.embedding_vector <=> :query_vector) AS similarity
                FROM ration_app.kb_article_chunks c
                WHERE c.embedding_vector IS NOT NULL
                ORDER BY c.embedding_vector <=> :query_vector
                LIMIT :chunk_limit
            ),
            best_chunks AS (
                SELECT DISTINCT ON (article_id)
                    article_id,
                    chunk_content,
                    similarity
                FROM chunk_search
                WHERE similarity >= :min_score
                ORDER BY article_id, similarity DESC
            )
            SELECT
                a.id, a.title, a.slug, a.summary, a.category_id,
                cat.name as category_name, a.view_count, a.created_at,
                a.updated_at, b.similarity, b.chunk_content
            FROM best_chunks b
            JOIN ration_app.kb_articles a ON b.article_id = a.id
            LEFT JOIN ration_app.kb_categories cat ON a.category_id = cat.id
            WHERE a.published = TRUE
            ORDER BY b.similarity DESC
            LIMIT :limit
            """)

            # Prepare query parameters
            query_params = {
                'query_vector': str(query_embedding),
                'chunk_limit': limit * 5,  # Get more chunks initially to find best per article
                'min_score': min_score,
                'limit': limit
            }
            logger.debug(f"Executing vector search with parameters: chunk_limit={query_params['chunk_limit']}, min_score={min_score}")

            # Execute the search
            query_start = time.time()
            result = db.session.execute(sql_query, query_params)
            query_time = time.time() - query_start
            logger.debug(f"Vector search query execution took {query_time:.3f}s")

            # Process results
            process_start = time.time()
            search_results = []
            result_count = 0

            for row in result:
                result_count += 1
                # Get the article to access its tags through the relationship
                article = db.session.query(KBArticle).get(row.id)
                tags = [tag.name for tag in article.tags] if article else []

                # Format the result with the matching chunk as the summary
                article_dict = {
                    'id': row.id,
                    'title': row.title,
                    'slug': row.slug,
                    'summary': row.chunk_content,  # Use the matching chunk as the summary
                    'original_summary': row.summary,  # Keep original summary as well
                    'category_id': row.category_id,
                    'category_name': row.category_name,
                    'view_count': row.view_count,
                    'created_at': row.created_at.isoformat() if row.created_at else None,
                    'updated_at': row.updated_at.isoformat() if row.updated_at else None,
                    'score': float(row.similarity),
                    'tags': tags
                }
                search_results.append(article_dict)
                logger.debug(f"Found article match: id={row.id}, title='{row.title[:30]}{'...' if len(row.title) > 30 else ''}', similarity={float(row.similarity):.4f}")

            process_time = time.time() - process_start
            logger.debug(f"Processed {result_count} vector search results in {process_time:.3f}s")

            total_time = time.time() - start_time
            logger.info(f"Semantic search completed in {total_time:.3f}s, found {len(search_results)} results for query: '{query[:30]}{'...' if len(query) > 30 else ''}'")
            return search_results[:limit]  # Limit final results

        except Exception as e:
            error_time = time.time() - start_time
            logger.error(f"Error performing KB semantic search after {error_time:.3f}s: {str(e)}", exc_info=True)
            # Fall back to keyword search
            logger.warning(f"Falling back to keyword search due to semantic search error")
            return KnowledgeBaseService._keyword_search(query, limit)

    @staticmethod
    def _keyword_search(query, limit=10, exclude_ids=None):
        """
        Fallback keyword-based search that matches the structure of semantic search results.

        Args:
            query: Search query text
            limit: Maximum number of results to return
            exclude_ids: Set of article IDs to exclude from results

        Returns:
            List of article dicts with relevant text excerpts as summaries
        """
        import time
        start_time = time.time()
        logger.debug(f"Starting keyword search with query: '{query[:50]}{'...' if len(query) > 50 else ''}', limit: {limit}")

        try:
            from sqlalchemy import or_, func
            from ..models.kb_models import KBArticle, KBArticleChunk

            # Create search terms for SQL query
            search_terms = [term.strip() for term in query.split() if term.strip()]
            logger.debug(f"Parsed {len(search_terms)} search terms from query")

            if not search_terms:
                logger.debug("No valid search terms found, returning empty results")
                return []

            # We'll search in both articles and chunks
            # First, find matching chunks
            chunk_query = db.session.query(
                KBArticleChunk.article_id,
                KBArticleChunk.chunk_text,
                func.count(KBArticleChunk.id).label('match_count')
            ).group_by(KBArticleChunk.article_id, KBArticleChunk.chunk_text)

            # Filter for chunks that match any of the search terms
            chunk_conditions = []
            for term in search_terms:
                term_pattern = f"%{term}%"
                chunk_conditions.append(
                    func.lower(KBArticleChunk.chunk_text).like(func.lower(term_pattern))
                )

            # Apply OR conditions for chunks (match any term)
            if chunk_conditions:
                chunk_query = chunk_query.filter(or_(*chunk_conditions))

            # Now find matching articles directly
            article_query = db.session.query(KBArticle)

            # Filter for published articles
            article_query = article_query.filter(KBArticle.published == True)

            # Exclude specific IDs if provided
            if exclude_ids:
                article_query = article_query.filter(~KBArticle.id.in_(exclude_ids))
                chunk_query = chunk_query.filter(~KBArticleChunk.article_id.in_(exclude_ids))

            # Apply search filters for each term in article metadata
            article_conditions = []
            for term in search_terms:
                term_pattern = f"%{term}%"
                article_conditions.append(or_(
                    func.lower(KBArticle.title).like(func.lower(term_pattern)),
                    func.lower(KBArticle.summary).like(func.lower(term_pattern)) if KBArticle.summary is not None else False
                ))

            # Apply OR conditions for articles (match any term)
            if article_conditions:
                article_query = article_query.filter(or_(*article_conditions))

            # Execute chunk query to get matching chunks
            chunk_start = time.time()
            matching_chunks = chunk_query.all()
            chunk_time = time.time() - chunk_start
            logger.debug(f"Found {len(matching_chunks)} matching chunks in {chunk_time:.3f}s")

            # Get article IDs from matching chunks
            chunk_article_ids = {chunk.article_id for chunk in matching_chunks}

            # Execute article query to get directly matching articles
            article_start = time.time()
            direct_matches = article_query.all()
            article_time = time.time() - article_start
            logger.debug(f"Found {len(direct_matches)} directly matching articles in {article_time:.3f}s")

            # Get article IDs from direct matches
            direct_article_ids = {article.id for article in direct_matches}

            # Combine both sets of article IDs
            all_article_ids = chunk_article_ids.union(direct_article_ids)
            logger.debug(f"Combined {len(all_article_ids)} unique matching article IDs")

            # If no matches found, return empty list
            if not all_article_ids:
                logger.debug("No matching articles found")
                return []

            # Get full article objects for all matching IDs
            # Filter for published and order by updated_at
            final_articles = db.session.query(KBArticle).filter(
                KBArticle.id.in_(all_article_ids),
                KBArticle.published == True
            ).order_by(KBArticle.updated_at.desc()).limit(limit).all()

            # Create a mapping of article ID to best matching chunk text
            best_chunks = {}
            for chunk in matching_chunks:
                if chunk.article_id not in best_chunks or \
                   chunk.match_count > best_chunks[chunk.article_id][1]:
                    best_chunks[chunk.article_id] = (chunk.chunk_text, chunk.match_count)

            # Format results to match semantic search structure
            format_start = time.time()
            formatted_results = []

            for article in final_articles:
                # Find best text to use as summary
                # 1. Use matching chunk if available
                # 2. Otherwise extract relevant text from article summary
                # 3. Fall back to article summary

                if article.id in best_chunks:
                    # Use the best matching chunk as summary
                    relevant_extract = best_chunks[article.id][0]
                    # Trim to a reasonable length if needed
                    if len(relevant_extract) > 300:
                        relevant_extract = KnowledgeBaseService._extract_relevant_text(relevant_extract, query, context_size=300)
                else:
                    # Try to extract from summary
                    relevant_extract = KnowledgeBaseService._extract_relevant_text(article.summary, query) if article.summary else None

                # Get tags through the relationship
                tags = [tag.name for tag in article.tags]

                # Calculate a score based on match location (title > summary > chunks)
                # This is a simplified scoring approach
                score = 0.7  # Default score

                # Boost score if title matches
                for term in search_terms:
                    if term.lower() in article.title.lower():
                        score = 0.8
                        break

                article_dict = {
                    'id': article.id,
                    'title': article.title,
                    'slug': article.slug,
                    'summary': relevant_extract or article.summary,  # Use extracted text or fall back to summary
                    'original_summary': article.summary,
                    'category_id': article.category_id,
                    'category_name': article.category.name if article.category else None,
                    'view_count': article.view_count,
                    'created_at': article.created_at.isoformat() if article.created_at else None,
                    'updated_at': article.updated_at.isoformat() if article.updated_at else None,
                    'score': score,
                    'tags': tags
                }
                formatted_results.append(article_dict)
                logger.debug(f"Added keyword result: id={article.id}, title='{article.title[:30]}{'...' if len(article.title) > 30 else ''}', score={score}")

            format_time = time.time() - format_start
            total_time = time.time() - start_time
            logger.debug(f"Formatted {len(formatted_results)} keyword search results in {format_time:.3f}s")
            logger.info(f"Keyword search completed in {total_time:.3f}s, found {len(formatted_results)} results for query: '{query[:30]}{'...' if len(query) > 30 else ''}'")

            return formatted_results

        except Exception as e:
            error_time = time.time() - start_time
            logger.error(f"Error in keyword search after {error_time:.3f}s: {str(e)}", exc_info=True)
            return []

    @staticmethod
    def _extract_relevant_text(content, query, context_size=100):
        """
        Extract the most relevant text snippet from content based on search query.

        Args:
            content: The article content text
            query: The search query
            context_size: Number of characters of context to include

        Returns:
            Most relevant text snippet or None if no match
        """
        if not content or not query:
            return None

        # Convert to lowercase for case-insensitive matching
        content_lower = content.lower()
        query_lower = query.lower()

        # Try to find the position of the query
        pos = content_lower.find(query_lower)

        # If direct query not found, try individual terms
        if pos == -1:
            terms = query_lower.split()
            # Find the first term that appears in the content
            for term in terms:
                if len(term) > 2:  # Skip very short terms
                    pos = content_lower.find(term)
                    if pos != -1:
                        break

        # If we found a match
        if pos != -1:
            # Calculate the start and end positions for the extract
            start = max(0, pos - context_size // 2)
            end = min(len(content), pos + len(query) + context_size // 2)

            # Find the start of the current paragraph or sentence
            if start > 0:
                # Try to start at paragraph break or sentence
                paragraph_start = content.rfind('\n\n', 0, pos)
                sentence_start = content.rfind('. ', 0, pos)
                if paragraph_start != -1 and paragraph_start > pos - context_size:
                    start = paragraph_start + 2  # Skip the newlines
                elif sentence_start != -1 and sentence_start > pos - context_size:
                    start = sentence_start + 2  # Skip the period and space

            # Find the end of current paragraph or sentence
            if end < len(content):
                paragraph_end = content.find('\n\n', pos)
                sentence_end = content.find('. ', pos)
                if paragraph_end != -1 and paragraph_end < pos + context_size:
                    end = paragraph_end
                elif sentence_end != -1 and sentence_end < pos + context_size:
                    end = sentence_end + 1  # Include the period

            # Extract the relevant text
            extract = content[start:end].strip()

            # Add ellipsis if needed
            if start > 0:
                extract = '...' + extract
            if end < len(content):
                extract = extract + '...'

            return extract

        # If no match found, return the beginning of the content
        return content[:min(len(content), 200)] + '...' if len(content) > 200 else content

    @staticmethod
    def _generate_summary(title, content, max_length=200):
        """Generate a summary for an article using AI or simple truncation."""
        try:
            # Try to use LLM for summary generation
            llm_provider = LLMFactory.get_current_provider()
            prompt = f"""
            Please generate a concise summary (maximum {max_length} characters) for the following article.
            Just provide the summary text without any additional text or explanations.

            Title: {title}

            Content:
            {content}
            """

            summary = llm_provider.analyze_data(prompt, max_tokens=100)

            # Clean up the summary
            summary = summary.strip()

            # Truncate if still too long
            if len(summary) > max_length:
                summary = summary[:max_length-3] + "..."

            return summary
        except Exception as e:
            logger.warning(f"Could not generate AI summary, falling back to truncation: {str(e)}")

            # Simple truncation fallback
            # Remove HTML/Markdown and excess whitespace
            clean_content = re.sub(r'<[^>]*>', ' ', content)
            clean_content = re.sub(r'\s+', ' ', clean_content).strip()

            if len(clean_content) <= max_length:
                return clean_content

            return clean_content[:max_length-3] + "..."

    @staticmethod
    def _generate_query_embedding(query, type='SEARCH_QUERY'):
        """Generate embedding for a search query or document.

        Args:
            query: The text to generate an embedding for
            type: The type of embedding to generate ('SEARCH_QUERY' or 'RETRIEVAL_DOCUMENT')

        Returns:
            The embedding vector or None if generation fails
        """
        try:
            from .embedding_service import EmbeddingService
            return EmbeddingService.generate_embeddings(query, type=type)
        except Exception as e:
            logger.warning(f"Could not generate query embedding: {str(e)}")
            return None

    @staticmethod
    def get_article_content(article_id):
        """Get the full content of an article by reconstructing it from chunks.

        Args:
            article_id: The ID of the article

        Returns:
            The full content of the article, or None if not found
        """
        try:
            # Use the existing reconstruct_article_content function
            return reconstruct_article_content(article_id)
        except Exception as e:
            logger.error(f"Error getting article content for ID {article_id}: {str(e)}")
            return None

    @staticmethod
    def answer_question(question, context_window=3, locale='en'):
        """Answer a question using relevant KB articles as context.

        Args:
            question: The question to answer
            context_window: Number of relevant articles to use as context
            locale: The language locale (e.g., 'en', 'es', 'fr')

        Returns:
            Dictionary with answer and sources
        """
        try:
            # Find relevant articles
            relevant_articles = KnowledgeBaseService.semantic_search(question, limit=context_window)

            # Prepare locale-specific no results message
            no_results_messages = {
                'en': "I couldn't find any relevant information to answer your question.",
                'es': "No pude encontrar información relevante para responder a tu pregunta.",
                'fr': "Je n'ai pas pu trouver d'informations pertinentes pour répondre à votre question.",
                'zh': "我找不到相关信息来回答您的问题。",
                'de': "Ich konnte keine relevanten Informationen finden, um Ihre Frage zu beantworten."
            }
            no_content_messages = {
                'en': "I found some potentially relevant articles, but couldn't access their content.",
                'es': "Encontré algunos artículos potencialmente relevantes, pero no pude acceder a su contenido.",
                'fr': "J'ai trouvé des articles potentiellement pertinents, mais je n'ai pas pu accéder à leur contenu.",
                'zh': "我找到了一些可能相关的文章，但无法访问其内容。",
                'de': "Ich habe einige möglicherweise relevante Artikel gefunden, konnte aber nicht auf deren Inhalt zugreifen."
            }

            # Default to English if locale not supported
            if locale not in no_results_messages:
                locale = 'en'

            if not relevant_articles:
                return {
                    "answer": no_results_messages[locale],
                    "sources": []
                }

            # Get full content of relevant articles
            article_contents = []
            sources = []

            for article_result in relevant_articles:
                article_id = article_result['id']
                article = KBArticle.query.get(article_id)

                if not article or not article.published:
                    continue

                # Get the full content using our general function
                content = KnowledgeBaseService.get_article_content(article_id)

                if content is not None:
                    # Add the article to our context
                    article_contents.append(f"Title: {article.title}\n\nContent: {content}")
                    sources.append({
                        "id": article.id,
                        "title": article.title,
                        "slug": article.slug,
                        "score": article_result.get('score', 0.0)
                    })

            if not article_contents:
                return {
                    "answer": no_content_messages[locale],
                    "sources": []
                }

            # Create context for LLM
            context = "\n\n---\n\n".join(article_contents)

            # Generate answer using LLM with an improved RAG prompt that includes locale
            llm_provider = LLMFactory.get_current_provider()

            # Define locale-specific instructions
            locale_instructions = {
                'en': "Answer in English. Be clear and concise.",
                'es': "Responde en español. Sé claro y conciso.",
                'fr': "Répondez en français. Soyez clair et concis.",
                'zh': "用中文回答。请清晰简洁。",
                'de': "Antworten Sie auf Deutsch. Seien Sie klar und präzise."
            }

            prompt = f"""
            You are a helpful assistant for a dairy nutrition platform. Answer the following question based ONLY on the information provided in the context below.

            {locale_instructions.get(locale, locale_instructions['en'])}

            Guidelines:
            1. If the context doesn't contain enough information to answer the question fully, acknowledge this limitation and then provide what you can based on the available context.
            2. Do not make up information or use knowledge outside of the provided context.
            3. Keep your answer concise and directly relevant to the question.
            4. Do not include citations or references in your answer.
            5. If the question is about dairy nutrition or farming and the context provides relevant information, focus on that specific information.

            Question: {question}

            Context:
            {context}
            """

            answer = llm_provider.analyze_data(prompt, max_tokens=500)

            return {
                "answer": answer.strip(),
                "sources": sources
            }
        except Exception as e:
            logger.error(f"Error answering question: {str(e)}")
            # Locale-specific error messages
            error_messages = {
                'en': "I encountered an error while trying to answer your question.",
                'es': "Encontré un error al intentar responder a tu pregunta.",
                'fr': "J'ai rencontré une erreur en essayant de répondre à votre question.",
                'zh': "在尝试回答您的问题时遇到了错误。",
                'de': "Beim Versuch, Ihre Frage zu beantworten, ist ein Fehler aufgetreten."
            }
            return {
                "answer": error_messages.get(locale, error_messages['en']),
                "sources": []
            }

    @staticmethod
    def get_article_stats():
        """Get overall KB statistics."""
        try:
            # Get basic stats
            stats = {
                "total_articles": KBArticle.query.count(),
                "published_articles": KBArticle.query.filter_by(published=True).count(),
                "total_categories": KBCategory.query.count(),
                "total_tags": KBTag.query.count(),
                "total_views": db.session.query(func.sum(KBArticle.view_count)).scalar() or 0,
                "embedding_status": {
                    "pending": 0,
                    "processing": 0,
                    "completed": 0,
                    "failed": 0,
                    "no_chunks": 0
                },
                "most_viewed": None,
                "most_helpful": None
            }

            # Calculate embedding status counts for articles
            # Get all articles
            articles = KBArticle.query.all()
            for article in articles:
                # Get counts for each status for this article's chunks
                status_counts = dict(db.session.query(
                    KBArticleChunk.embedding_status,
                    func.count(KBArticleChunk.id)
                ).filter(KBArticleChunk.article_id == article.id)
                 .group_by(KBArticleChunk.embedding_status).all())

                total_chunks = sum(status_counts.values())

                # Determine overall status using same logic as in KBArticle.to_dict()
                if total_chunks == 0:
                    stats["embedding_status"]["no_chunks"] += 1
                elif status_counts.get('failed', 0) > 0:
                    stats["embedding_status"]["failed"] += 1
                elif status_counts.get('processing', 0) > 0:
                    stats["embedding_status"]["processing"] += 1
                elif status_counts.get('pending', 0) > 0:
                    stats["embedding_status"]["pending"] += 1
                elif status_counts.get('completed', 0) == total_chunks:
                    stats["embedding_status"]["completed"] += 1

            # Get most viewed article
            most_viewed = KBArticle.query.filter_by(published=True).order_by(
                desc(KBArticle.view_count)
            ).first()

            if most_viewed:
                stats["most_viewed"] = {
                    "id": most_viewed.id,
                    "title": most_viewed.title,
                    "slug": most_viewed.slug,
                    "view_count": most_viewed.view_count
                }

            # Get most helpful article (highest ratio of helpful/total feedback)
            helpful_articles = KBArticle.query.filter(
                KBArticle.published == True,
                KBArticle.helpful_count > 0
            ).order_by(
                # Order by ratio of helpful to total feedbacks
                (KBArticle.helpful_count / (KBArticle.helpful_count + KBArticle.not_helpful_count)).desc(),
                # Secondary sort by total helpful count
                desc(KBArticle.helpful_count)
            ).first()

            if helpful_articles:
                total_feedback = helpful_articles.helpful_count + helpful_articles.not_helpful_count
                helpful_ratio = helpful_articles.helpful_count / total_feedback if total_feedback > 0 else 0

                stats["most_helpful"] = {
                    "id": helpful_articles.id,
                    "title": helpful_articles.title,
                    "slug": helpful_articles.slug,
                    "helpful_count": helpful_articles.helpful_count,
                    "not_helpful_count": helpful_articles.not_helpful_count,
                    "helpful_ratio": helpful_ratio
                }

            return stats
        except Exception as e:
            logger.error(f"Error getting KB stats: {str(e)}")
            raise

    @staticmethod
    def reprocess_failed_embeddings(limit=50):
        """
        Reprocess chunks with failed embedding generation.

        Args:
            limit: Maximum number of chunks to reprocess

        Returns:
            Number of chunks queued for reprocessing
        """
        try:
            # Find chunks with failed embedding status
            failed_chunks = KBArticleChunk.query.filter_by(
                embedding_status='failed'
            ).limit(limit).all()

            count = 0
            for chunk in failed_chunks:
                # Reset status for reprocessing
                chunk.embedding_status = 'pending'
                count += 1

            db.session.commit()

            # Queue processing
            if count > 0:
                from .tasks import queue_embedding_generation_for_chunk
                for chunk in failed_chunks:
                    queue_embedding_generation_for_chunk(chunk.id)

            return count
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error reprocessing failed embeddings: {str(e)}")
            return 0

    @staticmethod
    def reprocess_completed_embeddings(limit=50, article_id=None, process_all=False):
        """
        Reprocess chunks with completed embedding status.
        Use this when the embedding service has changed and you need to regenerate embeddings.
        Always re-chunks the article content before re-embedding.

        Args:
            limit: Maximum number of articles to reprocess (ignored if process_all=True)
            article_id: Optional article ID to filter chunks by
            process_all: If True, process all articles without limit

        Returns:
            Number of chunks queued for reprocessing
        """
        try:
            # Get article IDs to reprocess
            article_ids_query = None

            if article_id is not None:
                # If specific article ID is provided, just use that
                article = db.session.query(KBArticle).get(article_id)
                if not article:
                    logger.warning(f"Article with ID {article_id} not found")
                    return 0
                article_ids = [article_id]
            else:
                # Otherwise, get all articles
                article_ids_query = db.session.query(KBArticle.id).distinct()

                # Apply limit if not processing all
                if not process_all:
                    article_ids_query = article_ids_query.limit(limit)

                # Get the article IDs
                article_ids = [row[0] for row in article_ids_query.all()]

            logger.info(f"Found {len(article_ids)} articles to reprocess with re-chunking")
            total_chunks_queued = 0

            for art_id in article_ids:
                # Get the article to access its title
                article = db.session.query(KBArticle).get(art_id)
                if not article:
                    logger.warning(f"Article with ID {art_id} not found")
                    continue

                # Get the full article content
                full_content = reconstruct_article_content(art_id)
                if not full_content:
                    logger.warning(f"Could not reconstruct content for article {art_id}")
                    continue

                # Delete existing chunks
                deleted_count = db.session.query(KBArticleChunk).filter_by(article_id=art_id).delete(synchronize_session='fetch')
                logger.info(f"Deleted {deleted_count} existing chunks for article {art_id}")
                db.session.flush()

                # Re-chunk the content
                chunks = _chunk_content(full_content)
                logger.info(f"Re-chunked article {art_id} into {len(chunks)} chunks")

                # Create new chunks
                new_chunk_objects = []
                for i, chunk_text in enumerate(chunks):
                    chunk = KBArticleChunk(
                        article_id=art_id,
                        chunk_text=chunk_text,
                        chunk_order=i,
                        embedding_status='pending'
                    )
                    new_chunk_objects.append(chunk)

                if new_chunk_objects:
                    db.session.add_all(new_chunk_objects)
                    # Commit the transaction to ensure chunks are saved to the database
                    # before the worker process tries to access them
                    db.session.commit()

                    # Queue embedding generation for new chunks
                    from .tasks import queue_embedding_generation_for_chunk
                    queued_count = 0
                    for chunk in new_chunk_objects:
                        if chunk.id is not None:  # Ensure ID is populated
                            success = queue_embedding_generation_for_chunk(chunk.id)
                            if success:
                                queued_count += 1
                                total_chunks_queued += 1

                    logger.info(f"Queued {queued_count}/{len(new_chunk_objects)} chunks for embedding for article {art_id}")

                    # Start a new transaction for the next article
                    db.session.begin()

            # Final commit in case there are any pending changes
            if db.session.in_transaction():
                db.session.commit()
            logger.info(f"Re-chunked and queued {total_chunks_queued} chunks for embedding across {len(article_ids)} articles")
            return total_chunks_queued
        except Exception as e:
            # Ensure we rollback any active transaction
            if db.session.in_transaction():
                db.session.rollback()
            logger.error(f"Error reprocessing completed embeddings: {str(e)}", exc_info=True)
            return 0

    @staticmethod
    def keyword_search(query, limit=10):
        """
        Perform keyword-based search on articles.

        Args:
            query (str): The search query
            limit (int): Maximum number of results to return

        Returns:
            list: List of article dictionaries with score
        """
        # Simply delegate to our internal _keyword_search method
        # which has been updated to work with the chunked model
        return KnowledgeBaseService._keyword_search(query, limit)