import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate, useLocation } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useTranslation } from 'react-i18next'; // Import useTranslation

// Import NEW service functions
// Assuming getImportSessionTypes is also in importService or needs its own import
import { getPendingImportData, confirmImport, discardImport, getImportSessionTypes } from '../../services/importService'; // Added getImportSessionTypes

// Import UI components
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';

// ExpandableCell Component (Assumed unchanged and potentially needing its own i18n later)
const ExpandableCell = ({ content }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const isNutrientArray = Array.isArray(content) && content.length > 0 && ('nutrient_id' in content[0] || 'nutrient_name_en' in content[0]);
    const isGenericObject = typeof content === 'object' && content !== null && !isNutrientArray;

    let canExpand = isNutrientArray || isGenericObject;

    if (!canExpand) {
        return <span>{content === null || content === undefined ? '' : String(content)}</span>;
    }

    return (
        <div>
            <button
                type="button"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-blue-600 hover:text-blue-800 hover:underline focus:outline-none text-xs"
                aria-expanded={isExpanded}
            >
             {/* NOTE: Text inside ExpandableCell is not translated here as requested */}
                {isExpanded ? '[-] Collapse' : '[+] Expand'} {isNutrientArray ? `Nutrients (${content.length})` : 'Object'}
            </button>
            {isExpanded && (
                <div className="mt-1 p-1 border border-gray-200 rounded bg-gray-50 max-h-40 overflow-auto">
                    {isNutrientArray ? (
                        <table className="min-w-full text-xs">
                             <thead>
                                 <tr className="bg-gray-200">
                                 {/* NOTE: Text inside ExpandableCell is not translated here as requested */}
                                     <th className="px-2 py-1 text-left font-medium">Nutrient</th>
                                     <th className="px-2 py-1 text-right font-medium">Value</th>
                                 </tr>
                             </thead>
                             <tbody className="divide-y divide-gray-200">
                                 {content.map((nutrient, index) => (
                                     <tr key={nutrient.nutrient_id || index} className="border-t border-gray-200">
                                         <td className="px-2 py-1">{nutrient.nutrient_name_en || `ID: ${nutrient.nutrient_id}`}</td>
                                         <td className="px-2 py-1 text-right">{nutrient.value}</td>
                                     </tr>
                                 ))}
                             </tbody>
                        </table>
                    ) : (
                        <pre className="text-xs">
                            {JSON.stringify(content, null, 2)}
                        </pre>
                    )}
                </div>
            )}
        </div>
    );
};


// SimpleTable Component (Assumed unchanged and potentially needing its own i18n later)
const SimpleTable = ({ headers, data }) => {
   if (!data || data.length === 0) {
       // NOTE: Text inside SimpleTable is not translated here as requested
     return <p className="text-center text-gray-500 py-4">No data available for this page.</p>;
   }
   const displayHeaders = headers;

   return (
     <div className="max-h-[60vh] overflow-auto border border-gray-300 rounded">
       <table className="min-w-full divide-y divide-gray-200 text-sm">
         <thead className="bg-gray-100">
           <tr>
             {displayHeaders.map(header => (
               <th key={header} className="px-4 py-2 text-left font-medium text-gray-600 uppercase tracking-wider sticky top-0 bg-gray-100 z-10">
                 {/* Header transformation logic remains, may need i18n mapping if headers are fixed keys */}
                 {header === '_related_nutrients' ? 'Related Nutrients' : header.replace(/_/g, ' ')}
               </th>
             ))}
           </tr>
         </thead>
         <tbody className="bg-white divide-y divide-gray-200">
           {data.map((row, rowIndex) => (
             <tr key={row.id || row._excel_row || rowIndex} className="hover:bg-gray-50">
               {displayHeaders.map(header => (
                 <td key={`${row.id || rowIndex}-${header}`} className="px-4 py-2 align-top">
                   <ExpandableCell content={row[header]} />
                 </td>
               ))}
             </tr>
           ))}
         </tbody>
       </table>
     </div>
   );
};


// Pagination Component (Assumed unchanged and potentially needing its own i18n later)
const Pagination = ({ currentPage, totalPages, onPageChange, isLoading }) => {
   if (totalPages <= 1) return null;
   const handlePrev = () => onPageChange(currentPage - 1);
   const handleNext = () => onPageChange(currentPage + 1);

   return (
     <div className="mt-4 flex justify-center items-center space-x-2">
       <Button onClick={handlePrev} disabled={currentPage === 1 || isLoading} size="sm" variant="outline">
         {/* NOTE: Text inside Pagination is not translated here as requested */}
         &lt;&lt; Prev
       </Button>
        {/* NOTE: Text inside Pagination is not translated here as requested */}
       <span className="text-sm text-gray-700">Page {currentPage} of {totalPages}</span>
       <Button onClick={handleNext} disabled={currentPage === totalPages || isLoading} size="sm" variant="outline">
         {/* NOTE: Text inside Pagination is not translated here as requested */}
         Next &gt;&gt;
       </Button>
     </div>
   );
};


// Main Review Page Component (Refactored with i18n)
const ExcelImportReviewPage = () => {
    const { t } = useTranslation(); // Initialize translation hook
    const { sessionId } = useParams();
    const navigate = useNavigate();
    const queryClient = useQueryClient();
    const location = useLocation();

    const queryParams = new URLSearchParams(location.search);
    const recordType = queryParams.get('recordType');

    const [currentPage, setCurrentPage] = useState(1);
    const [commitMode, setCommitMode] = useState('replace');
    const [statusMessage, setStatusMessage] = useState({ text: '', type: '' });
    const [isProcessed, setIsProcessed] = useState(false);
    const [availableTypes, setAvailableTypes] = useState([]);

    const ROWS_PER_PAGE = 100;

    const handleRecordTypeChange = (newType) => {
        if (newType !== recordType) {
            setCurrentPage(1);
            setIsProcessed(false);
            setStatusMessage({ text: '', type: '' });
            navigate(`${location.pathname}?recordType=${newType}`, { replace: true });
        }
    };

    const {
        data: sessionTypesData,
        isLoading: isLoadingTypes,
    } = useQuery(
        ['importSessionTypes', sessionId],
        () => getImportSessionTypes(sessionId),
        {
            enabled: !!sessionId,
            onSuccess: (data) => {
                if (data?.recordTypes?.length > 0) {
                    const types = data.recordTypes.map(t => t.type);
                    setAvailableTypes(types);
                    if (!recordType || !types.includes(recordType)) {
                         navigate(`${location.pathname}?recordType=${types[0]}`, { replace: true });
                         setCurrentPage(1);
                    }
                } else {
                    setAvailableTypes([]);
                     if (!recordType) {
                       // i18n key used
                         setStatusMessage({text: t('excelImportReview.status.noTypesFoundForSession'), type: 'info'})
                     }
                }
            },
            onError: (err) => {
                // i18n key used with interpolation
                const errorMessage = err.message || t('excelImportReview.errors.unknownError');
                setStatusMessage({ text: t('excelImportReview.status.loadingTypesError', { errorMessage }), type: 'error' });
                 setAvailableTypes([]);
            }
        }
    );

    const {
        data: pendingDataResponse,
        isLoading,
        isError,
        error,
        isFetching,
        refetch
    } = useQuery(
        ['pendingImportData', sessionId, recordType, currentPage],
        () => getPendingImportData(sessionId, recordType, currentPage, ROWS_PER_PAGE),
        {
            keepPreviousData: true,
            enabled: !!recordType && !!sessionId && availableTypes.includes(recordType),
            onError: (err) => {
                // i18n key used with interpolation
                const errorMessage = err?.message || err?.error || t('excelImportReview.errors.unknownErrorLoadingPending');
                setStatusMessage({ text: t('excelImportReview.status.loadingDataError', { recordType, errorMessage }), type: 'error' });
            },
            onSuccess: (data) => {
                if (!isProcessed && statusMessage.type === 'error') {
                    setStatusMessage({ text: '', type: '' });
                }
                // No user-facing text here that needs translation
            }
        }
    );

    const pendingData = pendingDataResponse?.data;
    const pagination = {
        currentPage: pendingDataResponse?.page || 1,
        totalPages: pendingDataResponse?.totalPages || 1,
        totalRecords: pendingDataResponse?.totalRecords || 0
    };

    let tableHeaders = [];
    if (pendingData?.[0]) {
        tableHeaders = Object.keys(pendingData[0]).filter(
            key => key !== 'import_status' && key !== 'import_session_id' && key !== 'user_id'
        );
        // Reorder logic remains the same
       if (tableHeaders.includes('_related_nutrients')) {
           tableHeaders = tableHeaders.filter(h => h !== '_related_nutrients');
           tableHeaders.push('_related_nutrients');
       }
       const preferredOrder = ['id', 'name', 'ear_num'];
       tableHeaders.sort((a, b) => {
         const idxA = preferredOrder.indexOf(a);
         const idxB = preferredOrder.indexOf(b);
         if (idxA !== -1 && idxB !== -1) return idxA - idxB;
         if (idxA !== -1) return -1;
         if (idxB !== -1) return 1;
         if (a === '_related_nutrients') return 1;
         if (b === '_related_nutrients') return -1;
         return a.localeCompare(b);
       });
    }

    const confirmMutation = useMutation(
        () => confirmImport(sessionId, recordType, commitMode),
        {
            onSuccess: (data) => {
                // i18n key used with interpolation
                setStatusMessage({ text: data.message || t('excelImportReview.status.confirmSuccess', { recordType }), type: 'success' });
                setIsProcessed(true);
                queryClient.invalidateQueries([recordType]);
                queryClient.invalidateQueries(['pendingImportData', sessionId, recordType]);
                queryClient.removeQueries(['pendingImportData', sessionId, recordType]);
                queryClient.invalidateQueries(['importSessionTypes', sessionId]);
            },
            onError: (err) => {
                // i18n key used with interpolation
                const errorMessage = err.message || err.error || t('excelImportReview.errors.unknownError');
                setStatusMessage({ text: t('excelImportReview.status.confirmError', { recordType, errorMessage }), type: 'error' });
                 setIsProcessed(false);
            },
        }
    );

    const discardMutation = useMutation(
        () => discardImport(sessionId, recordType),
        {
            onSuccess: (data) => {
                // i18n key used with interpolation
                setStatusMessage({ text: data.message || t('excelImportReview.status.discardSuccess', { recordType }), type: 'success' });
                setIsProcessed(true);
                 queryClient.invalidateQueries(['pendingImportData', sessionId, recordType]);
                 queryClient.removeQueries(['pendingImportData', sessionId, recordType]);
                queryClient.invalidateQueries(['importSessionTypes', sessionId]);
            },
            onError: (err) => {
                // i18n key used with interpolation
                const errorMessage = err.message || err.error || t('excelImportReview.errors.unknownError');
                setStatusMessage({ text: t('excelImportReview.status.discardError', { recordType, errorMessage }), type: 'error' });
                setIsProcessed(false);
            },
        }
    );

    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= pagination.totalPages) {
            setCurrentPage(newPage);
        }
    };

    const handleConfirm = () => {
        if (isProcessed || confirmMutation.isLoading || !recordType) return;
        // i18n keys used for confirmation prompt
        let confirmMessage = t('excelImportReview.prompts.confirmImport', { recordType, sessionId, commitMode });
        if (commitMode === 'replace') {
            confirmMessage += t('excelImportReview.prompts.confirmReplaceWarning', { recordType });
        }
        if (window.confirm(confirmMessage)) {
            // i18n key used with interpolation
            setStatusMessage({ text: t('excelImportReview.status.processingConfirm', { recordType }), type: 'info' });
            confirmMutation.mutate();
        }
    };

    const handleDiscard = () => {
        if (isProcessed || discardMutation.isLoading || !recordType) return;
        // i18n key used for confirmation prompt
        if (window.confirm(t('excelImportReview.prompts.discardImport', { recordType, sessionId }))) {
            // i18n key used with interpolation
            setStatusMessage({ text: t('excelImportReview.status.processingDiscard', { recordType }), type: 'info' });
            discardMutation.mutate();
        }
    };


    // --- Render Logic ---

    // Initial loading state for session types
    if (isLoadingTypes && !availableTypes.length) {
        // i18n key used
         return <Card title={t('excelImportReview.loading.sessionTitle')}><p>{t('excelImportReview.loading.loadingTypes')}</p></Card>;
    }

    // Handle case where URL is missing recordType AND no types were found
    if (!recordType && availableTypes.length === 0 && !isLoadingTypes) {
        return (
             <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 space-y-6">
             {/* i18n key used */}
                 <h1 className="text-2xl font-bold text-gray-900">{t('excelImportReview.title')}</h1>
             {/* i18n key used */}
                 <p className="text-sm text-gray-600">{t('excelImportReview.sessionIdLabel')} {sessionId}</p>
                 {statusMessage.text && (
                     <div className={`p-4 rounded ${statusMessage.type === 'success' ? 'bg-green-100 text-green-800' : statusMessage.type === 'error' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`}>
                         {statusMessage.text} {/* Already translated when set */}
                     </div>
                 )}
             {/* i18n keys used */}
                 <Card title={t('excelImportReview.errors.noDataFoundTitle')}>
                     <p>{t('excelImportReview.errors.noDataFoundText')}</p>
                 </Card>
                 <Link to="/" className="mt-6 inline-block">
                    <Button variant='outline'>{t('excelImportReview.actions.backToDashboard')}</Button> {/* i18n key used */}
                 </Link>
             </div>
         );
    }

    // Handle case where URL has a recordType, but it's not valid for this session
     if (recordType && !isLoadingTypes && availableTypes.length > 0 && !availableTypes.includes(recordType)) {
         return (
             <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 space-y-6">
             {/* i18n key used */}
                 <h1 className="text-2xl font-bold text-gray-900">{t('excelImportReview.errors.invalidTypeTitle')}</h1>
             {/* i18n key used */}
                 <p className="text-sm text-gray-600">{t('excelImportReview.sessionIdLabel')} {sessionId}</p>
             {/* i18n keys used */}
                 <Card title={t('excelImportReview.errors.invalidTypeCardTitle')}>
                  {/* i18n key used with interpolation */}
                     <p className="text-red-600">{t('excelImportReview.errors.invalidTypeText', { recordType })}</p>
                  {/* i18n key used */}
                     <p className="mt-2">{t('excelImportReview.errors.availableTypesLabel')}</p>
                      <div className="flex flex-wrap gap-2 mt-2">
                          {availableTypes.map(type => (
                              <button
                                  key={type}
                                  onClick={() => handleRecordTypeChange(type)}
                                  className={'bg-gray-200 text-gray-800 hover:bg-gray-300 px-4 py-2 rounded text-sm font-medium transition-colors'}
                              >
                                  {type} {/* Keep type name as is, assuming it's a technical identifier */}
                              </button>
                          ))}
                      </div>
                 </Card>
                  <Link to="/" className="mt-6 inline-block">
                    {/* i18n key used */}
                      <Button variant='outline'>{t('excelImportReview.actions.backToDashboard')}</Button>
                  </Link>
             </div>
         );
     }

    // Main Render Output
    return (
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 space-y-6">
         {/* i18n key used, conditionally displays placeholder */}
            <h1 className="text-2xl font-bold text-gray-900">
             {recordType
                ? t('excelImportReview.titleWithType', { recordType })
                : t('excelImportReview.title')
             }
             {!recordType && availableTypes.length > 0 && `: ${t('excelImportReview.selectTypePlaceholder')}`}
         </h1>
         {/* i18n key used */}
            <p className="text-sm text-gray-600">{t('excelImportReview.sessionIdLabel')} {sessionId}</p>

            {/* Status Message Area */}
            {statusMessage.text && (
                <div className={`p-4 rounded ${statusMessage.type === 'success' ? 'bg-green-100 text-green-800' : statusMessage.type === 'error' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`}>
                    {statusMessage.text} {/* Already translated */}
                </div>
            )}

            {/* Available Record Types Selection */}
            {availableTypes.length > 1 && (
             // i18n key used
                <Card title={t('excelImportReview.actions.selectDataTypeTitle')}>
                    <div className="flex flex-wrap gap-2">
                        {availableTypes.map(type => (
                            <button
                                key={type}
                                onClick={() => handleRecordTypeChange(type)}
                                disabled={isLoading || isFetching || confirmMutation.isLoading || discardMutation.isLoading}
                                className={`px-4 py-2 rounded text-sm font-medium transition-colors disabled:opacity-50 ${
                                    type === recordType
                                        ? 'bg-blue-600 text-white'
                                        : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
                                }`}
                            >
                                {type} {/* Keep type name as is */}
                            </button>
                        ))}
                    </div>
                </Card>
            )}

            {/* Data Preview Section */}
            {recordType && availableTypes.includes(recordType) && (
                <>
                {/* i18n key used with interpolation */}
                    <Card title={t('excelImportReview.preview.title', { recordType, count: pagination.totalRecords || 0 })}>
                         {/* Loading indicator */}
                       {/* i18n key used */}
                        {(isLoading || (isFetching && currentPage !== pagination.currentPage)) && <p className="text-center text-gray-500 py-4">{t('excelImportReview.loading.loadingDataPage')}</p>}

                        {/* Error display */}
                         {isError && !isFetching && statusMessage.type === 'error' && (
                             <div className="text-center text-red-600 py-4">
                                {/* Already translated OR use fallback */}
                                 <p>{statusMessage.text || t('excelImportReview.errors.failedToLoadData', { recordType })}</p>
                                {/* i18n key used */}
                                 <Button onClick={() => refetch()} variant="outline" className="mt-2" size="sm">{t('excelImportReview.actions.retry')}</Button>
                             </div>
                         )}

                         {/* Data Table */}
                        {!isLoading && !isError && pendingData && tableHeaders.length > 0 && (
                            <SimpleTable headers={tableHeaders} data={pendingData} />
                        )}

                        {/* No data message */}
                        {!isLoading && !isFetching && !isError && (!pendingData || pendingData.length === 0) && !isProcessed && (
                            // i18n key used with interpolation
                            <p className="text-center text-gray-500 py-4">{t('excelImportReview.errors.noPendingRecords', { recordType })}</p>
                        )}

                        {/* Pagination */}
                        {!isError && pagination.totalPages > 1 && (
                            <Pagination
                                currentPage={pagination.currentPage}
                                totalPages={pagination.totalPages}
                                onPageChange={handlePageChange}
                                isLoading={isFetching}
                            />
                        )}
                    </Card>

                    {/* Confirmation/Discard Controls */}
                    {(!isProcessed || (isProcessed && statusMessage.type === 'error')) && pagination.totalRecords > 0 && (
                       // i18n key used with interpolation
                        <Card title={t('excelImportReview.actions.title', { recordType })}>
                            <div className="mb-4">
                                {/* i18n key used */}
                                <label className="font-bold block mb-2 text-sm text-gray-700">{t('excelImportReview.actions.commitModeLabel')}</label>
                                <div className="space-y-2">
                                    <label className="flex items-center">
                                        <input type="radio" name="commit_mode" value="replace" checked={commitMode === 'replace'} onChange={(e) => setCommitMode(e.target.value)}
                                            disabled={confirmMutation.isLoading || discardMutation.isLoading || isProcessed} className="mr-2 focus:ring-green-500 h-4 w-4 text-green-600 border-gray-300"/>
                                    {/* i18n keys used with interpolation */}
                                        {t('excelImportReview.actions.commitModeReplace')} <span className="text-red-600 font-semibold ml-1">{t('excelImportReview.actions.commitModeReplaceWarning', { recordType })}</span>
                                    </label>
                                    {/* Add other modes like 'upsert' if implemented, with their own labels/warnings */}
                                </div>
                            </div>

                            <div className="flex space-x-4 mt-4">
                                {/* i18n keys used */}
                                <Button onClick={handleConfirm} disabled={confirmMutation.isLoading || discardMutation.isLoading || isProcessed} variant="primary">
                                    {confirmMutation.isLoading ? t('excelImportReview.actions.confirming') : t('excelImportReview.actions.confirmImport')}
                                </Button>
                                {/* i18n keys used */}
                                <Button onClick={handleDiscard} disabled={confirmMutation.isLoading || discardMutation.isLoading || isProcessed} variant="danger">
                                    {discardMutation.isLoading ? t('excelImportReview.actions.discarding') : t('excelImportReview.actions.discardImport')}
                                </Button>
                             </div>
                             {isProcessed && statusMessage.type === 'error' && (
                                  // i18n key used
                                 <p className="mt-3 text-sm text-yellow-700">{t('excelImportReview.status.previousErrorRetryHint')}</p>
                             )}
                         </Card>
                     )}

                     {/* Message shown after successful processing */}
                     {isProcessed && statusMessage.type === 'success' && (
                          <Card>
                            {/* Already translated OR use fallback */}
                              <p className="text-green-700">{statusMessage.text || t('excelImportReview.status.processedSuccess', { recordType })}</p>
                          </Card>
                     )}
                 </>
             )}

            {/* i18n key used */}
            <Link to="/" className="mt-6 inline-block">
                <Button variant='outline'>{t('excelImportReview.actions.backToDashboard')}</Button>
            </Link>
        </div>
    );
};

export default ExcelImportReviewPage;