from ..extensions import db

class Constraint(db.Model):
    __tablename__ = 'constraints'
    __table_args__ = {'schema': 'ration_app'}
    
    id = db.Column(db.Integer, primary_key=True)
    ration_id = db.Column(db.In<PERSON>ger, db.<PERSON><PERSON>('ration_app.rations.id'))
    nutrient_name = db.Column(db.String(255), nullable=False)
    min_value = db.Column(db.Float)
    max_value = db.Column(db.Float)
    actual_value = db.Column(db.Float)
    
    def to_dict(self):
        return {
            'id': self.id,
            'ration_id': self.ration_id,
            'nutrient_name': self.nutrient_name,
            'min_value': self.min_value,
            'max_value': self.max_value,
            'actual_value': self.actual_value
        }