import { useState } from 'react'
import { Link } from 'react-router-dom'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Pagination from '../../components/ui/Pagination'
import { getFeeds } from '../../services/feedService'
import { formatPriceWithUnit } from '../../utils/formatters'

const FeedsListPage = () => {
  const { t, i18n } = useTranslation()
  const [currentPage, setCurrentPage] = useState(1)
  const ITEMS_PER_PAGE = 10

  // Use React Query with pagination
  const {
    data: feedsData = { feeds: [], page: 1, totalPages: 0, totalItems: 0 },
    isLoading,
    error,
    refetch,
    isFetching
  } = useQuery(
    ['feeds', currentPage, ITEMS_PER_PAGE],
    () => getFeeds(currentPage, ITEMS_PER_PAGE),
    { keepPreviousData: true }
  )

  // Extract data for convenience
  const { feeds = [], totalPages = 0, totalItems = 0 } = feedsData || {}

  // Handle page change
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage)
    window.scrollTo(0, 0) // Scroll to top when changing pages
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('feeds.library')}</h1>
        <Link to="/feeds/new">
          <Button>{t('feeds.addNew')}</Button>
        </Link>
      </div>

      {isLoading ? (
        <p className="text-center py-4">{t('common.loading')}</p>
      ) : error ? (
        <Card>
          <p className="text-red-500">{t('feeds.errorLoading')}: {error.message}</p>
          <Button onClick={() => refetch()} className="mt-2" variant="outline">{t('common.tryAgain')}</Button>
        </Card>
      ) : feeds.length === 0 ? (
        <Card>
          <div className="text-center py-4">
            <p className="text-gray-500 mb-4">{t('feeds.noFeedsFoundLong')}</p>
            <Link to="/feeds/new">
              <Button>{t('feeds.addNew')}</Button>
            </Link>
          </div>
        </Card>
      ) : (
        <>
          {/* Total feeds count */}
          <p className="mb-4 text-sm text-gray-500">
            {totalItems} {t('feeds.totalFeeds')}
          </p>
          <div className="grid grid-cols-1 gap-4">
            {feeds.map(feed => (
              <Card key={feed.id} className="hover:shadow-md transition-shadow duration-200">
                <Link to={`/feeds/${feed.id}`} className="block">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{feed.name}</h3>
                      <p className="text-sm text-gray-500">{t('feeds.dmShort')}: {(feed.dry_matter_percentage*100).toFixed(2)}% | {t('feeds.costShort')}: {formatPriceWithUnit(feed.cost_per_kg, 'kg', i18n.language)}</p>
                    </div>
                    <div>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {feed.is_public ? t('common.public') : t('common.private')}
                      </span>
                    </div>
                  </div>
                </Link>
              </Card>
            ))}
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            isLoading={isFetching}
          />
        </>
      )}
    </div>
  )
}

export default FeedsListPage