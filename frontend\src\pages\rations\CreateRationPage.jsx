import { useState, useEffect } from 'react'
import { useNavigate, Link, useParams } from 'react-router-dom'
import { useMutation, useQuery, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import Button from '../../components/ui/Button'

// Import service functions
import { createRation, getRationById, updateRation, formulateRation } from '../../services/rationService'
import { getFeeds } from '../../services/feedService'
import { getAnimalGroups } from '../../services/animalGroupService'
import { getNrcRequirements } from '../../services/nrcRequirementsService'
import { getNrcModelVersions } from '../../services/nrcCoefficientService'

// Import the new layout component
import RationFormulationLayout from '../../components/rations/RationFormulationLayout'

const CreateRationPage = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { id } = useParams() // Get ID from URL if editing existing ration
  const isEditMode = !!id

  // State
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    animal_group_id: '',
    feeds: [],
    constraints: []
  })
  const [errors, setErrors] = useState({})
  const [isFormulating, setIsFormulating] = useState(false)
  const [formulationResult, setFormulationResult] = useState(null)
  const [isSaved, setIsSaved] = useState(true) // Track if changes are saved
  const [selectedNrcModelId, setSelectedNrcModelId] = useState('') // For NRC model selection

  // Get available NRC model versions to set the first one as default
  const { data: modelVersionsData } = useQuery('nrc-model-versions', () => getNrcModelVersions(), {
    onSuccess: (data) => {
      if (data?.versions?.length > 0 && !selectedNrcModelId) {
        setSelectedNrcModelId(data.versions[0].id.toString())
      }
    }
  })
  const [optimizationSettings, setOptimizationSettings] = useState({
    objective: 'minimize_cost',
    maxIterations: 1000,
    tolerance: 0.001
  })

  // Queries
  const { data: feedsData = { feeds: [] }, isLoading: isLoadingFeeds } = useQuery('feeds', () => getFeeds(1, 100))
  const feeds = feedsData.feeds || []
  const { data: animalGroups = [], isLoading: isLoadingGroups } = useQuery('animal-groups', getAnimalGroups)

  // Fetch existing ration data if in edit mode
  const { data: existingRation, isLoading: isLoadingRation } = useQuery(
    ['ration', id],
    () => getRationById(id),
    {
      enabled: isEditMode,
      onSuccess: (data) => {
        if (data) {
          setFormData({
            id: data.id,
            name: data.name || '',
            description: data.description || '',
            animal_group_id: data.animal_group_id || '',
            feeds: data.feeds?.map(feed => ({
              feed_id: feed.feed_id,
              min_inclusion_percentage: feed.min_inclusion_percentage,
              max_inclusion_percentage: feed.max_inclusion_percentage,
              actual_inclusion_percentage: feed.actual_inclusion_percentage,
              cost_contribution: feed.cost_contribution
            })) || [],
            constraints: data.constraints?.map(constraint => ({
              nutrient_name: constraint.nutrient_name,
              min_value: constraint.min_value,
              max_value: constraint.max_value,
              actual_value: constraint.actual_value
            })) || []
          })

          if (data.last_formulated_at) {
            setFormulationResult({
              status: 'success',
              feeds: data.feeds.reduce((acc, feed) => {
                acc[feed.feed_id] = {
                  inclusion_percentage: feed.actual_inclusion_percentage,
                  cost_contribution: feed.cost_contribution
                }
                return acc
              }, {}),
              nutrients: data.constraints.reduce((acc, constraint) => {
                acc[constraint.nutrient_name] = {
                  actual_value: constraint.actual_value,
                  min_value: constraint.min_value,
                  max_value: constraint.max_value
                }
                return acc
              }, {}),
              total_cost: data.feeds.reduce((sum, feed) => sum + (feed.cost_contribution || 0), 0)
            })
          }
        }
      }
    }
  )

  // Fetch NRC requirements when animal group is selected or NRC model changes
  const { data: nrcData, refetch: refetchNrcData } = useQuery(
    ['nrc-requirements', formData.animal_group_id, selectedNrcModelId],
    () => getNrcRequirements(formData.animal_group_id, selectedNrcModelId),
    {
      enabled: !!formData.animal_group_id,
      onSuccess: (data) => {
        // If we have NRC requirements and no constraints yet, add them
        if (data?.nrc_requirements?.constraints && formData.constraints.length === 0) {
          // Map all constraints including DMI
          const nrcConstraints = Object.entries(data.nrc_requirements.constraints).map(([name, values]) => ({
            nutrient_name: name,
            min_value: values.min,
            max_value: values.max
          }))

          setFormData(prev => ({
            ...prev,
            constraints: nrcConstraints
          }))
          setIsSaved(false)
        }
      }
    }
  )

  // Effect to refetch NRC data when model changes
  useEffect(() => {
    if (formData.animal_group_id && selectedNrcModelId) {
      refetchNrcData();
    }
  }, [selectedNrcModelId, formData.animal_group_id, refetchNrcData]);

  // Mutations
  const createMutation = useMutation(createRation, {
    onSuccess: (data) => {
      queryClient.invalidateQueries('rations')
      setIsSaved(true)
      // Navigate to the ration details page or stay on this page with ID in URL
      navigate(`/rations/${data.ration.id}/edit`, { replace: true })
    }
  })

  const updateMutation = useMutation(
    (data) => updateRation(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['ration', id])
        queryClient.invalidateQueries('rations')
        setIsSaved(true)
      }
    }
  )

  const formulateMutation = useMutation(
    (settings) => formulateRation(id, {
      ...settings,
      nrc_model_id: selectedNrcModelId || undefined
    }),
    {
      onSuccess: (data) => {
        setFormulationResult(data.result)
        queryClient.invalidateQueries(['ration', id])
        setIsFormulating(false)
      },
      onError: (error) => {
        setIsFormulating(false)
      }
    }
  )

  // Mark as unsaved when form data changes
  useEffect(() => {
    setIsSaved(false)
  }, [formData])

  // Handler functions
  const handleFormDataChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }))
    }
  }

  const handleFeedChange = (feeds) => {
    setFormData(prev => ({
      ...prev,
      feeds
    }))
  }

  const handleConstraintChange = (constraints) => {
    setFormData(prev => ({
      ...prev,
      constraints
    }))
  }

  const handleNrcModelChange = (modelId) => {
    setSelectedNrcModelId(modelId);
  }

  const handleOptimizationSettingsChange = (settings) => {
    setOptimizationSettings(settings)
    // If we're in edit mode and the ration is already saved, formulate immediately
    if (isEditMode && isSaved) {
      setIsFormulating(true)
      formulateMutation.mutate(settings)
    }
  }

  const validate = () => {
    const newErrors = {}

    if (!formData.name) {
      newErrors.name = t('validation.nameRequired')
    }

    if (!formData.animal_group_id) {
      newErrors.animal_group_id = t('rations.animalGroupRequired')
    }

    if (formData.feeds.length === 0) {
      newErrors.feeds = t('rations.feedsRequired')
    }

    // Validate feeds
    formData.feeds.forEach((feed, index) => {
      if (feed.min_inclusion_percentage !== null && feed.max_inclusion_percentage !== null) {
        if (parseFloat(feed.min_inclusion_percentage) > parseFloat(feed.max_inclusion_percentage)) {
          newErrors[`feeds.${index}.min_inclusion_percentage`] = t('validation.minNotGreaterThanMax')
        }
      }
    })

    // Validate constraints
    formData.constraints.forEach((constraint, index) => {
      if (!constraint.nutrient_name) {
        newErrors[`constraints.${index}.nutrient_name`] = t('rations.nutrientNameRequired')
      }

      if (constraint.min_value !== null && constraint.max_value !== null) {
        if (parseFloat(constraint.min_value) > parseFloat(constraint.max_value)) {
          newErrors[`constraints.${index}.min_value`] = t('validation.minNotGreaterThanMax')
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (!validate()) {
      return
    }

    // Format numeric values
    const formattedData = {
      ...formData,
      feeds: formData.feeds.map(feed => ({
        ...feed,
        feed_id: parseInt(feed.feed_id),
        min_inclusion_percentage: feed.min_inclusion_percentage === '' ? null : parseFloat(feed.min_inclusion_percentage),
        max_inclusion_percentage: feed.max_inclusion_percentage === '' ? null : parseFloat(feed.max_inclusion_percentage)
      })),
      constraints: formData.constraints.map(constraint => ({
        ...constraint,
        min_value: constraint.min_value === '' ? null : parseFloat(constraint.min_value),
        max_value: constraint.max_value === '' ? null : parseFloat(constraint.max_value)
      }))
    }

    if (isEditMode) {
      updateMutation.mutate(formattedData)
    } else {
      createMutation.mutate(formattedData)
    }
  }

  const handleFormulate = () => {
    // Save first if there are unsaved changes
    if (!isSaved) {
      handleSave()
    }

    if (isEditMode) {
      setIsFormulating(true)
      formulateMutation.mutate(optimizationSettings)
    } else {
      // If new ration, save first then navigate to formulate
      handleSave()
    }
  }

  // Loading state
  const isLoading = isLoadingFeeds || isLoadingGroups || (isEditMode && isLoadingRation)

  if (isLoading) {
    return <p className="text-center py-4">{t('common.loading')}</p>
  }

  // Check if we can formulate
  const canFormulate = formData.feeds.length > 0 && formData.animal_group_id && isEditMode

  // Error handling for mutations
  if (createMutation.isError || updateMutation.isError || formulateMutation.isError) {
    const error = createMutation.error || updateMutation.error || formulateMutation.error;
    // We'll handle this in the layout
  }

  return (
    <div className="space-y-6">
      <RationFormulationLayout
        formData={formData}
        feeds={feeds}
        animalGroups={animalGroups}
        formulationResult={formulationResult}
        errors={errors}
        isFormulating={isFormulating}
        isSaving={createMutation.isLoading || updateMutation.isLoading}
        isSaved={isSaved}
        nrcData={nrcData}
        selectedNrcModelId={selectedNrcModelId}
        optimizationSettings={optimizationSettings}
        onSave={handleSave}
        onFormulate={handleFormulate}
        onNrcModelChange={handleNrcModelChange}
        onFormDataChange={handleFormDataChange}
        onFeedChange={handleFeedChange}
        onConstraintChange={handleConstraintChange}
        onOptimizationSettingsChange={handleOptimizationSettingsChange}
        canFormulate={canFormulate}
      />

      {/* Footer buttons for navigation */}
      <div className="flex justify-between">
        <Link to="/rations">
          <Button variant="outline">{t('common.cancel')}</Button>
        </Link>
      </div>
    </div>
  )
}

export default CreateRationPage