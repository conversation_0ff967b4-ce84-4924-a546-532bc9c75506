import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import {
  getEmbeddingStatus,
  reprocessEmbeddings,
  reprocessCompletedEmbeddings,
  regenerateEmbedding
} from '../../services/kbService';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';

export default function KBEmbeddingsTab() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [reembedLimit, setReembedLimit] = useState(50);
  const [processAll, setProcessAll] = useState(false);

  const { data: status = {}, isLoading } = useQuery('kb-embedding-status', getEmbeddingStatus, {
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const reprocessMut = useMutation(reprocessEmbeddings, {
    onSuccess: () => queryClient.invalidateQueries('kb-embedding-status'),
  });

  const reprocessCompletedMut = useMutation(
    () => reprocessCompletedEmbeddings(reembedLimit, null, processAll),
    {
      onSuccess: () => queryClient.invalidateQueries('kb-embedding-status'),
    }
  );

  const regenMut = useMutation(
    (id) => regenerateEmbedding(id),
    {
      onSuccess: () => queryClient.invalidateQueries('kb-embedding-status'),
    }
  );

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-16">
        <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
      </div>
    );
  }

  // Helper function to get status badge color
  const getStatusColor = (type) => {
    switch(type) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <Card
        title={t('kb.embeddingsStatus')}
        className="bg-white shadow-md hover:shadow-lg transition-shadow"
      >
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          {/* Status Cards */}
          <div className="bg-gray-50 p-4 rounded-lg text-center">
            <div className="text-3xl font-bold text-gray-800">{status.total || 0}</div>
            <div className="text-sm text-gray-500">{t('common.total')}</div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg text-center">
            <div className="text-3xl font-bold text-green-700">{status.completed || 0}</div>
            <div className="text-sm text-green-600">{t('kb.completed')}</div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg text-center">
            <div className="text-3xl font-bold text-yellow-700">{status.pending || 0}</div>
            <div className="text-sm text-yellow-600">{t('kb.pending')}</div>
          </div>

          <div className="bg-red-50 p-4 rounded-lg text-center">
            <div className="text-3xl font-bold text-red-700">{status.failed || 0}</div>
            <div className="text-sm text-red-600">{t('kb.failed')}</div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
          <div className="text-sm text-gray-600">
            {t('kb.embeddingsDescription')}
          </div>

          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="processAll"
                checked={processAll}
                onChange={(e) => setProcessAll(e.target.checked)}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="processAll" className="text-sm text-gray-700">
                {t('kb.processAllArticles')}
              </label>

              {!processAll && (
                <select
                  id="reprocessLimit"
                  className="ml-2 block rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                  value={reembedLimit}
                  onChange={(e) => setReembedLimit(Number(e.target.value))}
                  disabled={processAll}
                >
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                  <option value={200}>200</option>
                </select>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={() => reprocessMut.mutate()}
                disabled={reprocessMut.isLoading || status.failed === 0}
                className="whitespace-nowrap"
              >
                {reprocessMut.isLoading ? t('common.processing') : t('kb.reprocessFailed')}
              </Button>

              <Button
                onClick={() => reprocessCompletedMut.mutate()}
                disabled={reprocessCompletedMut.isLoading || status.completed === 0}
                className="whitespace-nowrap bg-orange-500 hover:bg-orange-600 text-white"
                title={t('kb.reembedCompletedTooltip')}
              >
                {reprocessCompletedMut.isLoading ? t('common.processing') : t('kb.reembedCompleted')}
              </Button>
            </div>
          </div>
        </div>

        {reprocessCompletedMut.isSuccess && (
          <div className="mt-4 p-3 bg-green-50 text-green-800 rounded-md text-sm">
            {t('kb.reembedSuccess', { count: reprocessCompletedMut.data?.count || 0 })}
          </div>
        )}
      </Card>

      {status.failed_ids?.length > 0 && (
        <Card
          title={t('kb.failedEmbeddings')}
          className="bg-white shadow-md hover:shadow-lg transition-shadow"
        >
          <div className="space-y-4">
            <p className="text-gray-600 text-sm">
              {t('kb.failedEmbeddingsDescription')}
            </p>

            <div className="flex flex-wrap gap-2">
              {status.failed_ids.map((id) => (
                <Button
                  key={id}
                  size="sm"
                  variant="outline"
                  onClick={() => regenMut.mutate(id)}
                  disabled={regenMut.isLoading}
                  className="bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
                >
                  {t('kb.regenerate')} #{id}
                </Button>
              ))}
            </div>
          </div>
        </Card>
      )}

      {status.processing > 0 && (
        <Card
          title={t('kb.processingEmbeddings')}
          className="bg-blue-50 border-blue-200 shadow-md"
        >
          <div className="flex items-center">
            <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mr-3" />
            <p className="text-blue-700">
              {t('kb.processingEmbeddingsDescription', { count: status.processing })}
            </p>
          </div>
        </Card>
      )}
    </div>
  );
}