# services/excel_service.py
import math
import re
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)

# --- Helper Functions ---

def get_col_index(col_str: str) -> int:
    """Converts Excel column letters (A, B, ..., Z, AA, AB, ...) to 0-based index."""
    index = 0
    power = 1
    if not isinstance(col_str, str): # Basic type check
         raise ValueError(f"Invalid column input type: {type(col_str)}")
    for char in reversed(col_str.upper()):
        char_val = ord(char) - ord('A') + 1
        if not 1 <= char_val <= 26:
            raise ValueError(f"Invalid column character: {char} in '{col_str}'")
        index += char_val * power
        power *= 26
    return index - 1 # Convert to 0-based

def parse_excel_range(range_str: str) -> tuple[int, int, int, int] | None:
    """
    Parses an Excel range string (e.g., "A1:D50", "B2") into 0-based indices.
    Returns (start_row, start_col, end_row, end_col) or None if invalid.
    """
    if not range_str or not isinstance(range_str, str):
        logger.error(f"Invalid range_str input: {range_str}")
        return None
    range_str = range_str.upper().strip()

    match_single = re.match(r'^([A-Z]+)([1-9][0-9]*)$', range_str)
    if match_single:
        col_str, row_str = match_single.groups()
        try:
            col = get_col_index(col_str)
            row = int(row_str) - 1
            return (row, col, row, col)
        except ValueError:
            logger.error(f"Error parsing single cell reference '{range_str}'")
            return None

    match_range = re.match(r'^([A-Z]+)([1-9][0-9]*):([A-Z]+)([1-9][0-9]*)$', range_str)
    if not match_range:
        logger.error(f"Invalid range format: '{range_str}'")
        return None

    start_col_str, start_row_str, end_col_str, end_row_str = match_range.groups()

    try:
        start_col = get_col_index(start_col_str)
        start_row = int(start_row_str) - 1
        end_col = get_col_index(end_col_str)
        end_row = int(end_row_str) - 1

        if start_row > end_row or start_col > end_col:
             start_row, end_row = min(start_row, end_row), max(start_row, end_row)
             start_col, end_col = min(start_col, end_col), max(start_col, end_col)

        return (start_row, start_col, end_row, end_col)
    except ValueError as e:
        logger.error(f"Error parsing range coordinates in '{range_str}': {e}")
        return None

def get_rows_and_cells_for_sampling(cluster_cells, parsed_range, keep_rows_each_end):
    """Gets cells from the first/last N rows defined by keep_rows_each_end."""
    if not cluster_cells: return [], set()
    start_row, _, end_row, _ = parsed_range
    sampled_cells = []
    row_indices_kept = set()

    # Determine target rows to keep
    first_n_rows = set(range(start_row, min(start_row + keep_rows_each_end, end_row + 1)))
    last_n_rows = set(range(max(end_row - keep_rows_each_end + 1, start_row), end_row + 1))
    target_rows_to_keep = first_n_rows.union(last_n_rows)

    # Group cells by row index for efficient lookup
    cells_by_row = defaultdict(list)
    for cell in cluster_cells:
        # Ensure cell is dict and has 'r' key
        if isinstance(cell, dict) and 'r' in cell:
            cells_by_row[cell['r']].append(cell)
        else:
            logger.warning(f"Skipping invalid cell object during sampling: {cell}")


    # Add cells from target rows
    for r_idx in sorted(list(target_rows_to_keep)): # Keep order
         if r_idx in cells_by_row:
              sampled_cells.extend(cells_by_row[r_idx])
              row_indices_kept.add(r_idx)

    return sampled_cells, row_indices_kept


# --- Main Sampling Function ---

def sample_excel_data(payload, worksheet_analysis=None, target_data_tokens=45000, tokens_per_cell=45, sample_large_clusters_keep_rows=3, small_cluster_cell_threshold=500, min_rows_after_sampling=3):
    """
    Samples cell data based on clusters to meet a target token budget, returning
    a structure similar to the input payload but with sampled 'data' arrays.

    Args:
        payload (dict): The full payload object {sourceSheetName, usedRangeAddress, clusters:[{id, range, data}]}
        worksheet_analysis (dict, optional): Metadata for logging.
        target_data_tokens (int): The desired token budget for the cell data sample.
        tokens_per_cell (int): Estimated tokens per cell object in JSON.
        sample_large_clusters_keep_rows (int): Min first/last rows for large clusters' minimum sample.
        small_cluster_cell_threshold (int): Max cells for a cluster to be kept fully (unless sampled in Case B).
        min_rows_after_sampling (int): Minimum number of rows to keep in a small cluster if sampled in Case B.

    Returns:
        dict: A dictionary mimicking the input payload structure, but with sampled data arrays within clusters.
    """
    source_sheet_name = payload.get("sourceSheetName", "UnknownSheet")
    used_range_address = payload.get("usedRangeAddress", "UnknownRange")
    clusters_in = payload.get('clusters', [])

    # Basic input checks
    if not isinstance(clusters_in, list): return {"sourceSheetName": source_sheet_name, "usedRangeAddress": used_range_address, "clusters": []}

    # Flatten the initial data for easier processing and filtering valid cells
    all_valid_cells_by_cluster = defaultdict(list)
    cluster_meta = {} # Store metadata like range, num_cols, row_indices
    sampled_clusters_output = []
    
    for cluster in clusters_in:
        cluster_id = cluster.get('id')
        cluster_range_str = cluster.get('range')
        cluster_data = cluster.get('data')

        if cluster_id is None or not cluster_range_str or not isinstance(cluster_data, list):
             logger.warning(f"[{source_sheet_name}] Skipping invalid cluster structure: {cluster.get('id', 'N/A')}")
             continue

        parsed_range = parse_excel_range(cluster_range_str)
        if not parsed_range:
            logger.warning(f"[{source_sheet_name}] Could not parse range '{cluster_range_str}' for cluster {cluster_id}.")
            continue

        num_cols = parsed_range[3] - parsed_range[1] + 1
        cluster_rows = set()
        valid_cell_count = 0

        # Iterate through rows and cells, flatten and validate
        flat_cells_for_cluster = []
        for r_idx, row_list in enumerate(cluster_data):
             if isinstance(row_list, list):
                  current_row_index = -1 # Placeholder
                  for c_idx, cell in enumerate(row_list):
                       if isinstance(cell, dict) and 'r' in cell and 'c' in cell:
                            flat_cells_for_cluster.append(cell)
                            valid_cell_count += 1
                            current_row_index = cell['r'] # Get actual row index from cell
                            cluster_rows.add(current_row_index)
                       # else: skip invalid cell object within row
             # else: skip invalid row structure

        if valid_cell_count > 0:
             all_valid_cells_by_cluster[cluster_id] = flat_cells_for_cluster
             cluster_meta[cluster_id] = {
                 "id": cluster_id,
                 "range_str": cluster_range_str,
                 "parsed_range": parsed_range,
                 "num_cols": num_cols,
                 "actual_cell_count": valid_cell_count,
                 "estimated_tokens": valid_cell_count * tokens_per_cell,
                 "row_indices": sorted(list(cluster_rows)),
                 "num_rows": len(cluster_rows)
             }
        else:
             logger.info(f"[{source_sheet_name}] Cluster {cluster_id} has no valid cells after parsing.")


    if not cluster_meta:
         logger.warning(f"[{source_sheet_name}] No valid clusters found after parsing.")
         return {"sourceSheetName": source_sheet_name, "usedRangeAddress": used_range_address, "clusters": []}

    # --- Now proceed with logic using cluster_meta and all_valid_cells_by_cluster ---

    logger.info(f"[{source_sheet_name}] Starting sampling. Target: {target_data_tokens} tokens, CellEst: {tokens_per_cell}, SmallThresh: {small_cluster_cell_threshold} cells, LargeKeepRows: {sample_large_clusters_keep_rows}, SmallMinKeepRows: {min_rows_after_sampling}")

    small_clusters_meta = []
    large_clusters_meta = []
    current_total_tokens = 0


    for cid, meta in cluster_meta.items():
        current_total_tokens += meta['estimated_tokens']
        # A cluster is small only if it meets cell threshold AND has >= min rows required
        if meta['actual_cell_count'] <= small_cluster_cell_threshold and meta['num_rows'] >= min_rows_after_sampling:
             small_clusters_meta.append(meta)
        elif meta['actual_cell_count'] > 0: # Must have cells to be large
             large_clusters_meta.append(meta)

    logger.info(f"[{source_sheet_name}] Initial estimate: {current_total_tokens} tokens. Small: {len(small_clusters_meta)}, Large: {len(large_clusters_meta)}.")

    # --- Check if sampling is needed ---
    if current_total_tokens <= target_data_tokens:
        logger.info(f"[{source_sheet_name}] Within budget. No sampling needed.")
        # Build output using all original valid data, structured
        sampled_clusters_output = []
        for cid, meta in cluster_meta.items():
             # Reconstruct 2D array from flat list for this cluster
             cells_2d = defaultdict(list)
             for cell in all_valid_cells_by_cluster[cid]:
                  cells_2d[cell['r']].append(cell)
             # Sort columns within each row
             final_data_2d = []
             for r_idx in sorted(cells_2d.keys()):
                  cells_2d[r_idx].sort(key=lambda c: c.get('c', -1))
                  final_data_2d.append(cells_2d[r_idx])

             sampled_clusters_output.append({
                 "id": cid,
                 "range": meta['range_str'],
                 "data": final_data_2d # Use the full 2D data
             })
        return {"sourceSheetName": source_sheet_name, "usedRangeAddress": used_range_address, "clusters": sampled_clusters_output}


    # --- Sampling is Needed ---
    logger.info(f"[{source_sheet_name}] Exceeds target tokens ({current_total_tokens} > {target_data_tokens}). Applying dynamic sampling.")

    # Calculate costs for minimums
    small_cluster_cost = sum(d['estimated_tokens'] for d in small_clusters_meta)
    large_clusters_min_samples_cells = {} # {cluster_id: [cells]}
    large_clusters_min_samples_rows = {} # {cluster_id: set(row_indices)}
    large_clusters_min_cost = 0
    for meta in large_clusters_meta:
        min_sample_cells, min_sample_rows = get_rows_and_cells_for_sampling(
            all_valid_cells_by_cluster[meta['id']],
            meta['parsed_range'],
            sample_large_clusters_keep_rows
        )
        large_clusters_min_samples_cells[meta['id']] = min_sample_cells
        large_clusters_min_samples_rows[meta['id']] = min_sample_rows
        large_clusters_min_cost += len(min_sample_cells) * tokens_per_cell

    total_min_cost_estimate = small_cluster_cost + large_clusters_min_cost
    

    if total_min_cost_estimate <= target_data_tokens:
        # --- Case A: Budget Sufficient for Minimums ---
        logger.info(f"[{source_sheet_name}] Budget OK for minimums ({total_min_cost_estimate} <= {target_data_tokens}). Adding minimums + distributing excess by row.")
        temp_final_cells = {} # Store cells by cluster_id temporarily {cid: {(r,c): cell}}

        # Add all small cluster cells
        for meta in small_clusters_meta:
             cluster_id = meta['id']
             temp_final_cells[cluster_id] = {(c['r'], c['c']): c for c in all_valid_cells_by_cluster[cluster_id]}

        # Add minimum samples from large clusters
        for cluster_id, cells in large_clusters_min_samples_cells.items():
             if cluster_id not in temp_final_cells: temp_final_cells[cluster_id] = {}
             for cell in cells: temp_final_cells[cluster_id][(cell['r'], cell['c'])] = cell

        # Calculate remaining budget and distribute by adding full rows
        current_cost = sum(len(cells) * tokens_per_cell for cells in temp_final_cells.values())
        remaining_budget = target_data_tokens - current_cost
        logger.debug(f"[{source_sheet_name}] Distributing remaining budget: {remaining_budget} tokens by adding rows.")

        large_clusters_meta.sort(key=lambda x: x['actual_cell_count'], reverse=True)
        made_progress = True
        while remaining_budget > 0 and made_progress:
            made_progress = False
            for meta in large_clusters_meta:
                cluster_id = meta['id']
                cost_per_row = meta['num_cols'] * tokens_per_cell
                if cost_per_row <= 0 or remaining_budget < cost_per_row: continue # Skip if row cost invalid or too high

                # Find next available row not already included
                rows_in_sample = set(r for r, c in temp_final_cells.get(cluster_id, {}).keys())
                available_rows = [r for r in meta['row_indices'] if r not in rows_in_sample]

                if available_rows:
                    row_to_add = available_rows[0] # Simple: add the first available one
                    # Find all original cells for this row in this cluster
                    cells_for_row_to_add = [c for c in all_valid_cells_by_cluster[cluster_id] if c['r'] == row_to_add]
                    actual_row_cost = len(cells_for_row_to_add) * tokens_per_cell

                    if remaining_budget >= actual_row_cost:
                         if cluster_id not in temp_final_cells: temp_final_cells[cluster_id] = {}
                         for cell in cells_for_row_to_add: temp_final_cells[cluster_id][(cell['r'], cell['c'])] = cell
                         remaining_budget -= actual_row_cost
                         made_progress = True
                         logger.debug(f"[{source_sheet_name}] Added row {row_to_add} from cluster {cluster_id}. Remaining budget: {remaining_budget}")
                    # else: Cannot afford this row's actual cost

                if remaining_budget <= 0: break # Exit inner loop

        # Build final output structure from temp_final_cells
        for cid, cell_dict in temp_final_cells.items():
             # Reconstruct 2D data for this cluster
             cells_2d = defaultdict(list)
             for cell in cell_dict.values(): cells_2d[cell['r']].append(cell)
             final_data_2d = []
             for r_idx in sorted(cells_2d.keys()):
                  cells_2d[r_idx].sort(key=lambda c: c.get('c', -1))
                  final_data_2d.append(cells_2d[r_idx])

             sampled_clusters_output.append({
                 "id": cid,
                 "range": cluster_meta[cid]['range_str'],
                 "data": final_data_2d
             })

    else:
        # --- Case B: Budget Insufficient for Minimums ---
        deficit = total_min_cost_estimate - target_data_tokens
        logger.warning(f"[{source_sheet_name}] Budget insufficient for minimums by ~{deficit} tokens. Adding large minimums and sampling small clusters by removing rows.")

        # Need mutable copy of cells for small clusters to track removals
        current_small_cluster_data = {} # {cid: {(r, c): cell}}
        small_clusters_meta.sort(key=lambda x: x['actual_cell_count'], reverse=True)
        small_clusters_to_process = [meta['id'] for meta in small_clusters_meta] # IDs

        for meta in small_clusters_meta:
             current_small_cluster_data[meta['id']] = {(c['r'], c['c']): c for c in all_valid_cells_by_cluster[meta['id']]}

        # Use final_sampled_cells_dict to build the result, starting with large minimums
        final_sampled_cells_dict = {} # {cid: {(r,c): cell}}
        for cid, cells in large_clusters_min_samples_cells.items():
             final_sampled_cells_dict[cid] = {(c['r'], c['c']): c for c in cells}

        # Add initial small clusters data
        for cid, cell_dict in current_small_cluster_data.items():
             if cid not in final_sampled_cells_dict: final_sampled_cells_dict[cid] = {}
             final_sampled_cells_dict[cid].update(cell_dict) # Add all initially

        # Iteratively remove rows from largest small clusters
        processed_small_cluster_ids = set() # Track which ones we tried removing from
        while deficit > 0 and len(processed_small_cluster_ids) < len(small_clusters_meta):
            # Find the next largest small cluster *we haven't exhausted*
            target_cluster_meta = None
            for meta in small_clusters_meta: # Iterate in sorted order
                 if meta['id'] not in processed_small_cluster_ids:
                      target_cluster_meta = meta
                      break

            if not target_cluster_meta: break # No more clusters to process

            cluster_id = target_cluster_meta['id']
            num_cols = target_cluster_meta['num_cols']
            cost_per_row_estimate = num_cols * tokens_per_cell
            current_rows_in_sample = sorted(list(set(r for r,c in final_sampled_cells_dict.get(cluster_id, {}).keys())))

            if len(current_rows_in_sample) > min_rows_after_sampling and cost_per_row_estimate > 0:
                # Remove a row (e.g., middle one or last one)
                row_to_remove = current_rows_in_sample[len(current_rows_in_sample) // 2] # Middle-ish
                # Find actual cells to remove from the final dict
                cells_removed_count = 0
                keys_to_remove = []
                if cluster_id in final_sampled_cells_dict:
                     for r_c_key in final_sampled_cells_dict[cluster_id]:
                          if r_c_key[0] == row_to_remove:
                               keys_to_remove.append(r_c_key)

                if keys_to_remove:
                     for key in keys_to_remove:
                          del final_sampled_cells_dict[cluster_id][key]
                          cells_removed_count += 1

                     actual_tokens_saved = cells_removed_count * tokens_per_cell
                     deficit -= actual_tokens_saved
                     logger.debug(f"[{source_sheet_name}] Removed {cells_removed_count} cells from row {row_to_remove} in small cluster {cluster_id}. Deficit remaining: ~{deficit}")
                     # Check if this cluster still has enough rows, if not mark as processed
                     current_rows_now = set(r for r,c in final_sampled_cells_dict.get(cluster_id, {}).keys())
                     if len(current_rows_now) <= min_rows_after_sampling:
                          processed_small_cluster_ids.add(cluster_id)
                          logger.debug(f"[{source_sheet_name}] Cluster {cluster_id} reached min rows ({min_rows_after_sampling}) after removal.")

                else: # No cells found for that row (shouldn't happen?), mark processed
                     processed_small_cluster_ids.add(cluster_id)

            else: # Cannot remove more rows from this cluster
                processed_small_cluster_ids.add(cluster_id)


        if deficit > 0:
            logger.warning(f"[{source_sheet_name}] Could not meet budget after sampling small clusters. Final estimate will exceed target by ~{deficit} tokens.")

        # Build final output structure from final_sampled_cells_dict
        for cid, cell_dict in final_sampled_cells_dict.items():
             # Reconstruct 2D data for this cluster
             cells_2d = defaultdict(list)
             for cell in cell_dict.values(): cells_2d[cell['r']].append(cell)
             final_data_2d = []
             for r_idx in sorted(cells_2d.keys()):
                  cells_2d[r_idx].sort(key=lambda c: c.get('c', -1))
                  final_data_2d.append(cells_2d[r_idx])

             sampled_clusters_output.append({
                 "id": cid,
                 "range": cluster_meta[cid]['range_str'], # Get range from stored meta
                 "data": final_data_2d
             })

    # --- 5. Finalize and Return ---
    sampled_clusters_output.sort(key=lambda x: x.get('id', -1)) # Sort output by cluster ID
    final_cell_count = sum(len(row) for cluster in sampled_clusters_output for row in cluster.get('data', []))
    final_token_estimate = final_cell_count * tokens_per_cell

    logger.info(f"[{source_sheet_name}] Sampling complete. Final estimated tokens: {final_token_estimate} ({final_cell_count} cells across {len(sampled_clusters_output)} clusters). Target was: {target_data_tokens}")

    # Construct the final payload structure
    final_sample_payload = {
        "sourceSheetName": source_sheet_name,
        "usedRangeAddress": used_range_address,
        "clusters": sampled_clusters_output
    }
    return final_sample_payload