import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom'
import { useMutation, useQuery, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Input from '../../components/ui/Input'
import { createAnimalGroup, getAnimalGroupById, updateAnimalGroup } from '../../services/animalGroupService'
import { getHerds } from '../../services/herdService'

const CreateAnimalGroupPage = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const isEditMode = !!id

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    weight_kg: '',
    bcs: '',
    milk_production_kg: '',
    days_in_milk: '',
    lactation_number: ''
  })

  const [errors, setErrors] = useState({})
  const [selectedHerds, setSelectedHerds] = useState([])

  // Fetch herds for assignment
  const { data: herdsData = { herds: [] } } = useQuery(
    'herds',
    () => getHerds()
  )

  // Fetch group data if in edit mode
  const { data: groupData } = useQuery(
    ['animal-group', id],
    () => getAnimalGroupById(id),
    {
      enabled: isEditMode,
      onSuccess: (data) => {
        if (data && data.animalGroup) {
          setFormData({
            name: data.animalGroup.name || '',
            description: data.animalGroup.description || '',
            weight_kg: data.animalGroup.weight_kg !== null ? data.animalGroup.weight_kg : '',
            bcs: data.animalGroup.bcs !== null ? data.animalGroup.bcs : '',
            milk_production_kg: data.animalGroup.milk_production_kg !== null ? data.animalGroup.milk_production_kg : '',
            days_in_milk: data.animalGroup.days_in_milk !== null ? data.animalGroup.days_in_milk : '',
            lactation_number: data.animalGroup.lactation_number !== null ? data.animalGroup.lactation_number : ''
          })

          // Set selected herds if available
          if (data.herds) {
            setSelectedHerds(data.herds.map(herd => herd.id))
          }
        }
      }
    }
  )

  const createMutation = useMutation(createAnimalGroup, {
    onSuccess: (data) => {
      queryClient.invalidateQueries('animal-groups')
      navigate(`/animal-groups/${data.animal_group.id}`)
    }
  })

  const updateMutation = useMutation((data) => updateAnimalGroup(id, data), {
    onSuccess: () => {
      queryClient.invalidateQueries(['animal-group', id])
      queryClient.invalidateQueries('animal-groups')
      navigate(`/animal-groups/${id}`)
    }
  })

  const handleChange = (e) => {
    const { name, value, type } = e.target
    let processedValue = value

    // Handle numeric inputs
    if (type === 'number') {
      processedValue = value === '' ? '' : Number(value)
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }))

    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }))
    }
  }

  const handleHerdToggle = (herdId) => {
    setSelectedHerds(prev => {
      if (prev.includes(herdId)) {
        return prev.filter(id => id !== herdId)
      } else {
        return [...prev, herdId]
      }
    })
  }

  const validate = () => {
    const newErrors = {}

    if (!formData.name) {
      newErrors.name = t('validation.nameRequired')
    }

    // Validate numeric fields
    if (formData.weight_kg && isNaN(Number(formData.weight_kg))) {
      newErrors.weight_kg = t('validation.mustBeNumber')
    }

    if (formData.bcs && isNaN(Number(formData.bcs))) {
      newErrors.bcs = t('validation.mustBeNumber')
    }

    if (formData.milk_production_kg && isNaN(Number(formData.milk_production_kg))) {
      newErrors.milk_production_kg = t('validation.mustBeNumber')
    }

    if (formData.days_in_milk && isNaN(Number(formData.days_in_milk))) {
      newErrors.days_in_milk = t('validation.mustBeNumber')
    }

    if (formData.lactation_number && isNaN(Number(formData.lactation_number))) {
      newErrors.lactation_number = t('validation.mustBeNumber')
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (!validate()) {
      return
    }

    // Format data for submission
    const formattedData = { ...formData }

    // Convert empty strings to null for numeric fields
    const numericFields = ['weight_kg', 'bcs', 'milk_production_kg', 'days_in_milk', 'lactation_number']

    numericFields.forEach(field => {
      if (formattedData[field] === '') {
        formattedData[field] = null
      }
    })

    if (isEditMode) {
      updateMutation.mutate(formattedData)
    } else {
      createMutation.mutate(formattedData)
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? t('animalGroups.editGroup') : t('animalGroups.addNew')}
        </h1>
      </div>

      <form onSubmit={handleSubmit}>
        <Card title={t('animalGroups.basicInformation')}>
          <div className="space-y-4">
            <Input
              id="name"
              name="name"
              label={t('animalGroups.groupName')}
              value={formData.name}
              onChange={handleChange}
              error={errors.name}
              required
            />

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.description')}
              </label>
              <textarea
                id="description"
                name="description"
                rows="3"
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                value={formData.description}
                onChange={handleChange}
              ></textarea>
            </div>
          </div>
        </Card>

        <Card title={t('animalGroups.groupDetails')} className="mt-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                id="weight_kg"
                name="weight_kg"
                label={t('animalGroups.avgWeight')}
                type="number"
                step="0.1"
                value={formData.weight_kg}
                onChange={handleChange}
                error={errors.weight_kg}
              />

              <Input
                id="bcs"
                name="bcs"
                label={t('animalGroups.bodyConditionScore')}
                type="number"
                step="0.1"
                min="1"
                max="5"
                value={formData.bcs}
                onChange={handleChange}
                error={errors.bcs}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                id="milk_production_kg"
                name="milk_production_kg"
                label={t('animalGroups.dailyMilkProduction')}
                type="number"
                step="0.1"
                value={formData.milk_production_kg}
                onChange={handleChange}
                error={errors.milk_production_kg}
              />

              <Input
                id="days_in_milk"
                name="days_in_milk"
                label={t('animalGroups.daysInMilk')}
                type="number"
                value={formData.days_in_milk}
                onChange={handleChange}
                error={errors.days_in_milk}
              />

              <Input
                id="lactation_number"
                name="lactation_number"
                label={t('animalGroups.lactationNumber')}
                type="number"
                step="0.1"
                value={formData.lactation_number}
                onChange={handleChange}
                error={errors.lactation_number}
              />
            </div>
          </div>
        </Card>

        {isEditMode && (
          <Card title={t('animalGroups.assignAnimals')} className="mt-6">
            <div className="space-y-4">
              {herdsData.herds.length === 0 ? (
                <p className="text-gray-500 text-center">{t('animalGroups.noAnimalsToAssign')}</p>
              ) : (
                <>
                  <p className="text-sm text-gray-500">
                    {t('animalGroups.assignAnimalsDescription')}
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {herdsData.herds.map(herd => (
                      <div key={herd.id} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50">
                        <input
                          type="checkbox"
                          id={`herd-${herd.id}`}
                          checked={selectedHerds.includes(herd.id)}
                          onChange={() => handleHerdToggle(herd.id)}
                          className="h-4 w-4 text-green-600 rounded"
                        />
                        <label htmlFor={`herd-${herd.id}`} className="text-sm cursor-pointer flex-1">
                          {herd.ear_num ? `#${herd.ear_num}` : t('herds.unnamed')}
                          {herd.name && ` - ${herd.name}`}
                        </label>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </Card>
        )}

        {(createMutation.isError || updateMutation.isError) && (
          <div className="mt-6 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  {(createMutation.error || updateMutation.error)?.response?.data?.message || t('common.errorOccurred')}
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 flex justify-end space-x-3">
          <Link to="/animal-groups">
            <Button variant="outline">{t('common.cancel')}</Button>
          </Link>
          <Button
            type="submit"
            disabled={createMutation.isLoading || updateMutation.isLoading}
          >
            {createMutation.isLoading || updateMutation.isLoading ? t('common.saving') : isEditMode ? t('animalGroups.updateGroup') : t('animalGroups.saveGroup')}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default CreateAnimalGroupPage