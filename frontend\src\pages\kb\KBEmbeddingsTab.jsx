// src/pages/kb/KBEmbeddingsTab.jsx
import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'

import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import { getEmbeddingStatus, reprocessEmbeddings, regenerateEmbedding } from '../../services/kbService'

// Status badge component for embedding status
const StatusBadge = ({ status, count }) => {
  const { t } = useTranslation()
  
  // Define colors based on status
  const colorMap = {
    completed: 'bg-green-100 text-green-800',
    pending: 'bg-blue-100 text-blue-800',
    processing: 'bg-yellow-100 text-yellow-800',
    failed: 'bg-red-100 text-red-800'
  }
  
  const statusText = {
    completed: t('kb.completed'),
    pending: t('kb.pending'),
    processing: t('kb.processing'),
    failed: t('kb.failed')
  }
  
  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${colorMap[status] || 'bg-gray-100 text-gray-800'}`}>
      {statusText[status] || status}: {count}
    </span>
  )
}

const KBEmbeddingsTab = () => {
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const [isProcessing, setIsProcessing] = useState(false)
  const [reprocessLimit, setReprocessLimit] = useState(50)
  const [showInfo, setShowInfo] = useState(false)
  
  // Get embedding status
  const { 
    data: embeddingStatus = {}, 
    isLoading,
    refetch: refetchStatus
  } = useQuery('kb-embedding-status', getEmbeddingStatus, {
    refetchInterval: 30000, // Refresh every 30 seconds
  })
  
  // Reprocess failed embeddings mutation
  const reprocessMutation = useMutation(reprocessEmbeddings, {
    onSuccess: (data) => {
      setIsProcessing(false)
      queryClient.invalidateQueries('kb-embedding-status')
    },
    onError: () => {
      setIsProcessing(false)
    }
  })
  
  // Handle reprocess
  const handleReprocess = () => {
    setIsProcessing(true)
    reprocessMutation.mutate(reprocessLimit)
  }
  
  // Calculate percentages for progress bars
  const calculatePercentage = (status) => {
    const total = embeddingStatus.total || 0
    if (total === 0) return 0
    return Math.round((embeddingStatus[status] || 0) * 100 / total)
  }

  // Auto-refresh when processing is happening
  useEffect(() => {
    let intervalId = null;
    
    if ((embeddingStatus.processing > 0 || embeddingStatus.pending > 0) && !isLoading) {
      // Set up more frequent polling when there's active processing
      intervalId = setInterval(() => {
        refetchStatus();
      }, 5000); // Refresh every 5 seconds during active processing
    }
    
    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [embeddingStatus, isLoading, refetchStatus]);

  return (
    <div className="space-y-6">
      <Card title={t('kb.embeddingsOverview')}>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <div className="flex gap-2 flex-wrap mb-4 md:mb-0">
            {Object.entries(embeddingStatus)
              .filter(([key]) => key !== 'total')
              .map(([status, count]) => (
                <StatusBadge key={status} status={status} count={count} />
              ))}
            {embeddingStatus.total > 0 && (
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {t('common.total')}: {embeddingStatus.total}
              </span>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={() => refetchStatus()} 
              variant="outline" 
              disabled={isLoading}
              size="sm"
            >
              {isLoading ? t('common.refreshing') : t('common.refresh')}
            </Button>
            
            <Button
              onClick={() => setShowInfo(!showInfo)}
              variant="outline"
              size="sm"
            >
              {showInfo ? t('common.hideInfo') : t('common.showInfo')}
            </Button>
          </div>
        </div>
        
        {showInfo && (
          <div className="mb-6 bg-blue-50 p-4 rounded-lg text-sm">
            <h3 className="font-medium text-blue-800 mb-2">{t('kb.embeddingsInfo')}</h3>
            <p className="text-blue-700 mb-2">
              {t('kb.embeddingsDescription')}
            </p>
            <ul className="list-disc pl-5 text-blue-700 space-y-1">
              <li>{t('kb.embeddingsPending')}</li>
              <li>{t('kb.embeddingsProcessing')}</li>
              <li>{t('kb.embeddingsCompleted')}</li>
              <li>{t('kb.embeddingsFailed')}</li>
            </ul>
          </div>
        )}
        
        <div className="space-y-6 mt-6">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">{t('kb.completedEmbeddings')}</span>
              <span className="text-sm font-medium text-gray-500">
                {embeddingStatus.completed || 0} / {embeddingStatus.total || 0}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                className="bg-green-600 h-2.5 rounded-full" 
                style={{ width: `${calculatePercentage('completed')}%` }}
              ></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">{t('kb.pendingEmbeddings')}</span>
              <span className="text-sm font-medium text-gray-500">
                {embeddingStatus.pending || 0} / {embeddingStatus.total || 0}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                className="bg-blue-600 h-2.5 rounded-full" 
                style={{ width: `${calculatePercentage('pending')}%` }}
              ></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">{t('kb.processingEmbeddings')}</span>
              <span className="text-sm font-medium text-gray-500">
                {embeddingStatus.processing || 0} / {embeddingStatus.total || 0}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                className="bg-yellow-400 h-2.5 rounded-full" 
                style={{ width: `${calculatePercentage('processing')}%` }}
              ></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">{t('kb.failedEmbeddings')}</span>
              <span className="text-sm font-medium text-gray-500">
                {embeddingStatus.failed || 0} / {embeddingStatus.total || 0}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                className="bg-red-600 h-2.5 rounded-full" 
                style={{ width: `${calculatePercentage('failed')}%` }}
              ></div>
            </div>
          </div>
        </div>
      </Card>
      
      {embeddingStatus.failed > 0 && (
        <Card title={t('kb.reprocessFailedEmbeddings')}>
          <div className="space-y-4">
            <p className="text-gray-600">
              {t('kb.reprocessDescription')}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="w-full sm:w-1/3">
                <label htmlFor="reprocessLimit" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('kb.reprocessLimit')}
                </label>
                <select
                  id="reprocessLimit"
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                  value={reprocessLimit}
                  onChange={(e) => setReprocessLimit(Number(e.target.value))}
                >
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                  <option value={200}>200</option>
                </select>
              </div>
              
              <div className="w-full sm:w-2/3 flex justify-end">
                <Button 
                  onClick={handleReprocess} 
                  disabled={isProcessing || isLoading || !(embeddingStatus.failed > 0)}
                  variant="primary"
                >
                  {isProcessing 
                    ? t('kb.processingEmbeddings')
                    : t('kb.reprocessFailedEmbeddings')
                  }
                </Button>
              </div>
            </div>
            
            {reprocessMutation.isSuccess && (
              <div className="mt-4 p-4 bg-green-50 text-green-800 rounded-md">
                {t('kb.reprocessSuccess', { count: reprocessMutation.data?.count || 0 })}
              </div>
            )}
            
            {reprocessMutation.isError && (
              <div className="mt-4 p-4 bg-red-50 text-red-800 rounded-md">
                {t('kb.reprocessError')}
              </div>
            )}
          </div>
        </Card>
      )}
      
      {embeddingStatus.pending > 0 && (
        <Card title={t('kb.pendingJobs')}>
          <div className="space-y-4">
            <p className="text-gray-600">
              {t('kb.pendingJobsDescription')}
            </p>
            
            <div className="flex items-center justify-between">
              <div className="text-sm">
                <span className="font-medium">{embeddingStatus.pending}</span> {t('kb.pendingJobsCount')}
              </div>
              
              <div className="text-sm">
                <span className="font-medium">{embeddingStatus.processing}</span> {t('kb.processingJobsCount')}
              </div>
            </div>
            
            {(embeddingStatus.pending > 0 || embeddingStatus.processing > 0) && (
              <div className="mt-4 p-4 bg-blue-50 text-blue-800 rounded-md">
                {t('kb.jobsInProgressMessage')}
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  )
}

export default KBEmbeddingsTab