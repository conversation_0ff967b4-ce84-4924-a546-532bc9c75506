import React from 'react'
import { useTranslation } from 'react-i18next'
import { useQuery } from 'react-query'
import { getNrcModelVersions } from '../../services/nrcCoefficientService'

const NrcModelSelector = ({ selectedModelId, onModelChange }) => {
  const { t } = useTranslation()

  // Fetch available NRC model versions
  const { data: modelVersionsData, isLoading } = useQuery('nrc-model-versions', getNrcModelVersions)

  // Extract model versions without a default option
  const modelVersions = [
    ...((modelVersionsData?.versions || []).map(version => ({
      value: version.id.toString(), // Ensure ID is a string for consistency
      label: version.name,
      description: version.description,
      isActive: version.is_active
    })))
  ]

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {t('nrc.selectModelVersion')}
      </label>
      <select
        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
        value={selectedModelId}
        onChange={(e) => {
          // Only trigger change if the value is actually different
          if (e.target.value !== selectedModelId) {
            onModelChange(e.target.value);
          }
        }}
        disabled={isLoading}
      >
        {modelVersions.map(model => (
          <option key={model.value} value={model.value}>
            {model.label} {model.isActive ? t('nrc.activeModel') : ''}
          </option>
        ))}
      </select>

      {selectedModelId && (
        <div className="mt-2 p-3 bg-blue-50 rounded-md text-sm">
          <h4 className="font-medium text-blue-800">
            {modelVersions.find(m => m.value === selectedModelId)?.label || t('nrc.selectedModel')}
          </h4>
          <p className="mt-1 text-xs text-blue-700">
            {modelVersions.find(m => m.value === selectedModelId)?.description || t('nrc.noDescriptionAvailable')}
          </p>
        </div>
      )}
    </div>
  )
}

export default NrcModelSelector