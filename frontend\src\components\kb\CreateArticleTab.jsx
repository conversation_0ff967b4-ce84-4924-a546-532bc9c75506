import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { getCategories, createArticle } from '../../services/kbService';
import Card from '../../components/ui/Card';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';
import KBArticleContent from './KBArticleContent';

export default function CreateArticleTab() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({ title: '', slug: '', content: '', summary: '', category_id: '', tags: [], published: true });
  const [errors, setErrors] = useState({});
  const [preview, setPreview] = useState(false);
  const [tagInput, setTagInput] = useState('');

  const { data: categories = [], isLoading: catLoading } = useQuery('kb-admin-categories', () => getCategories());
  const createMut = useMutation(createArticle, {
    onSuccess: () => { queryClient.invalidateQueries('kb-admin-articles'); queryClient.invalidateQueries('kb-stats'); reset(); }
  });

  function reset() {
    setFormData({ title: '', slug: '', content: '', summary: '', category_id: '', tags: [], published: true });
    setErrors({}); setPreview(false); setTagInput('');
  }

  function handleChange(e) {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
    if (name === 'title') {
      const slug = value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
      setFormData(prev => ({ ...prev, slug }));
    }
    setErrors(prev => ({ ...prev, [name]: '' }));
  }

  function addTag() {
    const tag = tagInput.trim();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({ ...prev, tags: [...prev.tags, tag] }));
      setTagInput('');
    }
  }

  function removeTag(tag) {
    setFormData(prev => ({ ...prev, tags: prev.tags.filter(t => t !== tag) }));
  }

  function handleSubmit(e) {
    e.preventDefault();
    const errs = {};
    ['title','slug','content','category_id'].forEach(f => { if (!formData[f]) errs[f] = t(`validation.${f}Required`); });
    if (Object.keys(errs).length) { setErrors(errs); return; }
    createMut.mutate({ ...formData, category_id: parseInt(formData.category_id) });
  }

  return (
    <Card
      title={t('kb.createArticle')}
      className="bg-white shadow-md hover:shadow-lg transition-shadow"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title and Slug */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            id="title"
            name="title"
            label={t('kb.kbtitle')}
            value={formData.title}
            onChange={handleChange}
            error={errors.title}
            required
          />
          <Input
            id="slug"
            name="slug"
            label={t('kb.slug')}
            value={formData.slug}
            onChange={handleChange}
            error={errors.slug}
            required
          />
        </div>

        {/* Category and Published */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">{t('kb.category')}</label>
            <select
              name="category_id"
              value={formData.category_id}
              onChange={handleChange}
              disabled={catLoading}
              className="mt-1 block w-full border rounded-md p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">{t('kb.selectCategory')}</option>
              {categories.map(c => <option key={c.id} value={c.id}>{c.name}</option>)}
            </select>
            {errors.category_id && <p className="text-red-600 text-sm mt-1">{errors.category_id}</p>}
          </div>
          <div className="flex items-center mt-8">
            <input
              type="checkbox"
              id="published"
              name="published"
              checked={formData.published}
              onChange={handleChange}
              className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="published" className="ml-2 text-sm text-gray-700">{t('kb.publishImmediately')}</label>
          </div>
        </div>

        {/* Abstract/Summary */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="mb-1 flex justify-between items-center">
            <label className="block text-sm font-medium text-gray-700">{t('kb.summary')}</label>
            <span className="text-xs text-gray-500">{t('kb.summaryHelp')}</span>
          </div>
          <textarea
            name="summary"
            rows={3}
            value={formData.summary}
            onChange={handleChange}
            className="w-full border rounded-md p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder={t('kb.summaryPlaceholder')}
          />
        </div>

        {/* Tags */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('kb.tags')}</label>
          <div className="flex gap-2">
            <input
              value={tagInput}
              onChange={e => setTagInput(e.target.value)}
              onKeyDown={e => e.key==='Enter' && (e.preventDefault(), addTag())}
              className="border rounded-md p-2 flex-grow focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder={t('kb.addTag')}
            />
            <Button type="button" variant="outline" onClick={addTag}>{t('common.add')}</Button>
          </div>
          {formData.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-3">
              {formData.tags.map(tag => (
                <span key={tag} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full flex items-center text-sm">
                  {tag}
                  <button
                    type="button"
                    className="ml-2 text-blue-600 hover:text-blue-800 focus:outline-none"
                    onClick={() => removeTag(tag)}
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="border rounded-lg overflow-hidden">
          <div className="flex justify-between items-center bg-gray-50 px-4 py-2 border-b">
            <label className="font-medium text-gray-700">{t('kb.content')}</label>
            <div className="space-x-2">
              <Button
                size="sm"
                variant={preview ? 'outline' : 'primary'}
                onClick={() => setPreview(false)}
              >
                {t('kb.edit')}
              </Button>
              <Button
                size="sm"
                variant={preview ? 'primary' : 'outline'}
                onClick={() => setPreview(true)}
              >
                {t('kb.preview')}
              </Button>
            </div>
          </div>
          {preview ? (
            <div className="p-4 min-h-[300px] bg-white overflow-auto">
              {formData.content ?
                <KBArticleContent content={formData.content} /> :
                <p className="text-gray-400 italic">{t('kb.noContentToPreview')}</p>
              }
            </div>
          ) : (
            <textarea
              name="content"
              rows={12}
              value={formData.content}
              onChange={handleChange}
              className="block w-full border-0 p-4 font-mono focus:ring-0"
              placeholder={t('kb.markdownSupported')}
            />
          )}
          {errors.content && <p className="text-red-600 text-sm p-2 bg-red-50">{errors.content}</p>}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-2">
          <Button variant="outline" onClick={reset}>
            {t('common.reset')}
          </Button>
          <Button
            type="submit"
            disabled={createMut.isLoading}
          >
            {createMut.isLoading ?
              <>
                <span className="animate-spin inline-block h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                {t('common.saving')}
              </> :
              t('kb.publishArticle')
            }
          </Button>
        </div>
      </form>
    </Card>
  )
}