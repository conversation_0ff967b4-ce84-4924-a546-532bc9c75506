# .gitignore

# Purpose: Specifies intentionally untracked files that <PERSON><PERSON> should ignore.
# Customize this file based on your specific project needs and technologies.

# Operating System Files
# ----------------------
# macOS
.DS_Store
.AppleDouble
.LSOverride

# Thumbnails cache files for macOS
._*

# Files that might appear on external disks
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux thumbnail cache files
.Trash-*

# IDE / Editor Configuration
# --------------------------
# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# JetBrains IDEs (IntelliJ, PyCharm, WebStorm, etc.)
.idea/
*.iml
*.ipr
*.iws
.project
.classpath
.settings/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.[oa]
*.sw[op]
*~
Session.vim
.netrwhist

# Atom
.atom/

# Build Artifacts & Output
# ------------------------
build/
dist/
out/
target/
*.o
*.obj
*.exe
*.dll
*.so
*.dylib
*.class
*.jar
*.war
*.ear

# Dependency Directories
# ----------------------
node_modules/
jspm_packages/
bower_components/
vendor/ # For PHP Composer, Ruby gems, etc.
venv/   # Python virtual environment
env/    # Python virtual environment (alternative name)
__pycache__/
*.pyc

# Log Files
# ---------
*.log
logs/
*.log.*

# Runtime Data
# ------------
pids/
*.pid
*.seed
*.pid.lock

# Environment Variables / Secrets
# -------------------------------
# Never commit sensitive data!
.env
.env.*
!.env.example
!.env.test
secrets.yml
config.php

# Temporary Files
# ---------------
*.tmp
*.temp
*~

# Compressed Files
# ----------------
*.zip
*.tar.gz
*.rar
*.7z

# Database Files
# --------------
*.sqlite
*.db
*.sqlitedb

# Add project-specific ignores below this line
# --------------------------------------------
# e.g., /my_secret_folder/
# e.g., *.bak

