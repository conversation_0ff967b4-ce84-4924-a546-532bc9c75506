"""Establish baseline for existing database

Revision ID: 8271b065814f
Revises: 
Create Date: 2025-04-09 10:15:04.020853

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8271b065814f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint('animal_groups_herd_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'herds', ['herd_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint('constraints_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint('feed_nutrients_feed_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('feed_nutrients_nutrient_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'nutrients', ['nutrient_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app', ondelete='CASCADE')

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint('feeds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.add_column(sa.Column('ear_num', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('gender', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('birth_weight', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('breed', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('color', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('father_ear_num', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('mother_ear_num', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('entry_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('entry_type', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('group_name', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('lactation_number', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('month_age', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('grow_status', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('fertility_status', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('high', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('bust', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('measure_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('birth_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('is_on_farm', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('exit_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('exit_type', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('exit_reason', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('calving_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('cure_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('abortion_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('insem_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('curedate', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('pd1_date', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('pd2_date', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('disease_date', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('health_status', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('disease_name', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('create_time', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('remark', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('dry_date', sa.DateTime(), nullable=True))
        batch_op.drop_constraint('herds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint('ration_feeds_feed_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('ration_feeds_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint('rations_animal_group_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('rations_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'animal_groups', ['animal_group_id'], ['id'], referent_schema='ration_app')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('rations_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('rations_animal_group_id_fkey', 'animal_groups', ['animal_group_id'], ['id'])

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('ration_feeds_ration_id_fkey', 'rations', ['ration_id'], ['id'])
        batch_op.create_foreign_key('ration_feeds_feed_id_fkey', 'feeds', ['feed_id'], ['id'])

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('herds_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.drop_column('dry_date')
        batch_op.drop_column('remark')
        batch_op.drop_column('create_time')
        batch_op.drop_column('disease_name')
        batch_op.drop_column('health_status')
        batch_op.drop_column('disease_date')
        batch_op.drop_column('pd2_date')
        batch_op.drop_column('pd1_date')
        batch_op.drop_column('curedate')
        batch_op.drop_column('insem_date')
        batch_op.drop_column('abortion_date')
        batch_op.drop_column('cure_date')
        batch_op.drop_column('calving_date')
        batch_op.drop_column('exit_reason')
        batch_op.drop_column('exit_type')
        batch_op.drop_column('exit_date')
        batch_op.drop_column('is_on_farm')
        batch_op.drop_column('birth_date')
        batch_op.drop_column('measure_date')
        batch_op.drop_column('bust')
        batch_op.drop_column('high')
        batch_op.drop_column('fertility_status')
        batch_op.drop_column('grow_status')
        batch_op.drop_column('month_age')
        batch_op.drop_column('lactation_number')
        batch_op.drop_column('group_name')
        batch_op.drop_column('entry_type')
        batch_op.drop_column('entry_date')
        batch_op.drop_column('mother_ear_num')
        batch_op.drop_column('father_ear_num')
        batch_op.drop_column('color')
        batch_op.drop_column('breed')
        batch_op.drop_column('birth_weight')
        batch_op.drop_column('gender')
        batch_op.drop_column('ear_num')

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feeds_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feed_nutrients_nutrient_id_fkey', 'nutrients', ['nutrient_id'], ['id'])
        batch_op.create_foreign_key('feed_nutrients_feed_id_fkey', 'feeds', ['feed_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('constraints_ration_id_fkey', 'rations', ['ration_id'], ['id'])

    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('animal_groups_herd_id_fkey', 'herds', ['herd_id'], ['id'])

    # ### end Alembic commands ###
