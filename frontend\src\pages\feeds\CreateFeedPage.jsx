import { useState } from 'react'
import { useNavigate, Link, useParams } from 'react-router-dom'
import { useMutation, useQueryClient, useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Input from '../../components/ui/Input'
import { createFeed, getFeedById, updateFeed } from '../../services/feedService'
import { EXCHANGE_RATES } from '../../utils/formatters'

const CreateFeedPage = () => {
  const { t, i18n } = useTranslation()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { id } = useParams()
  const isEditMode = !!id

  const [formData, setFormData] = useState({
    name: '',
    dry_matter_percentage: '',
    cost_per_kg: '',
    description: '',
    is_public: false,
    nutrients: []
  })
  const [errors, setErrors] = useState({})

  // Fetch feed data if in edit mode
  const { isLoading } = useQuery(
    ['feed', id],
    () => getFeedById(id),
    {
      enabled: isEditMode,
      onSuccess: (data) => {
        if (data) {
          setFormData({
            name: data.name,
            dry_matter_percentage: data.dry_matter_percentage,
            cost_per_kg: data.cost_per_kg,
            description: data.description || '',
            is_public: data.is_public,
            nutrients: data.nutrients?.map(n => ({
              name: n.nutrient_name,
              value: n.value,
              unit: n.nutrient_unit
            })) || []
          })
        }
      }
    }
  )

  const createMutation = useMutation(createFeed, {
    onSuccess: () => {
      queryClient.invalidateQueries('feeds')
      navigate('/feeds')
    }
  })

  const updateMutation = useMutation(
    (data) => updateFeed(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['feed', id])
        queryClient.invalidateQueries('feeds')
        navigate(`/feeds/${id}`)
      }
    }
  )

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))

    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }))
    }
  }

  const handleNutrientChange = (index, field, value) => {
    const updatedNutrients = [...formData.nutrients]
    updatedNutrients[index] = {
      ...updatedNutrients[index],
      [field]: value
    }

    setFormData(prev => ({
      ...prev,
      nutrients: updatedNutrients
    }))

    // Clear error when user types
    if (errors[`nutrients.${index}.${field}`]) {
      setErrors(prev => ({ ...prev, [`nutrients.${index}.${field}`]: null }))
    }
  }

  const addNutrient = () => {
    setFormData(prev => ({
      ...prev,
      nutrients: [...prev.nutrients, { name: '', value: '', unit: '%' }]
    }))
  }

  const removeNutrient = (index) => {
    const updatedNutrients = [...formData.nutrients]
    updatedNutrients.splice(index, 1)

    setFormData(prev => ({
      ...prev,
      nutrients: updatedNutrients
    }))
  }

  const validate = () => {
    const newErrors = {}

    if (!formData.name) {
      newErrors.name = t('validation.nameRequired')
    }

    if (!formData.dry_matter_percentage) {
      newErrors.dry_matter_percentage = t('feeds.dryMatterRequired')
    } else if (isNaN(formData.dry_matter_percentage) || parseFloat(formData.dry_matter_percentage) <= 0 || parseFloat(formData.dry_matter_percentage) > 100) {
      newErrors.dry_matter_percentage = t('feeds.dryMatterRange')
    }

    if (!formData.cost_per_kg) {
      newErrors.cost_per_kg = t('feeds.costRequired')
    } else if (isNaN(formData.cost_per_kg) || parseFloat(formData.cost_per_kg) < 0) {
      newErrors.cost_per_kg = t('feeds.costPositive')
    }

    // Validate nutrients
    formData.nutrients.forEach((nutrient, index) => {
      if (!nutrient.name) {
        newErrors[`nutrients.${index}.name`] = t('feeds.nutrientNameRequired')
      }

      if (!nutrient.value) {
        newErrors[`nutrients.${index}.value`] = t('feeds.valueRequired')
      } else if (isNaN(nutrient.value)) {
        newErrors[`nutrients.${index}.value`] = t('feeds.valueMustBeNumber')
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (!validate()) {
      return
    }

    // Convert numeric strings to numbers
    const formattedData = {
      ...formData,
      dry_matter_percentage: parseFloat(formData.dry_matter_percentage),
      cost_per_kg: parseFloat(formData.cost_per_kg),
      nutrients: formData.nutrients.map(nutrient => ({
        ...nutrient,
        value: parseFloat(nutrient.value)
      }))
    }

    if (isEditMode) {
      updateMutation.mutate(formattedData)
    } else {
      createMutation.mutate(formattedData)
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? t('feeds.editFeed') : t('feeds.addNew')}
        </h1>
      </div>

      {isLoading ? (
        <p className="text-center py-4">{t('common.loading')}</p>
      ) : (

      <form onSubmit={handleSubmit}>
        <Card>
          <div className="space-y-4">
            <Input
              id="name"
              name="name"
              label={t('feeds.feedName')}
              value={formData.name}
              onChange={handleChange}
              error={errors.name}
              required
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                id="dry_matter_percentage"
                name="dry_matter_percentage"
                label={t('feeds.dryMatterPercent')}
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={formData.dry_matter_percentage}
                onChange={handleChange}
                error={errors.dry_matter_percentage}
                required
              />

              <Input
                id="cost_per_kg"
                name="cost_per_kg"
                label={i18n.language === 'zh'
                  ? t('feeds.costPerKgCurrency').replace('$', '¥') + ` (${t('feeds.usdEquivalent')}: $${(parseFloat(formData.cost_per_kg || 0) / EXCHANGE_RATES.USD_TO_RMB).toFixed(2)})`
                  : t('feeds.costPerKgCurrency')
                }
                type="number"
                min="0"
                step="0.01"
                value={formData.cost_per_kg}
                onChange={handleChange}
                error={errors.cost_per_kg}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.description')}
              </label>
              <textarea
                id="description"
                name="description"
                rows="3"
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                value={formData.description}
                onChange={handleChange}
              ></textarea>
            </div>

            <div className="flex items-center">
              <input
                id="is_public"
                name="is_public"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500"
                checked={formData.is_public}
                onChange={handleChange}
              />
              <label htmlFor="is_public" className="ml-2 block text-sm text-gray-900">
                {t('feeds.makePublic')}
              </label>
            </div>
          </div>
        </Card>

        <Card title={t('feeds.nutrientComposition')} className="mt-6">
          {formData.nutrients.length > 0 ? (
            <div className="space-y-4">
              {formData.nutrients.map((nutrient, index) => (
                <div key={index} className="flex items-end space-x-2">
                  <div className="flex-1">
                    <Input
                      id={`nutrient-name-${index}`}
                      label={index === 0 ? t('feeds.nutrientName') : ""}
                      value={nutrient.name}
                      onChange={(e) => handleNutrientChange(index, 'name', e.target.value)}
                      error={errors[`nutrients.${index}.name`]}
                    />
                  </div>
                  <div className="w-24">
                    <Input
                      id={`nutrient-value-${index}`}
                      label={index === 0 ? t('common.value') : ""}
                      type="number"
                      step="0.01"
                      value={nutrient.value}
                      onChange={(e) => handleNutrientChange(index, 'value', e.target.value)}
                      error={errors[`nutrients.${index}.value`]}
                    />
                  </div>
                  <div className="w-20">
                    <Input
                      id={`nutrient-unit-${index}`}
                      label={index === 0 ? t('feeds.unit') : ""}
                      value={nutrient.unit}
                      onChange={(e) => handleNutrientChange(index, 'unit', e.target.value)}
                    />
                  </div>
                  <div>
                    <button
                      type="button"
                      className="inline-flex items-center p-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      onClick={() => removeNutrient(index)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">{t('feeds.noNutrientsAdded')}</p>
          )}

          <button
            type="button"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            onClick={addNutrient}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            {t('feeds.addNutrient')}
          </button>
        </Card>

        <div className="mt-6 flex justify-end space-x-3">
          <Link to="/feeds">
            <Button variant="outline">{t('common.cancel')}</Button>
          </Link>
          <Button type="submit" disabled={isEditMode ? updateMutation.isLoading : createMutation.isLoading}>
            {isEditMode ?
              (updateMutation.isLoading ? t('common.saving') : t('common.update')) :
              (createMutation.isLoading ? t('common.saving') : t('feeds.saveFeed'))}
          </Button>
        </div>
      </form>
      )}
    </div>
  )
}

export default CreateFeedPage