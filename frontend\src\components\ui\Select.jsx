import React, { forwardRef } from 'react'
import { useTranslation } from 'react-i18next'

const Select = forwardRef((
  {
    id,
    label,
    options = [],
    value,
    onChange,
    error,
    helpText,
    required = false,
    disabled = false,
    placeholder = 'Select an option',
    className = '',
    name,
    ...props
  },
  ref
) => {
  const { t } = useTranslation()

  // Ensure value is properly formatted
  // This handles both null and undefined values
  const formattedValue = value === null || value === undefined ? '' : value

  return (
    <div className="mb-4">
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">{t('common.requiredMark')}</span>}
        </label>
      )}
      <select
        id={id}
        name={name}
        value={formattedValue}
        onChange={onChange}
        disabled={disabled}
        ref={ref}
        className={`
          block w-full rounded-md border-gray-300 shadow-sm
          focus:border-green-500 focus:ring-green-500 sm:text-sm
          ${error ? 'border-red-300' : 'border-gray-300'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
          ${className}
        `}
        {...props}
      >
        {/* Only show placeholder option if showPlaceholder is true */}
        {props.showPlaceholder !== false && (
          <option value="">{placeholder === 'Select an option' ? t('common.selectOption') : placeholder}</option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      {helpText && !error && (
        <p className="mt-1 text-sm text-gray-500">{helpText}</p>
      )}
    </div>
  )
})

export default Select