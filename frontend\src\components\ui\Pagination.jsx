import React from 'react'
import { useTranslation } from 'react-i18next'
import Button from './Button'

/**
 * Reusable pagination component
 * 
 * @param {Object} props
 * @param {number} props.currentPage - Current page number (1-based)
 * @param {number} props.totalPages - Total number of pages
 * @param {number} props.totalItems - Total number of items
 * @param {Function} props.onPageChange - Function to call when page changes
 * @param {boolean} props.isLoading - Whether data is currently loading
 * @returns {JSX.Element}
 */
const Pagination = ({ currentPage, totalPages, totalItems, onPageChange, isLoading }) => {
  const { t } = useTranslation()

  // Don't render pagination if there's only one page or no pages
  if (totalPages <= 1) return null

  const handlePrev = () => onPageChange(currentPage - 1)
  const handleNext = () => onPageChange(currentPage + 1)

  return (
    <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-2">
      <div className="text-sm text-gray-500">
        {t('common.total')}: {totalItems} {t('common.items')}
      </div>
      
      <div className="flex items-center space-x-2">
        <Button 
          onClick={handlePrev} 
          disabled={currentPage === 1 || isLoading} 
          size="sm" 
          variant="outline"
        >
          {t('common.previous')}
        </Button>
        
        <span className="text-sm text-gray-700">
          {t('common.page')} {currentPage} {t('common.of')} {totalPages}
        </span>
        
        <Button 
          onClick={handleNext} 
          disabled={currentPage === totalPages || isLoading} 
          size="sm" 
          variant="outline"
        >
          {t('common.next')}
        </Button>
      </div>
    </div>
  )
}

export default Pagination
