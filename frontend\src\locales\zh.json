{"app": {"title": "奶牛饲料配方平台"}, "nav": {"dashboard": "仪表盘", "feeds": "饲料", "herds": "牛只", "rations": "配方", "animalGroups": "牛群", "logout": "退出登录", "knowledgeBase": "知识库"}, "dashboard": {"welcome": "欢迎使用奶牛饲料配方平台", "feeds": "饲料", "herds": "牛只", "rations": "配方", "milkProduction": "产奶量", "bcs": "体况评分"}, "auth": {"login": "登录", "register": "注册", "email": "电子邮箱", "password": "密码", "confirmPassword": "确认密码", "forgotPassword": "忘记密码？", "noAccount": "没有账号？", "haveAccount": "已有账号？", "signUp": "注册", "signIn": "登录", "name": "姓名", "editPassword": "修改密码", "changePassword": "更改密码", "currentPassword": "当前密码", "newPassword": "新密码", "confirmNewPassword": "确认新密码", "passwordChanged": "密码修改成功", "currentPasswordIncorrect": "当前密码不正确", "passwordMismatch": "新密码不匹配"}, "common": {"add": "添加", "refresh": "刷新", "reset": "重置", "create": "创建", "edit": "编辑", "delete": "删除", "save": "保存", "success": "成功", "saveAndFinish": "完成并保存", "currencySymbol": "¥", "cancel": "取消", "back": "返回", "next": "下一步", "submit": "提交", "loading": "加载中...", "search": "搜索", "filter": "筛选", "sort": "排序", "actions": "操作", "name": "名称", "description": "描述", "date": "日期", "status": "状态", "type": "类型", "value": "值", "required": "必填", "optional": "选填", "selectOption": "请选择", "requiredMark": "*", "tryAgain": "重试", "created": "创建于", "public": "公开", "private": "私有", "viewAll": "查看全部", "showLess": "显示更少", "showMoreDetails": "显示更多详情", "yes": "是", "no": "否", "saving": "保存中...", "searching": "搜索中...", "thinking": "思考中...", "view": "查看", "published": "已发布", "close": "关闭", "send": "发送", "draft": "草稿", "submitting": "提交中...", "previous": "上一页", "page": "页", "of": "共", "total": "总计", "refreshing": "刷新中...", "hideInfo": "隐藏信息", "showInfo": "显示信息", "update": "更新", "errorOccurred": "发生错误。请重试。", "basicInformation": "基本信息", "items": "项", "clearSearch": "清除搜索", "comingSoon": "即将推出", "unsavedChanges": "您有未保存的更改", "added": "已添加", "saveAsDraft": "保存为草稿", "remove": "移除", "error": "错误", "partial": "部分"}, "feeds": {"title": "饲料", "createNew": "创建新饲料", "editFeed": "编辑饲料", "feedDetails": "饲料详情", "feedName": "饲料名称", "feedType": "饲料类型", "dryMatter": "干物质", "protein": "蛋白质", "energy": "能量", "fiber": "纤维", "minerals": "矿物质", "vitamins": "维生素", "fat": "脂肪", "calcium": "钙", "phosphorus": "磷", "cost": "成本", "recentlyAdded": "最近添加的饲料", "noFeedsFound": "未找到饲料", "library": "饲料库", "addNew": "添加新饲料", "errorLoading": "加载饲料出错", "noFeedsFoundLong": "未找到饲料。添加您的第一个饲料开始使用。", "dmShort": "干物质", "costShort": "成本", "backToFeeds": "返回饲料列表", "feedNotFound": "未找到饲料", "feedInformation": "饲料信息", "costPerKg": "每公斤成本", "visibility": "可见性", "nutrientComposition": "营养成分", "noNutrientData": "没有可用的营养数据", "confirmDeletion": "确认删除", "deleteConfirmationMessage": "您确定要删除 {{name}} 吗？此操作无法撤消。", "dryMatterPercent": "干物质 (%)", "costPerKgCurrency": "每公斤成本 ($)", "usdEquivalent": "美元等值", "makePublic": "将此饲料设为公开（所有用户可见）", "nutrientName": "营养素名称", "unit": "单位", "noNutrientsAdded": "尚未添加营养素", "addNutrient": "添加营养素", "saveFeed": "保存饲料", "dryMatterRequired": "干物质百分比是必填项", "dryMatterRange": "干物质百分比必须在 0 到 100 之间", "costRequired": "每公斤成本是必填项", "costPositive": "每公斤成本必须是正数", "nutrientNameRequired": "营养素名称是必填项", "valueRequired": "值是必填项", "valueMustBeNumber": "值必须是数字", "totalFeeds": "个饲料总计", "search": "搜索饲料...", "noMatchingFeeds": "未找到匹配的饲料", "noFeedsAvailable": "没有可用的饲料", "totalAvailable": "总可用数", "selected": "已选择"}, "herds": {"title": "牛只", "createNew": "创建新牛只", "editHerd": "编辑牛只", "herdDetails": "牛只详情", "herdName": "牛只名称", "animalType": "动物类型", "count": "数量", "averageWeight": "平均体重", "productionLevel": "生产水平", "ageGroup": "年龄组", "addNew": "添加新牛只", "errorLoading": "加载牛只出错", "noHerdsFoundLong": "未找到牛只。添加您的第一个牛只开始使用。", "groups": "组", "backToHerds": "返回牛只列表", "herdNotFound": "未找到牛只", "earNumber": "耳标号", "basicInformation": "基本信息", "breed": "品种", "gender": "性别", "color": "颜色", "birthWeight": "出生体重", "birthDate": "出生日期", "ageMonths": "年龄（月）", "fatherEarNum": "父亲耳标号", "motherEarNum": "母亲耳标号", "unnamed": "未命名动物", "born": "出生于", "healthy": "健康", "sick": "生病", "notOnFarm": "不在场", "productionStatus": "生产与状态", "lactationNumber": "胎次", "group": "组别", "growthStatus": "生长状态", "fertilityStatus": "生育状态", "healthStatus": "健康状态", "disease": "疾病", "height": "身高", "bust": "胸围", "entryExitInfo": "进出场信息", "entryDate": "进场日期", "entryType": "进场类型", "onFarm": "在场", "exitDate": "出场日期", "exitType": "出场类型", "exitReason": "出场原因", "importantDates": "重要日期", "measureDate": "测量日期", "calvingDate": "分娩日期", "dryDate": "干奶日期", "inseminationDate": "授精日期", "abortionDate": "流产日期", "cureDate": "治疗日期", "remarks": "备注", "confirmDeletion": "确认删除", "deleteConfirmationMessage": "您确定要删除 {{name}} 吗？此操作无法撤消。", "animalDetails": "动物详情", "birthWeightKg": "出生体重（公斤）", "datesStatus": "日期与状态", "groupName": "组名称", "additionalDates": "其他日期", "isOnFarm": "是否在场（1=是，0=否）", "healthNotes": "健康与备注", "saveHerd": "保存牛只", "updateHerd": "更新牛只", "male": "公", "female": "母", "searchByEarNumber": "按耳标号、名称或品种搜索...", "animalsFound": "个动物被找到", "noSearchResults": "没有找到符合搜索条件的动物", "viewAnimal": "查看动物 #{{id}} {{name}}", "viewUnnamedAnimal": "查看未命名动物 #{{id}}"}, "rations": {"title": "配方", "createNew": "创建新配方", "editRation": "编辑配方", "rationDetails": "配方详情", "rationName": "配方名称", "description": "描述", "targetHerd": "目标牛群", "ingredients": "成分", "nutritionalValues": "营养价值", "formulate": "配制", "optimize": "优化", "errorLoading": "加载配方出错", "noRationsFoundLong": "未找到配方。创建您的第一个配方开始使用。", "formulated": "已配制", "notFormulated": "未配制", "reformulate": "重新配制", "recent": "最近配方", "recentlyFormulated": "您最近配制的配方", "noRationsFound": "未找到配方", "totalRations": "个配方总计", "lastFormulated": "最后配制于", "notYetFormulated": "尚未配制", "pending": "待处理", "backToRations": "返回配方列表", "rationNotFound": "未找到配方", "feedIngredients": "饲料成分", "min": "最小", "max": "最大", "noMin": "无最小值", "noMax": "无最大值", "none": "无", "noFeedsAdded": "此配方没有添加饲料", "nutritionalConstraints": "营养约束", "noConstraintsDefined": "未定义营养约束", "nrcRequirementsAvailable": "营养模型可用", "readyToFormulate": "准备配制", "readyToFormulateDescription": "点击配置开始配方规划", "constraintsMet": "满足营养约束", "aiAssistant": "AI助手", "aiAssistance": "AI辅助", "askAIPlaceholder": "向AI助手询问关于此配方的问题...", "aiAssistantHint": "尝试询问配方帮助、饲料建议或营养建议。", "aiQuickActions": {"analyze": "分析", "analyzeDesc": "分析此配方状态并继续配置", "optimize": "优化", "optimizeDesc": "帮助优化配方以提高成本效益", "fixIssues": "修复问题", "fixIssuesDesc": "帮助解决配方中的问题", "chat": "聊天", "chatDesc": "与AI助手进行一般对话"}, "functionNames": {"add_feed_to_ration": "添加饲料到配方", "check_nutrition": "检查配方营养成分", "remove_feed_from_ration": "从配方中移除饲料", "update_nutrient_constraint": "更新营养约束", "run_formulation": "运行配方计算", "save_formulation": "保存配方", "analyze_formulation_result": "分析配方结果", "get_feed_info": "获取饲料信息", "search_feeds": "搜索饲料", "get_kb_article": "搜索知识库", "finish_formulation": "完成配方计算", "make_ration": "创建配方", "add_nutrients_to_feed": "添加饲料营养成分"}, "functionResults": {"add_feed_to_ration": "饲料已成功添加到配方中", "remove_feed_from_ration": "饲料已成功从配方中移除", "update_nutrient_constraint": "营养约束已成功更新", "run_formulation": "配方计算已成功完成", "save_formulation": "配方已成功保存", "analyze_formulation_result": "配方分析已完成", "get_feed_info": "已获取饲料信息", "search_feeds": "饲料搜索已完成", "get_knowledge_base": "知识库搜索已完成", "finish_formulation": "配方计算过程已完成", "check_nutrition": "营养成分检查已完成", "make_ration": "配方已成功创建", "add_nutrients_to_feed": "营养成分已成功添加到饲料"}, "toolMessages": {"addedFeedsToRation": "已添加 {{count}} 个饲料到配方中: {{feedNames}}", "removedFeedsFromRation": "已从配方中移除 {{count}} 个饲料: {{feedNames}}", "updatedNutrientConstraints": "已更新 {{count}} 个营养约束: {{constraints}}", "addedNutrientConstraints": "已添加 {{count}} 个营养约束: {{constraints}}", "updatedAndAddedNutrientConstraints": "已更新 {{updateCount}} 个并添加 {{addCount}} 个营养约束: {{constraints}}", "formulationSuccessful": "配方计算成功。每日总成本: {{cost}}", "formulationFailed": "配方计算失败: {{reason}}", "kbArticlesFound": "找到 {{count}} 篇相关文章", "noKbArticlesFound": "未找到与查询相关的知识库文章: '{{query}}'", "noFeedsFoundMatching": "未找到匹配 '{{query}}' 的饲料", "couldNotFindFeeds": "未找到 {{count}} 个饲料", "rationFormulationCreated": "配方已成功创建并保存", "nutritionCheckCompleted": "营养检查已完成。", "nutritionCheckRequirementsNotMet": "部分营养需求未满足。", "updatedNutrientsForFeeds": "已更新 {{count}} 个饲料的营养成分"}, "checkNutrition": {"completed": "营养检查已完成。", "requirementsNotMet": "部分营养需求未满足。", "dmiMismatch": "干物质采食量目标不匹配。"}, "aiCommands": {"formulate": "请配制当前的日粮。"}, "startFormulation": "开始配制", "additionalInstructions": "附加说明（可选）", "instructionsPlaceholder": "输入此配方的任何特定要求或约束...", "proceedToFormulation": "继续配制", "aiTabs": {"conversation": "对话", "changes": "变更", "analysis": "分析"}, "noChangesToApply": "没有由AI进行的更改可应用。", "noChangesToDisplay": "没有可显示的变更", "changesDetected": "检测到变更", "aiWelcomeMessage": "您好！我是您的AI日粮配方智能体。\n 我可以自主添加饲料、修改约束，进行配方，调整，并保存。", "aiInitializationError": "初始化AI助手时出错。请关闭并重试。", "aiProcessingError": "抱歉，处理您的请求时出错。", "aiActionTaken": "AI执行的操作", "recommendations": "建议", "noRecommendations": "没有建议", "noRecommendationsYet": "尚未生成任何建议。", "actionHistory": "操作历史", "noActionsYet": "尚未执行任何AI操作。", "aiActionPrompt": "由您的消息触发", "changesReady": "更改已准备好应用。", "applyChanges": "应用更改", "errorApplyingChanges": "应用AI更改时出错。", "aiChangesApplied": "AI更改已成功应用！", "noChangesYet": "AI助手尚未进行任何更改。", "feedChanges": "饲料变更", "noFeedChanges": "没有饲料变更", "addedFeeds": "添加的饲料", "removedFeeds": "移除的饲料", "modifiedFeeds": "修改的饲料", "inclusionRange": "包含范围", "actual": "实际", "before": "之前", "after": "之后", "rangeChangedFrom": "范围从", "rangeChangedTo": "变为", "constraintChanges": "约束变更", "noConstraintChanges": "没有约束变更", "actionCompleted": "操作已完成。", "formulationChanges": "配方变更", "rationFormulated": "配方已制定", "aiAnalysis": "AI分析", "formulationSummary": "配方摘要", "autoFormulating": "自动配制中...", "autoFormulationInProgress": "自动配制进行中", "autoFormulationActive": "自动配制已激活", "autoFormulationHint": "AI正在自动配制您的配方。您可以随时停止该过程。", "formulationStopped": "配制过程已停止", "aiCommunicationError": "与AI服务通信失败", "aiActionsSummary": "AI操作摘要", "noAiActions": "尚未执行任何AI操作", "emptyAgentResponse": "抱歉，我没有收到有效的回复。请再试一次。", "statusUpdates": {"thinking": "思考中...", "autoFormulating": "自动配制中...", "autoFormulationInProgress": "自动配制进行中", "autoFormulationActive": "自动配制已激活", "autoFormulationHint": "AI正在自动配制您的配方。您可以随时停止该过程。", "formulationStopped": "配制过程已停止", "stopped": "已停止", "continue": "继续。", "emptyResponse": "AI提供了空响应。请重试。", "stoppingFormulation": "正在停止配制..."}, "addedConstraints": "添加的约束", "removedConstraints": "移除的约束", "modifiedConstraints": "修改的约束", "feedsUsed": "使用的饲料", "aiActions": {"addFeed": "添加饲料", "removeFeed": "移除饲料", "updateConstraint": "更新约束", "runFormulation": "执行配方", "saveFormulation": "保存配方", "analyzeFormulation": "分析结果", "getFeedInfo": "获取饲料信息", "searchFeeds": "搜索饲料", "getKnowledgeBase": "搜索知识库"}, "conflictsDetected": "检测到 {{count}} 个潜在冲突", "nutrientResults": "配方营养成分", "confirmDeletion": "确认删除", "deleteConfirmationMessage": "您确定要删除 {{name}} 吗？此操作无法撤消。", "herd": "牛只", "animalGroup": "牛群", "selectHerd": "选择牛只", "selectGroup": "选择组", "selectHerdFirst": "请先选择牛只", "feeds": "饲料", "feed": "饲料", "feedsRequired": "请添加可用饲料", "minimumInclusion": "最小包含百分比 (%)", "maximumInclusion": "最大包含百分比 (%)", "addFeed": "添加饲料", "addFeedsToLibraryFirst": "请先将饲料添加到您的库中", "nutrientName": "营养素名称", "minimumValue": "最小值", "maximumValue": "最大值", "noConstraintsAdded": "尚未添加约束", "addConstraint": "添加约束", "saveRation": "保存配方", "animalGroupRequired": "牛群是必填项", "nutrientNameRequired": "营养素名称是必填项", "formulateRation": "配制配方", "cannotFormulate": "无法配制配方", "addFeedsBeforeFormulating": "请在配制前将饲料添加到此配方。", "formulation": "配方制定", "formulationResult": "配制结果", "formulationResults": "配制结果", "noFormulationResults": "没有可用的配制结果", "totalCost": "总成本", "viewRationDetails": "查看配方详情", "formulationError": "配制错误", "formulationErrorMessage": "配制配方失败。请检查您的约束并重试。", "backToRation": "返回配方", "formulating": "配制中...", "usingNrcRequirements": "使用NRC第8版为该牛群提供的需求", "availableFeeds": "可用饲料", "search": "搜索饲料...", "noMatchingFeeds": "未找到匹配的饲料", "noFeedsAvailable": "没有可用的饲料", "added": "已添加", "addFeedsHint": "从列表中选择饲料添加到您的配方", "resetToNrc": "重置为NRC需求", "addDefaultConstraints": "添加默认约束", "completeBasicInfoFirst": "请先完成基本信息", "selectAnimalGroup": "选择一个牛群", "addFeeds": "将饲料添加到配方", "saveRationFirst": "先保存配方", "formulationStatus": "配制状态", "partialSuccess": "部分成功", "formulationSuccess": "配制成功", "someConstraintsNotMet": "某些约束无法满足", "nutritionStatus": "营养状态", "someNutrientsMissing": "某些营养需求未满足", "allRequirementsMet": "所有需求已满足", "nutrientsAnalyzed": "个营养素已分析", "feedComposition": "饲料成分", "nutrientAnalysis": "营养分析", "waitingForFormulation": "等待配方计算...", "clickFormulateHint": "点击配制按钮优化您的配方", "met": "已满足", "notMet": "未满足", "noFeedsIncluded": "配制中未包含饲料", "detailedResults": "详细结果", "inclusion": "包含百分比", "constraint": "约束", "cost": "成本", "costAnalysis": "成本分析", "perDayPerAnimal": "每天每头动物", "basicInformation": "基本信息", "noAnimalGroupsAvailable": "没有可用的牛群", "nutrientSummary": "营养摘要", "noNutrientData": "没有可用的营养数据", "constraintsNote": "注意：约束定义了配方中每种营养素的最小和最大值", "nutritionalRequirements": "营养需求", "selectedFeeds": "已选饲料", "noFeedsSelected": "未选择饲料", "createRation": "创建配方", "saveAsDraft": "保存为草稿", "saveAndFinish": "保存并完成", "selectAnimalGroupHint": "选择牛群以查看营养需求", "clearConstraints": "清空营养需求", "resetToModel": "重置营养需求", "troubleshooting": "故障排除", "troubleshootingTitle": "配方故障排除", "troubleshootingDescription": "如果您在配方制定中遇到问题，请检查以下常见问题：", "checkConstraints": "检查您的营养约束", "checkConstraintsDescription": "确保您的最小和最大值是现实的，并且可以通过所选饲料实现。", "checkFeeds": "检查您的饲料选择", "checkFeedsDescription": "确保您有良好的饲料组合，可以满足所有营养需求。", "checkInclusions": "检查饲料包含限制", "checkInclusionsDescription": "验证饲料的最小和最大包含百分比是否允许足够的灵活性。", "infeasibleFormulation": "不可行的配方", "infeasibleFormulationDescription": "当前的饲料和约束组合无法产生有效的解决方案。", "formulationFailed": "配方制定失败", "formulationFailedDescription": "优化求解器遇到错误。尝试调整您的约束或饲料。", "tryAgain": "重试", "adjustAndRetry": "调整设置并重试", "formulationIncomplete": "配方不完整", "formulationIncompleteDescription": "您的配方制定不完整。请解决以下问题：", "conflictingConstraints": "约束冲突", "conflictingConstraintsDescription": "您的一些营养约束可能相互冲突。", "insufficientFeedVariety": "饲料种类不足", "insufficientFeedVarietyDescription": "添加更多种类的饲料以满足所有营养需求。", "tooRestrictive": "约束过于严格", "tooRestrictiveDescription": "您的约束可能过于严格。尝试放宽一些最小或最大值。", "atMinimumInclusion": "处于最小包含量", "atMaximumInclusion": "处于最大包含量", "requirementNotMet": "需求未满足", "requirementMet": "需求已满足", "actualValue": "实际值", "nutrient": "营养素", "status": "状态", "costPerKgDM": "每公斤干物质成本", "perKgDryMatter": "每公斤干物质", "noFeasibleSolution": "在给定的约束条件下没有可行的解决方案。", "solverFailed": "优化求解器遇到错误。请重试或调整您的约束。", "generalInfeasibility": "一般不可行性", "feedMinimums": "饲料最小值", "feedMinMaxConflict": "饲料最小/最大值冲突", "nutrientMinMaxConflict": "营养素最小/最大值冲突", "constraintsTooRestrictive": "约束组合可能过于严格。考虑放宽营养需求或饲料包含限制。", "totalMinimumExceeds": "总最小包含量超过100%。请减少最小包含百分比。", "minGreaterThanMax": "最小包含量大于最大包含量。请调整这些值。", "specificTips": "具体建议", "definiteConflicts": "确定的冲突", "potentialConflicts": "潜在问题", "potentialIssue": "潜在问题", "nutrientMinimumUnachievable": "营养素最小值无法达到", "nutrientMaximumUnachievable": "营养素最大值无法达到", "nutrientMinDetail": "{nutrient}的最小需求{value} {unit}可能无法达到。可用饲料中的最高浓度为{maxConc} {unit}。", "nutrientMaxDetail": "{nutrient}的最大需求{value} {unit}可能无法达到。可用饲料中的最低浓度为{minConc} {unit}。", "feedInclusionConflict": "饲料包含限制不兼容。总最小包含量超过100%。", "feedMinMaxConflictDetail": "饲料'{feed}'：最小包含量({min}%)大于最大包含量({max}%)。", "nutrientMinMaxConflictDetail": "营养素'{nutrient}'：最小需求({min} {unit})大于最大需求({max} {unit})。"}, "language": {"en": "English", "zh": "中文"}, "nrc": {"modelVersion": "NRC模型版本", "activeModel": "当前选择", "selectModelVersion": "选择NRC模型版本", "useDefaultModel": "使用默认模型", "selectModel": "选择模型", "loadingModels": "加载模型中...", "selectedModel": "选定模型", "noDescriptionAvailable": "没有可用的描述", "modelDescription": "不同的NRC模型基于研究提供不同的营养需求。默认模型使用最新的建议。"}, "optimization": {"title": "优化", "settings": "优化设置", "objective": "优化目标", "minimizeCost": "最小化成本", "maximizeMilkProduction": "最大化产奶量", "maximizeProfit": "最大化利润", "balanceNutrients": "平衡营养", "showAdvancedSettings": "显示高级设置", "maxIterations": "最大迭代次数", "convergenceTolerance": "收敛容差", "currentSettingsDescription": "当前优化目标：{{objective}}", "applySettings": "应用设置", "optimizing": "优化中...", "status": "优化状态", "readyToFormulate": "准备配制", "clickToStartDescription": "点击下面的按钮开始配制过程", "optimizationNote": "优化将以最低成本找到最佳的饲料组合来满足您的营养限制。"}, "validation": {"nameRequired": "请输入名称", "emailRequired": "请输入电子邮箱", "emailInvalid": "电子邮箱格式不正确", "passwordRequired": "请输入密码", "passwordLength": "密码长度至少为6个字符", "confirmPasswordRequired": "请确认密码", "passwordsDoNotMatch": "两次输入的密码不一致", "mustBeNumber": "必须是数字", "minNotGreaterThanMax": "最小值不能大于最大值", "multipleErrors": "需要修复多个问题才能继续", "required": "必填字段"}, "animalGroups": {"title": "牛群", "addNew": "添加新牛群", "recentlyAdded": "最新添加牛群", "editGroup": "编辑牛群", "groupName": "组名称", "groupDetails": "组详情", "details": "详情", "animals": "动物数量", "avgWeight": "平均体重", "avgLactation": "平均胎次", "milkProduction": "产奶量", "dailyMilkProduction": "日产奶量 (kg)", "daysInMilk": "泌乳天数", "avgBCS": "平均体况评分", "bodyConditionScore": "体况评分 (1-5)", "lactationNumber": "胎次", "animalsInGroup": "组内动物 ({{count}})", "noAnimalsInGroup": "该组中没有动物。", "backToGroups": "返回牛群列表", "confirmDeletion": "确认删除", "deleteConfirmationMessage": "您确定要删除 {{name}} 吗？此操作无法撤消。", "errorLoading": "加载牛群出错", "groupNotFound": "未找到牛群", "searchGroups": "搜索牛群...", "noGroupsFound": "未找到牛群。添加您的第一个牛群开始使用。", "noGroupsFoundForSearch": "未找到符合搜索条件的牛群。", "createFirst": "创建第一个牛群", "assignAnimals": "分配动物", "assignAnimalsDescription": "选择要添加到此组的动物。", "noAnimalsToAssign": "没有可分配的动物。请先添加动物。", "saveGroup": "保存牛群", "updateGroup": "更新牛群", "basicInformation": "基本信息", "nrcRequirements": "NRC第8版需求", "noNrcData": "计算NRC需求的数据不足。请添加体重和其他动物数据。", "dryMatterIntake": "干物质采食量", "estimatedDMI": "预估干物质采食量", "energyRequirements": "能量需求", "maintenance": "维持需求", "lactation": "泌乳需求", "pregnancy": "妊娠需求", "growth": "生长需求", "totalEnergy": "总能量需求", "requiredConcentration": "所需浓度", "proteinRequirements": "蛋白质需求", "totalProtein": "总蛋白质需求", "fiberRequirements": "纤维需求", "ndfRange": "NDF范围", "forageNdfMin": "最低粗饲料NDF", "adfMin": "最低ADF"}, "kb": {"title": "知识库", "description": "找到常见问题的答案，了解奶牛饲料配方的最佳实践。", "searchPlaceholder": "搜索知识库...", "searchResults": "搜索结果", "noSearchResults": "未找到搜索结果。", "viewAllResults": "查看所有结果", "showingResults": "显示 {{count}} 个结果", "resultsFound": "个结果", "categories": "分类", "selectCategory": "请选择", "noCategories": "未找到分类", "articles": "文章", "recentArticles": "最新文章", "popularArticles": "热门文章", "noArticles": "未找到文章", "views": "浏览量", "viewAllArticles": "查看所有文章", "popularTags": "热门标签", "noTags": "未找到标签", "adminTitle": "知识库管理", "adminDescription": "管理您的知识库内容、分类和嵌入向量。", "totalArticles": "文章总数", "publishedArticles": "已发布文章", "totalCategories": "分类总数", "totalViews": "总浏览量", "createArticle": "创建文章", "editCategory": "编辑分类", "addCategory": "添加分类", "parentCategory": "父分类", "noParent": "无父分类", "createCategoryPrompt": "创建您的第一个分类来组织您的知识库。", "confirmDeleteCategory": "确定要删除此分类吗？此操作无法撤消。", "confirmDeleteArticle": "确定要删除此文章吗？此操作无法撤消。", "kbtitle": "标题", "slug": "别名", "category": "分类", "publishImmediately": "立即发布", "tags": "标签", "addTag": "添加标签...", "summary": "摘要", "summaryPlaceholder": "文章简短摘要（可选）", "summaryHelp": "如果未提供，将自动生成摘要。", "content": "内容", "edit": "编辑", "preview": "预览", "noContentToPreview": "没有内容可预览", "markdownSupported": "支持Markdown格式", "markdownHelp": "使用Markdown格式化文本、链接、列表、代码块等。", "publishArticle": "发布文章", "saveDraft": "保存为草稿", "categoryRequired": "请选择一个分类", "articleCreatedSuccessfully": "文章创建成功！", "embeddings": "嵌入向量", "embeddingsOverview": "嵌入向量概览", "embeddingsStatus": "嵌入向量状态", "embeddingsDescription": "嵌入向量是文章内容的向量表示，使语义搜索成为可能。以下是当前状态：", "completed": "已完成", "pending": "待处理", "processing": "处理中", "failed": "失败", "completedEmbeddings": "已完成的嵌入向量", "pendingEmbeddings": "待处理的嵌入向量", "processingEmbeddings": "处理中的嵌入向量", "failedEmbeddings": "失败的嵌入向量", "embeddingsInfo": "关于嵌入向量", "embeddingsPending": "待处理：等待处理的文章", "embeddingsProcessing": "处理中：正在生成嵌入向量的文章", "embeddingsCompleted": "已完成：成功生成嵌入向量的文章", "embeddingsFailed": "失败：在生成嵌入向量过程中遇到错误的文章", "reprocessAll": "全部重新处理", "reprocessFailedEmbeddings": "重新处理失败的嵌入向量", "reprocessDescription": "您可以重新处理失败的嵌入向量以再次尝试。选择一次重新处理多少个失败的嵌入向量。", "reprocessLimit": "重新处理限制", "reprocessSuccess": "成功将 {{count}} 个嵌入向量排队等待重新处理", "reprocessError": "重新处理嵌入向量时发生错误", "reembedCompleted": "重新嵌入已完成的向量", "reembedCompletedTooltip": "当嵌入服务发生变化需要重新生成嵌入向量时使用。这将使用当前设置重新分块文章。", "reembedSuccess": "成功将 {{count}} 个已完成的块排队等待重新嵌入", "processAllArticles": "处理所有文章（可能需要更长时间）", "pendingJobs": "待处理任务", "pendingJobsDescription": "这些是当前等待处理或正在处理中的嵌入向量。", "pendingJobsCount": "个待处理任务", "processingJobsCount": "个处理中任务", "jobsInProgressMessage": "任务正在后台处理。稍后再来查看更新。", "regenerate": "重新生成", "regenerating": "重新生成中", "reprocessFailed": "重新处理失败任务", "failedEmbeddingsDescription": "以下文章块生成嵌入向量失败。点击重新生成。", "errorLoadingArticles": "加载文章时出错", "home": "首页", "published": "发布于", "updated": "更新于", "wasThisHelpful": "这篇文章对您有帮助吗？", "thankYouForPositiveFeedback": "感谢您的反馈！我们很高兴这篇文章对您有帮助。", "thankYouForFeedback": "感谢您的反馈。我们将用它来改进我们的内容。", "additionalComments": "您想添加任何其他评论吗？", "howCanWeImprove": "我们如何改进这篇文章？", "feedbackPlaceholder": "在这里写下您的评论...", "relatedArticles": "相关文章", "articleNotFound": "文章未找到", "articleNotFoundDescription": "抱歉，您要查找的文章不存在或已被移动。", "backToKnowledgeBase": "返回知识库", "categoryNotFound": "分类未找到", "categoryNotFoundDescription": "抱歉，您要查找的分类不存在或已被移动。", "noCategoryArticles": "该分类中没有找到文章。", "in": "分类于", "relevance": "相关度", "askAIAssistant": "向AI助手提问", "askAIAssistantDescription": "就奶牛饲料配方提出任何问题，我们的AI将从知识库中找到最佳答案。", "askPlaceholder": "提出问题...", "ask": "提问", "thinkingAboutQuestion": "正在思考您的问题...", "sources": "信息来源", "hideSources": "隐藏来源", "showSources": "显示来源", "minContext": "最小上下文 (1)", "defaultContext": "默认上下文 (3)", "maxContext": "最大上下文 (5)", "contextSizeExplanation": "上下文大小决定AI将使用多少文章来回答您的问题。", "askAnything": "关于奶牛饲料配方的任何问题", "askAnythingDescription": "我们的AI助手将搜索我们的知识库，为您的问题找到最佳答案。", "errorProcessingQuestion": "处理您的问题时出错。请重试。", "enterSearchTerms": "输入搜索词", "searchDescription": "搜索我们的知识库文章、指南和最佳实践。", "tryThese": "尝试这些搜索", "startSearchTitle": "搜索知识库", "startSearchHint": "在上方输入搜索词以查找知识库中的文章。", "search": "搜索", "searchError": "搜索时发生错误。请重试。", "tryDifferentSearch": "尝试使用不同的搜索词或浏览我们的分类。", "searchMode": "搜索模式", "semanticSearch": "语义搜索", "keywordSearch": "关键词搜索", "semanticSearchDescription": "即使没有完全匹配的词语，也能找到概念上相似的内容", "keywordSearchDescription": "在内容中查找精确的词语匹配", "semanticSearchInfo": "关于语义搜索", "semanticSearchDetail": "结果按与您的查询的相关性排序，即使它们不包含您搜索的确切词语。", "searchInstead": "改为搜索", "manageArticles": "管理文章", "searchArticles": "搜索文章...", "allArticles": "所有文章", "publishedOnly": "仅已发布", "draftsOnly": "仅草稿", "noArticlesFound": "未找到符合条件的文章。", "askQuestion": "有问题？", "askQuestionDescription": "使用我们的AI助手提问并从知识库中获取答案。", "askNow": "立即提问", "presetQuestion1": "如何制定奶牛饲料配方？", "presetQuestion2": "饲料添加剂的主要成分是什么？", "presetQuestion3": "如何提高牛奶产量？"}, "excelImportReview": {"title": "审查待处理导入", "titleWithType": "审查待处理导入：{{recordType}}", "selectTypePlaceholder": "选择类型...", "sessionIdLabel": "会话 ID：", "status": {"confirmSuccess": "{{recordType}} 的导入已成功确认！", "confirmError": "确认 {{recordType}} 时出错：{{errorMessage}}", "discardSuccess": "{{recordType}} 的导入已成功丢弃！", "discardError": "丢弃 {{recordType}} 时出错：{{errorMessage}}", "processingConfirm": "正在处理 {{recordType}} 的确认...", "processingDiscard": "正在处理 {{recordType}} 的丢弃...", "loadingTypesError": "加载会话类型时出错：{{errorMessage}}", "loadingDataError": "加载 {{recordType}} 数据时出错：{{errorMessage}}", "noTypesFoundForSession": "此会话未找到待处理的数据类型。", "processedSuccess": "'{{recordType}}' 的导入已处理。", "previousErrorRetryHint": "之前发生了错误。您可以重试该操作。"}, "loading": {"sessionTitle": "正在加载会话...", "loadingTypes": "正在加载此导入会话的可用数据类型...", "loadingDataPage": "正在加载数据页面..."}, "errors": {"unknownError": "未知错误", "unknownErrorLoadingPending": "加载待处理数据时发生未知错误。", "noDataFoundTitle": "未找到数据", "noDataFoundText": "未找到此导入会话的待处理数据类型，或加载时出错。", "invalidTypeTitle": "审查待处理导入：类型无效", "invalidTypeCardTitle": "记录类型无效", "invalidTypeText": "URL 中指定的记录类型 '{{recordType}}' 在此导入会话中不可用。", "availableTypesLabel": "可用类型：", "noPendingRecords": "此会话未找到待处理的 '{{recordType}}' 记录。", "failedToLoadData": "未能加载 {{recordType}} 的数据。"}, "actions": {"backToDashboard": "返回仪表板", "retry": "重试", "selectDataTypeTitle": "选择要审查的数据类型", "confirmImport": "确认导入", "confirming": "正在确认...", "discardImport": "丢弃导入", "discarding": "正在丢弃...", "commitModeLabel": "选择确认模式：", "commitModeReplace": "替换", "commitModeReplaceWarning": "（警告：这将首先删除您账户下所有已确认的 '{{recordType}}' 记录！）", "title": "{{recordType}} 的操作"}, "preview": {"title": "数据预览：{{recordType}}（{{count}} 条记录待处理）"}, "prompts": {"confirmImport": "确认使用 '{{commitMode}}' 模式导入 {{recordType}} （会话：{{sessionId}}）？", "confirmReplaceWarning": "\n\n警告：这将首先删除您账户下所有已确认的 '{{recordType}}' 记录！", "discardImport": "丢弃 {{recordType}} 的待处理导入（会话：{{sessionId}}）？此操作无法撤销。"}}}