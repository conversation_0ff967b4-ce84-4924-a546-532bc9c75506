import json
import re

def sanitize_json_response(text):
    """
    Attempt to fix common JSON issues in LLM responses:
    1. Replace single quotes with double quotes
    2. Escape unescaped double quotes within string values
    3. <PERSON><PERSON> trailing commas
    4. Extract JSON from markdown code blocks
    """
    # Extract JSON from markdown code blocks if present
    if "```json" in text:
        match = re.search(r'```json\s*([\s\S]*?)\s*```', text)
        if match:
            text = match.group(1)
    elif "```" in text:
        match = re.search(r'```\s*([\s\S]*?)\s*```', text)
        if match:
            text = match.group(1)
    
    # Replace any instances where properties have single quotes
    text = re.sub(r"'([^']*)'(\s*:)", r'"\1"\2', text)
    
    # Replace trailing commas in objects
    text = re.sub(r',\s*}', '}', text)
    
    # Replace trailing commas in arrays
    text = re.sub(r',\s*]', ']', text)
    
    try:
        # Try to parse the JSON to see if our fixes worked
        parsed = json.loads(text)
        return parsed
    except json.JSONDecodeError as e:
        # If still failing, try more aggressive normalization
        try:
            # Try to parse using Python's literal_eval which is more forgiving
            import ast
            text_dict = ast.literal_eval(text)
            return json.loads(json.dumps(text_dict))
        except:
            # Return diagnostic info
            return {
                "error": f"Failed to parse JSON: {str(e)}",
                "headers": ["Error"],
                "types": ["string"],
                "rows": [[f"JSON parsing error: {str(e)}"]]
            }

def apply_json_response_handler(llm_service):
    """
    Patch the analyze_excel_data and update_structured_data functions
    to apply JSON sanitization to responses
    """
    original_analyze = llm_service.analyze_excel_data
    original_update = llm_service.update_structured_data
    
    def analyze_excel_data_with_fix(*args, **kwargs):
        result = original_analyze(*args, **kwargs)
        if kwargs.get('task') == 'structure' and isinstance(result, str):
            # If we got a string back instead of parsed JSON
            return sanitize_json_response(result)
        return result
    
    def update_structured_data_with_fix(*args, **kwargs):
        result = original_update(*args, **kwargs)
        if isinstance(result, str):
            # If we got a string back instead of parsed JSON
            return sanitize_json_response(result)
        return result
    
    # Apply the patches
    llm_service.analyze_excel_data = analyze_excel_data_with_fix
    llm_service.update_structured_data = update_structured_data_with_fix
    
    return llm_service