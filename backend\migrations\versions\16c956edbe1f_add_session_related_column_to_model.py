"""Add session related column to model

Revision ID: 16c956edbe1f
Revises: 8271b065814f
Create Date: 2025-04-09 11:16:28.556093

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '16c956edbe1f'
down_revision = '8271b065814f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint('animal_groups_herd_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'herds', ['herd_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint('constraints_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint('feed_nutrients_nutrient_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('feed_nutrients_feed_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'nutrients', ['nutrient_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.add_column(sa.Column('import_status', sa.String(length=50), nullable=False))
        batch_op.add_column(sa.Column('import_session_id', sa.String(length=36), nullable=True))
        batch_op.create_index(batch_op.f('ix_ration_app_feeds_import_session_id'), ['import_session_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_ration_app_feeds_import_status'), ['import_status'], unique=False)
        batch_op.drop_constraint('feeds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.add_column(sa.Column('import_status', sa.String(length=50), nullable=False))
        batch_op.add_column(sa.Column('import_session_id', sa.String(length=36), nullable=True))
        batch_op.create_index(batch_op.f('ix_ration_app_herds_import_session_id'), ['import_session_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_ration_app_herds_import_status'), ['import_status'], unique=False)
        batch_op.drop_constraint('herds_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint('ration_feeds_feed_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('ration_feeds_ration_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'feeds', ['feed_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'rations', ['ration_id'], ['id'], referent_schema='ration_app')

    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.add_column(sa.Column('import_status', sa.String(length=50), nullable=False))
        batch_op.add_column(sa.Column('import_session_id', sa.String(length=36), nullable=True))
        batch_op.create_index(batch_op.f('ix_ration_app_rations_import_session_id'), ['import_session_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_ration_app_rations_import_status'), ['import_status'], unique=False)
        batch_op.drop_constraint('rations_animal_group_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('rations_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='ration_app')
        batch_op.create_foreign_key(None, 'animal_groups', ['animal_group_id'], ['id'], referent_schema='ration_app')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('rations', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('rations_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('rations_animal_group_id_fkey', 'animal_groups', ['animal_group_id'], ['id'])
        batch_op.drop_index(batch_op.f('ix_ration_app_rations_import_status'))
        batch_op.drop_index(batch_op.f('ix_ration_app_rations_import_session_id'))
        batch_op.drop_column('import_session_id')
        batch_op.drop_column('import_status')

    with op.batch_alter_table('ration_feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('ration_feeds_ration_id_fkey', 'rations', ['ration_id'], ['id'])
        batch_op.create_foreign_key('ration_feeds_feed_id_fkey', 'feeds', ['feed_id'], ['id'])

    with op.batch_alter_table('herds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('herds_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.drop_index(batch_op.f('ix_ration_app_herds_import_status'))
        batch_op.drop_index(batch_op.f('ix_ration_app_herds_import_session_id'))
        batch_op.drop_column('import_session_id')
        batch_op.drop_column('import_status')

    with op.batch_alter_table('feeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feeds_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.drop_index(batch_op.f('ix_ration_app_feeds_import_status'))
        batch_op.drop_index(batch_op.f('ix_ration_app_feeds_import_session_id'))
        batch_op.drop_column('import_session_id')
        batch_op.drop_column('import_status')

    with op.batch_alter_table('feed_nutrients', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('feed_nutrients_feed_id_fkey', 'feeds', ['feed_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('feed_nutrients_nutrient_id_fkey', 'nutrients', ['nutrient_id'], ['id'])

    with op.batch_alter_table('constraints', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('constraints_ration_id_fkey', 'rations', ['ration_id'], ['id'])

    with op.batch_alter_table('animal_groups', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('animal_groups_herd_id_fkey', 'herds', ['herd_id'], ['id'])

    # ### end Alembic commands ###
