import React from 'react';
import { useTranslation } from 'react-i18next';

const AIToggle = ({ enabled, onChange }) => {
  const { t } = useTranslation();
  
  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm font-medium text-gray-700">
        {t('rations.aiAssistance')}
      </span>
      
      <button
        type="button"
        className={`${
          enabled ? 'bg-green-600' : 'bg-gray-200'
        } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2`}
        role="switch"
        aria-checked={enabled}
        onClick={() => onChange(!enabled)}
      >
        <span
          aria-hidden="true"
          className={`${
            enabled ? 'translate-x-5' : 'translate-x-0'
          } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
        ></span>
      </button>
    </div>
  );
};

export default AIToggle;