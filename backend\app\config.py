# my_project/backend/app/config.py
import os
from datetime import timedelta
from pathlib import Path # Import Path

# Define BASE_DIR for backend if needed elsewhere, otherwise os.path.join works
# BASE_DIR = Path(__file__).resolve().parent.parent # Path to the 'app' directory

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key' # Keep existing

    # Database configuration - Keep existing
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Schema configuration for PostgreSQL - Updated to include extensions schema
    SQLALCHEMY_ENGINE_OPTIONS = {
        "connect_args": {"options": "-c search_path=ration_app,extensions,public"}
    } if os.environ.get('DATABASE_URL') and 'postgresql' in os.environ.get('DATABASE_URL') else {}

    # JWT configuration - Keep existing
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-super-secret' # Provide a default for dev
    # print(f"JWT_SECRET_KEY") # Avoid printing secrets in production logs
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_TOKEN_LOCATION = ["headers"]
    JWT_HEADER_NAME = "Authorization"
    JWT_HEADER_TYPE = "Bearer"

    # CORS configuration - Keep existing
    CORS_HEADERS = 'Content-Type'

    # --- LLM Configuration (from plugin_back/config.py) ---
    LLM_PROVIDER = os.environ.get('LLM_PROVIDER', 'gemini') # Default to gemini or choose openai/deepseek
    # --- API Keys (Read from environment) ---
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY', None)
    OPENAI_MODEL = os.environ.get('OPENAI_MODEL', None)
    OPENAI_API_ENDPOINT = os.environ.get('OPENAI_API_ENDPOINT', None)
    QWEN_API_KEY = os.environ.get('QWEN_API_KEY', None)
    GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY', None)
    DEEPSEEK_API_KEY = os.environ.get('DEEPSEEK_API_KEY', None)
    DEEPSEEK_API_BASE = os.environ.get('DEEPSEEK_API_BASE', 'https://api.deepseek.com')
    GEMINI_MODEL = os.environ.get('GEMINI_MODEL', 'models/gemini-1.5-flash-latest') # Use a standard Gemini model
    GEMINI_ANALYZE_MODEL = os.environ.get('GEMINI_ANALYZE_MODEL', 'models/gemini-1.5-flash-latest') # Use a standard Gemini model
    QWEN_MODEL = os.environ.get('QWEN_MODEL', None)

    # --- NEW: Embedding Configuration ---
    EMBEDDING_PROVIDER = os.environ.get('EMBEDDING_PROVIDER', 'openai')  # openai, google, huggingface
    EMBEDDING_MODEL = os.environ.get('EMBEDDING_MODEL', 'text-embedding-ada-002')  # OpenAI model name
    EMBEDDING_DIMENSIONS = int(os.environ.get('EMBEDDING_DIMENSIONS', '1024'))  # Match Vector(1024) in KBArticleChunk model
    TARGET_TOKEN_COUNT = int(os.environ.get('TARGET_TOKEN_COUNT', '512')) # Default for OpenAI ada-002, 512 is the max for text-embedding-ada-002
    TIKTOKEN_ENCODING_NAME = os.environ.get('TIKTOKEN_ENCODING_NAME', 'cl100k_base') # Default for OpenAI ada-002

    # Provider-specific embedding configurations
    OPENAI_EMBEDDING_API_KEY = os.environ.get('OPENAI_EMBEDDING_API_KEY', OPENAI_API_KEY)  # Default to general key
    GOOGLE_EMBEDDING_API_KEY = os.environ.get('GOOGLE_EMBEDDING_API_KEY', GEMINI_API_KEY)  # Default to general key
    HUGGINGFACE_EMBEDDING_MODEL = os.environ.get('HUGGINGFACE_EMBEDDING_MODEL', 'all-MiniLM-L6-v2')

    # --- NEW: Procrastinate Configuration for Async Tasks ---
    PROCRASTINATE_APP_NAME = os.environ.get('PROCRASTINATE_APP_NAME', 'ration_app')
    PROCRASTINATE_SCHEMA = os.environ.get('PROCRASTINATE_SCHEMA', 'ration_app')

    # Database connection for Procrastinate - uses same database as main app by default
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = int(os.environ.get('DB_PORT', 5432))
    DB_USER = os.environ.get('DB_USER', 'postgres')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', '')
    DB_NAME = os.environ.get('DB_NAME', 'postgres')

    # --- NEW: pgvector Configuration ---
    PGVECTOR_INDEX_TYPE = os.environ.get('PGVECTOR_INDEX_TYPE', 'hnsw')  # hnsw or ivfflat
    PGVECTOR_HNSW_M = int(os.environ.get('PGVECTOR_HNSW_M', 16))         # HNSW m parameter (neighbors per node)
    PGVECTOR_HNSW_EF_CONSTRUCTION = int(os.environ.get('PGVECTOR_HNSW_EF_CONSTRUCTION', 64))  # HNSW build quality
    PGVECTOR_HNSW_EF_SEARCH = int(os.environ.get('PGVECTOR_HNSW_EF_SEARCH', 40))  # HNSW query accuracy

    # --- Staging Directory (from plugin_back/config.py, adapted) ---
    # Using os.path.join for broader compatibility
    # BASE_DIR needs to point to the backend root directory (where .env is)
    BACKEND_BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    # Define SESSION_STORAGE_DIR relative to backend root
    SESSION_STORAGE_DIR = os.environ.get('SESSION_STORAGE_DIR', os.path.join(BACKEND_BASE_DIR, 'staging_sessions'))

    # --- End New Config ---

# --- Keep Existing Config Subclasses ---
class DevelopmentConfig(Config):
    DEBUG = True

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    # Add test keys if needed
    JWT_SECRET_KEY = 'test-jwt-secret'


class ProductionConfig(Config):
    DEBUG = False
    # Ensure DATABASE_URL, SECRET_KEY, JWT_SECRET_KEY, and LLM keys are set via environment variables in production

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}