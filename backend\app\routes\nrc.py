from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from ..models import NrcModelVersion, NrcCoefficientGroup, NrcCoefficient
from ..extensions import db
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('nrc', __name__, url_prefix='/api/nrc')

# ---- NRC Model Version Routes ----

@bp.route('/versions', methods=['GET'])
def get_model_versions():
    """Get all NRC model versions"""
    try:
        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        
        # Get model versions with pagination
        query = NrcModelVersion.query.order_by(NrcModelVersion.name)
        
        # Apply pagination
        pagination = query.paginate(page=page, per_page=limit, error_out=False)
        versions = pagination.items
        
        return jsonify({
            'versions': [version.to_dict() for version in versions],
            'page': page,
            'totalPages': pagination.pages,
            'totalItems': pagination.total
        }), 200
    except Exception as e:
        logger.error(f"Error getting NRC model versions: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/versions/<int:version_id>', methods=['GET'])
def get_model_version(version_id):
    """Get a specific NRC model version"""
    try:
        version = NrcModelVersion.query.get(version_id)
        if not version:
            return jsonify({'message': 'NRC model version not found'}), 404
            
        return jsonify({
            'version': version.to_dict()
        }), 200
    except Exception as e:
        logger.error(f"Error getting NRC model version {version_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/versions', methods=['POST'])
@jwt_required()
def create_model_version():
    """Create a new NRC model version (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Get request data
        data = request.get_json()
        
        # Validate required fields
        if not data or not data.get('name'):
            return jsonify({'message': 'Name is required'}), 400
            
        # Create new model version
        version = NrcModelVersion(
            name=data['name'],
            description=data.get('description'),
            publication_date=datetime.fromisoformat(data['publication_date']) if data.get('publication_date') else None,
            is_active=data.get('is_active', True)
        )
        
        db.session.add(version)
        db.session.commit()
        
        return jsonify({
            'message': 'NRC model version created successfully',
            'version': version.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating NRC model version: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/versions/<int:version_id>', methods=['PUT'])
@jwt_required()
def update_model_version(version_id):
    """Update an NRC model version (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Get request data
        data = request.get_json()
        
        # Get the model version
        version = NrcModelVersion.query.get(version_id)
        if not version:
            return jsonify({'message': 'NRC model version not found'}), 404
            
        # Update fields
        if 'name' in data:
            version.name = data['name']
        if 'description' in data:
            version.description = data['description']
        if 'publication_date' in data:
            version.publication_date = datetime.fromisoformat(data['publication_date']) if data['publication_date'] else None
        if 'is_active' in data:
            version.is_active = data['is_active']
            
        db.session.commit()
        
        return jsonify({
            'message': 'NRC model version updated successfully',
            'version': version.to_dict()
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating NRC model version {version_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/versions/<int:version_id>', methods=['DELETE'])
@jwt_required()
def delete_model_version(version_id):
    """Delete an NRC model version (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Get the model version
        version = NrcModelVersion.query.get(version_id)
        if not version:
            return jsonify({'message': 'NRC model version not found'}), 404
            
        # Delete the model version (cascade will delete related coefficient groups and coefficients)
        db.session.delete(version)
        db.session.commit()
        
        return jsonify({
            'message': 'NRC model version deleted successfully'
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting NRC model version {version_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

# ---- Coefficient Group Routes ----

@bp.route('/versions/<int:version_id>/groups', methods=['GET'])
def get_coefficient_groups(version_id):
    """Get all coefficient groups for a model version"""
    try:
        # Check if model version exists
        version = NrcModelVersion.query.get(version_id)
        if not version:
            return jsonify({'message': 'NRC model version not found'}), 404
            
        # Get coefficient groups
        groups = NrcCoefficientGroup.query.filter_by(model_version_id=version_id).all()
        
        return jsonify({
            'groups': [group.to_dict() for group in groups]
        }), 200
    except Exception as e:
        logger.error(f"Error getting coefficient groups for version {version_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/groups/<int:group_id>', methods=['GET'])
def get_coefficient_group(group_id):
    """Get a specific coefficient group"""
    try:
        group = NrcCoefficientGroup.query.get(group_id)
        if not group:
            return jsonify({'message': 'Coefficient group not found'}), 404
            
        return jsonify({
            'group': group.to_dict()
        }), 200
    except Exception as e:
        logger.error(f"Error getting coefficient group {group_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/versions/<int:version_id>/groups', methods=['POST'])
@jwt_required()
def create_coefficient_group(version_id):
    """Create a new coefficient group (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Check if model version exists
        version = NrcModelVersion.query.get(version_id)
        if not version:
            return jsonify({'message': 'NRC model version not found'}), 404
            
        # Get request data
        data = request.get_json()
        
        # Validate required fields
        if not data or not data.get('name'):
            return jsonify({'message': 'Name is required'}), 400
            
        # Create new coefficient group
        group = NrcCoefficientGroup(
            model_version_id=version_id,
            name=data['name'],
            description=data.get('description')
        )
        
        db.session.add(group)
        db.session.commit()
        
        return jsonify({
            'message': 'Coefficient group created successfully',
            'group': group.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating coefficient group: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/groups/<int:group_id>', methods=['PUT'])
@jwt_required()
def update_coefficient_group(group_id):
    """Update a coefficient group (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Get request data
        data = request.get_json()
        
        # Get the coefficient group
        group = NrcCoefficientGroup.query.get(group_id)
        if not group:
            return jsonify({'message': 'Coefficient group not found'}), 404
            
        # Update fields
        if 'name' in data:
            group.name = data['name']
        if 'description' in data:
            group.description = data['description']
            
        db.session.commit()
        
        return jsonify({
            'message': 'Coefficient group updated successfully',
            'group': group.to_dict()
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating coefficient group {group_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/groups/<int:group_id>', methods=['DELETE'])
@jwt_required()
def delete_coefficient_group(group_id):
    """Delete a coefficient group (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Get the coefficient group
        group = NrcCoefficientGroup.query.get(group_id)
        if not group:
            return jsonify({'message': 'Coefficient group not found'}), 404
            
        # Delete the coefficient group (cascade will delete related coefficients)
        db.session.delete(group)
        db.session.commit()
        
        return jsonify({
            'message': 'Coefficient group deleted successfully'
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting coefficient group {group_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

# ---- Coefficient Routes ----

@bp.route('/groups/<int:group_id>/coefficients', methods=['GET'])
def get_coefficients(group_id):
    """Get all coefficients for a group"""
    try:
        # Check if coefficient group exists
        group = NrcCoefficientGroup.query.get(group_id)
        if not group:
            return jsonify({'message': 'Coefficient group not found'}), 404
            
        # Get coefficients
        coefficients = NrcCoefficient.query.filter_by(group_id=group_id).all()
        
        return jsonify({
            'coefficients': [coef.to_dict() for coef in coefficients]
        }), 200
    except Exception as e:
        logger.error(f"Error getting coefficients for group {group_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/coefficients/<int:coefficient_id>', methods=['GET'])
def get_coefficient(coefficient_id):
    """Get a specific coefficient"""
    try:
        coefficient = NrcCoefficient.query.get(coefficient_id)
        if not coefficient:
            return jsonify({'message': 'Coefficient not found'}), 404
            
        return jsonify({
            'coefficient': coefficient.to_dict()
        }), 200
    except Exception as e:
        logger.error(f"Error getting coefficient {coefficient_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/groups/<int:group_id>/coefficients', methods=['POST'])
@jwt_required()
def create_coefficient(group_id):
    """Create a new coefficient (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Check if coefficient group exists
        group = NrcCoefficientGroup.query.get(group_id)
        if not group:
            return jsonify({'message': 'Coefficient group not found'}), 404
            
        # Get request data
        data = request.get_json()
        
        # Validate required fields
        if not data or not data.get('name') or not data.get('code') or 'value' not in data:
            return jsonify({'message': 'Name, code, and value are required'}), 400
            
        # Create new coefficient
        coefficient = NrcCoefficient(
            group_id=group_id,
            name=data['name'],
            code=data['code'],
            value=data['value'],
            unit=data.get('unit'),
            description=data.get('description'),
            animal_type=data.get('animal_type')
        )
        
        db.session.add(coefficient)
        db.session.commit()
        
        return jsonify({
            'message': 'Coefficient created successfully',
            'coefficient': coefficient.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating coefficient: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/coefficients/<int:coefficient_id>', methods=['PUT'])
@jwt_required()
def update_coefficient(coefficient_id):
    """Update a coefficient (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Get request data
        data = request.get_json()
        
        # Get the coefficient
        coefficient = NrcCoefficient.query.get(coefficient_id)
        if not coefficient:
            return jsonify({'message': 'Coefficient not found'}), 404
            
        # Update fields
        if 'name' in data:
            coefficient.name = data['name']
        if 'code' in data:
            coefficient.code = data['code']
        if 'value' in data:
            coefficient.value = data['value']
        if 'unit' in data:
            coefficient.unit = data['unit']
        if 'description' in data:
            coefficient.description = data['description']
        if 'animal_type' in data:
            coefficient.animal_type = data['animal_type']
            
        db.session.commit()
        
        return jsonify({
            'message': 'Coefficient updated successfully',
            'coefficient': coefficient.to_dict()
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating coefficient {coefficient_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

@bp.route('/coefficients/<int:coefficient_id>', methods=['DELETE'])
@jwt_required()
def delete_coefficient(coefficient_id):
    """Delete a coefficient (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Get the coefficient
        coefficient = NrcCoefficient.query.get(coefficient_id)
        if not coefficient:
            return jsonify({'message': 'Coefficient not found'}), 404
            
        # Delete the coefficient
        db.session.delete(coefficient)
        db.session.commit()
        
        return jsonify({
            'message': 'Coefficient deleted successfully'
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting coefficient {coefficient_id}: {str(e)}")
        return jsonify({'message': str(e)}), 500

# ---- Bulk Operations ----

@bp.route('/versions/<int:version_id>/bulk-import', methods=['POST'])
@jwt_required()
def bulk_import_coefficients(version_id):
    """Bulk import coefficients for a model version (admin only)"""
    try:
        # Check if user is admin (you may need to implement this check)
        current_user_id = get_jwt_identity()
        
        # Check if model version exists
        version = NrcModelVersion.query.get(version_id)
        if not version:
            return jsonify({'message': 'NRC model version not found'}), 404
            
        # Get request data
        data = request.get_json()
        
        if not data or not isinstance(data.get('groups'), list):
            return jsonify({'message': 'Invalid data format. Expected a list of coefficient groups'}), 400
            
        # Start a transaction
        for group_data in data['groups']:
            # Create or update the coefficient group
            group_name = group_data.get('name')
            if not group_name:
                continue
                
            group = NrcCoefficientGroup.query.filter_by(
                model_version_id=version_id, 
                name=group_name
            ).first()
            
            if not group:
                group = NrcCoefficientGroup(
                    model_version_id=version_id,
                    name=group_name,
                    description=group_data.get('description')
                )
                db.session.add(group)
                db.session.flush()  # Get the group ID
                
            # Process coefficients for this group
            if isinstance(group_data.get('coefficients'), list):
                for coef_data in group_data['coefficients']:
                    coef_code = coef_data.get('code')
                    if not coef_code or 'value' not in coef_data:
                        continue
                        
                    # Check if coefficient already exists
                    coef = NrcCoefficient.query.filter_by(
                        group_id=group.id,
                        code=coef_code
                    ).first()
                    
                    if coef:
                        # Update existing coefficient
                        coef.name = coef_data.get('name', coef.name)
                        coef.value = coef_data['value']
                        coef.unit = coef_data.get('unit', coef.unit)
                        coef.description = coef_data.get('description', coef.description)
                        coef.animal_type = coef_data.get('animal_type', coef.animal_type)
                    else:
                        # Create new coefficient
                        coef = NrcCoefficient(
                            group_id=group.id,
                            name=coef_data.get('name', coef_code),
                            code=coef_code,
                            value=coef_data['value'],
                            unit=coef_data.get('unit'),
                            description=coef_data.get('description'),
                            animal_type=coef_data.get('animal_type')
                        )
                        db.session.add(coef)
        
        db.session.commit()
        
        return jsonify({
            'message': 'Coefficients imported successfully'
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error bulk importing coefficients: {str(e)}")
        return jsonify({'message': str(e)}), 500
